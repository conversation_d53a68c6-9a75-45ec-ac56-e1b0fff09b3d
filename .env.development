# 资源公共路径,需要以 /开头和结尾
VITE_PUBLIC_PATH = /


# 本地开发代理，可以解决跨域及多地址代理
# 如果接口地址匹配到，则会转发到http://localhost:30000，防止本地出现跨域问题
# 可以有多个，注意多个不能换行，否则代理将会失效
VITE_PROXY = [["/dev","http://www.fjbdex.com"]]
# VITE_PROXY = [["/dev","http://************:9088"]]
#VITE_PROXY = [["/dev","http://127.0.0.1:8080"]]
# 是否删除Console.log
VITE_DROP_CONSOLE = true

# 是否删除Console.log
VITE_DROP_CONSOLE = false

# 接口地址
# 如果没有跨域问题，直接在这里配置即可
VITE_GLOB_API_URL=/dev

# WebSocket基础地址
VITE_GLOB_WEBSOCKET_URL='ws://localhost:30000'


# 接口地址前缀，有些系统所有接口地址都有前缀，可以在这里统一加，方便切换
VITE_GLOB_API_URL_PREFIX=
