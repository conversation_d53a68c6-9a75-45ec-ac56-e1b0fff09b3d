/*
 * @Author: cf
 * @Date: 2023-07-28 10:08:23
 * @LastEditTime: 2023-08-08 09:24:32
 * @LastEditors: cf
 * @Description: cxbim
 */
import { generateAntColors } from "../config/themeConfig";
import theme from "naive-ui";
import { resolve } from "path";

/**
 * less global variable
 */
export function generateModifyVars(dark = false) {
  const { defaultAlgorithm, defaultSeed, darkAlgorithm } = theme;

  const modifyVars = dark
    ? darkAlgorithm(defaultSeed)
    : defaultAlgorithm(defaultSeed);
  modifyVars.colorPrimary = modifyVars.colorPrimary;
  // const { theme: theme1, token: token1 } = useToken;
  // console.log('theme1', theme1);
  // console.log('token1', token1);
  const palettes = generateAntColors(modifyVars.colorPrimary);
  // const primary = palettes[5];

  const primaryColorObj: Record<string, string> = {};

  for (let index = 0; index < 10; index++) {
    primaryColorObj[`primary-${index + 1}`] = palettes[index];
  }

  return {
    ...modifyVars,
    // Used for global import to avoid the need to import each style file separately
    // reference:  Avoid repeated references
    hack: `true;@import (reference) "${resolve("src/design/config.less")}";`,
    // 'colorPrimary': primary,
    ...primaryColorObj,
    "app-content-background": "#FAFCFF", //   Link color
    "background-color-light": "#FAFCFF",
  };
}
