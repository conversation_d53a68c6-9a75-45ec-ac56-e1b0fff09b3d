/*
 * @Author: cf
 * @Date: 2023-07-28 10:08:23
 * @LastEditTime: 2023-08-08 10:18:09
 * @LastEditors: cf
 * @Description: cxbim
 */
/**
 * Vite plugin for website theme color switching
 * https://github.com/anncwb/@kirklin/vite-plugin-vben-theme
 */
import type { PluginOption } from 'vite';
import path from 'path';
import {
  viteThemePlugin,
  antdDarkThemePlugin,
  mixLighten,
  mixDarken,
  tinycolor,
} from '@kirklin/vite-plugin-vben-theme';
import { getThemeColors, generateColors } from '../../config/themeConfig';
import { generateModifyVars } from '../../generate/generateModifyVars';

export function configThemePlugin(isBuild: boolean): PluginOption[] {
  const colors = generateColors({
    mixDarken,
    mixLighten,
    tinycolor,
  });
  const plugin = [
    viteThemePlugin({
      resolveSelector: (s) => {
        s = s.trim();
        switch (s) {
          case '.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon':
            return '.ant-steps-item-icon > .ant-steps-icon';
          case '.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)':
          case '.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover':
          case '.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active':
            return s;
          case '.ant-steps-item-icon > .ant-steps-icon':
            return s;
          case '.ant-select-item-option-selected:not(.ant-select-item-option-disabled)':
            return s;
          default:
            if (s.indexOf('.ant-btn') >= -1) {
              // 按钮被重新定制过，需要过滤掉class防止覆盖
              return s;
            }
        }
        return s.startsWith('[data-theme') ? s : `[data-theme] ${s}`;
      },
      colorVariables: [...getThemeColors(), ...colors],
    }),
    antdDarkThemePlugin({
      preloadFiles: [
        // path.resolve(process.cwd(), 'node_modules/ant-design-vue/dist/antd.less'),
        // path.resolve(process.cwd(), 'node_modules/ant-design-vue/dist/index.less'),
        //path.resolve(process.cwd(), 'node_modules/ant-design-vue/dist/antd.dark.less'),
        path.resolve(process.cwd(), 'src/design/index.less'),
      ],
      filter: (id) => (isBuild ? !id.endsWith('antd.less') : true),
      // extractCss: false,
      darkModifyVars: {
        ...generateModifyVars(true),
        // colorPrimary: '#C2CBE1',
        // colorTextSecondary: '#C2CBE1',
        // colorText: '#C2CBE1',
        // 'text-color': '#C2CBE1', // 主文本色
        // 'primary-1': '#104181 ',
        // 'text-color-base': '#8791AB',
        // 'component-background': '#082F61', //组件背景色

        // 'border-color-split': '#30363d',
        // 'item-active-bg': '#111b26',
        // 'app-content-background': '#04244D',
        // 'tree-node-selected-bg': '#11263c',

        // 'alert-success-border-color': '#274916',
        // 'alert-success-bg-color': '#162312',
        // 'alert-success-icon-color': '#49aa19',
        // 'alert-info-border-color': '#153450',
        // 'alert-info-bg-color': '#111b26',
        // 'alert-info-icon-color': '#177ddc',
        // 'alert-warning-border-color': '#594214',
        // 'alert-warning-bg-color': '#2b2111',
        // 'alert-warning-icon-color': '#d89614',
        // 'alert-error-border-color': '#58181c',
        // 'alert-error-bg-color': '#2a1215',
        // 'alert-error-icon-color': '#a61d24',
      },
    }),
  ];

  return plugin as unknown as PluginOption[];
}
