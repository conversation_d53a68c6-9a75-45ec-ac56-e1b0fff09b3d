{"success": 1, "errMsg": "ok", "data": [{"precinct_id": 4, "precinct_name": "气象数据专区", "precinct_seq": 1, "service_list": [{"service_id": 2075, "name": "全球数值天气预报产品数据库", "descript": "用于山东省煤田地质局第一勘探队编制《山东省无棣县碣石山镇旅游地质调查评价》报告", "cover": "/apaas/static/docs/image/sdk/8c5148ff-a3cb-4d43-af64-a57bba743732/产品封面.png", "url": "/market/detail/2075", "apply_count": 62, "up_time": "2024-04-29 11:48:00"}, {"service_id": 1360, "name": "云上贵州人员信息管理系统", "descript": "建设统一用户体系的基础数据，实现高效办理员工入转调离手续，提升人事管理效率，全局展现员工职业周期。", "cover": "/apaas/static/docs/image/image/blob_ac88c86e-994e-4132-86bc-79bb604c263f.blob", "url": "/market/detail/1360", "apply_count": 47, "up_time": "2022-05-06 18:39:49"}, {"service_id": 1347, "name": "贵阳市倾斜摄影数据采集", "descript": "采用无人机或有人驾驶固定翼飞机进行倾斜摄影数据采集；按照1:500DOM精度要求进行像控点布设和数据采集；作业区域位于贵阳市，具体以指定的范围和实际完成工作量为准；", "cover": "/apaas/static/docs/image/image/blob_4cb100da-2b7a-442a-9d53-51df8712ba56.blob", "url": "/market/detail/1347", "apply_count": 31, "up_time": "2023-06-07 09:37:07"}, {"service_id": 1172, "name": "测试09291724", "descript": "测试09291724测试09291724测试09291724测试09291724测试09291724测试09291724测试09291724测试09291724测试09291724测试09291724测试09291724", "cover": "/apaas/static/docs/image/image/blob_8eb9a4ca-50e3-47cf-84d9-2ef573fde863.blob", "url": "/market/detail/1172", "apply_count": 25, "up_time": "2021-09-29 17:26:44"}, {"service_id": 1192, "name": "测试离线数据包", "descript": "测试离线数据包测试离线数据包测试离线数据包测试离线数据包", "cover": "/apaas/static/docs/image/image/blob_278e1195-7b35-461d-bdf4-328457175764.blob", "url": "/market/detail/1192", "apply_count": 23, "up_time": "2021-11-10 19:00:19"}, {"service_id": 1891, "name": "接口类-后付费产品0221", "descript": "接口类-后付费产品0221接口类-后付费产品0221接口类-后付费产品0221接口类-后付费产品0221", "cover": "/apaas/static/docs/image/image/1676965428834_5ca70bfb-fe5e-4874-8db2-9384eb5e2555.png", "url": "/market/detail/1891", "apply_count": 15, "up_time": "2023-02-21 15:58:45"}, {"service_id": 1169, "name": "ceshi001", "descript": "ceshi001ceshi001ceshi001ceshi001ceshi001ceshi001", "cover": "/apaas/static/docs/image/image/blob_043886b1-566e-4dbe-a8b5-84391ae61a04.blob", "url": "/market/detail/1169", "apply_count": 15, "up_time": "2021-09-27 15:37:08"}, {"service_id": 1237, "name": "测试12281720", "descript": "测试12281720", "cover": "/apaas/static/docs/image/image/blob_cb97d697-6202-4d03-b174-4b3a039986f0.blob", "url": "/market/detail/1237", "apply_count": 10, "up_time": "2021-12-28 17:23:21"}, {"service_id": 2002, "name": "125ggggg", "descript": "125ggggg", "cover": "/apaas/static/docs/image/image/1706148330460_d770b5e1-cd33-455c-a616-1c41777420e5.png", "url": "/market/detail/2002", "apply_count": 9, "up_time": "2024-01-25 10:06:57"}, {"service_id": 1877, "name": "联调测试001-测试更新", "descript": "1101更新联调测试1101更新联调测试1101更新联调测试1101更新联调测试", "cover": "/apaas/static/docs/image/image/1669787113421_c05f0153-fdf2-4db6-a226-f6a43411351c.png", "url": "/market/detail/1877", "apply_count": 5, "up_time": "2022-11-30 13:48:26"}, {"service_id": 1944, "name": "0605北京天气服务21", "descript": "0605北京天气服务210605北京天气服务210605北京天气服务210605北京天气服务210605北京天气服务21", "cover": "/apaas/static/docs/image/image/1685959306849_d04a3be3-2ad7-467d-99eb-af6fe74c1cee.png", "url": "/market/detail/1944", "apply_count": 4, "up_time": "2023-07-26 15:29:52"}, {"service_id": 1381, "name": "云上社保", "descript": "为深入贯彻数字化改革要求，贵阳市人力资源和社会保障局先行先试，大胆实践，以数字化改革创新服务，力推\"云上见面\"办理社保业务，全力打造\"社保云厅\"智办服务平台，将社保综合窗口从\"线下\"移动到\"云上\"，实现社保业务办理", "cover": "/apaas/static/docs/image/image/blob_7c8fb8d4-afd9-4cd7-a64b-3e00215ae26a.blob", "url": "/market/detail/1381", "apply_count": 4, "up_time": "2022-11-21 17:36:40"}, {"service_id": 1947, "name": "测试要素凭证用益凭证发行产品", "descript": "测试要素凭证用益凭证发行产品测试要素凭证用益凭证发行产品测试要素凭证用益凭证发行产品", "cover": "/apaas/static/docs/image/image/1688010700210_17b75177-469f-4171-83c1-e979912a66c4.png", "url": "/market/detail/1947", "apply_count": 3, "up_time": "2023-06-29 11:55:50"}, {"service_id": 1881, "name": "API测试推送", "descript": "测试推送api测试推送api测试推送api测试推送api测试推送api测试推送api", "cover": "/apaas/static/docs/image/image/1670981945240_0a65daa6-d245-4c28-bb41-9ff13390f689.png", "url": "/market/detail/1881", "apply_count": 3, "up_time": "2022-12-16 16:04:55"}, {"service_id": 1308, "name": "后计费-月-141645", "descript": "后计费-月-141645", "cover": "/apaas/static/docs/image/image/blob_5730ffbb-90fd-4130-ba15-3b65d3f9a24a.blob", "url": "/market/detail/1308", "apply_count": 3, "up_time": "2022-02-14 16:45:48"}, {"service_id": 1959, "name": "测试数据加密", "descript": "测试数据加密测试数据加密测试数据加密测试数据加密测试数据加密测试数据加密测试数据加密", "cover": "/apaas/static/docs/image/image/1695367136061_e99b886d-72c7-48bf-9da9-1bbb72a96a8c.png", "url": "/market/detail/1959", "apply_count": 1, "up_time": "2023-09-22 15:23:09"}, {"service_id": 1925, "name": "贵阳市个人社保缴费明细查询", "descript": "贵阳市个人社保缴费明细查询测试121贵阳市个人社保缴费明细查询测试121贵阳市个人社保缴费明细查询测试121", "cover": "/apaas/static/docs/image/image/1679291060952_e7568302-6d11-4541-9a53-1188b587f1dc.png", "url": "/market/detail/1925", "apply_count": 1, "up_time": "2023-03-20 13:49:22"}, {"service_id": 1404, "name": "产品发布测试1004", "descript": "这里也是测试产品发布测试1004产品发布测试1004产品发布测试1004产品发布测试1004我加了一个测试这里也是测试这里也是测试这里也是测试这里也是测试这里也是测试这里也是测试这里也是测试这里也是测试这里也是测试这里也", "cover": "/apaas/static/docs/image/image/blob_fd7d6485-67b1-4f95-94cc-aae15e340edb.blob", "url": "/market/detail/1404", "apply_count": 1, "up_time": "2022-11-01 20:59:20"}, {"service_id": 1413, "name": "流程节点调整测试", "descript": "流程节点调整测试流程节点调整测试流程节点调整测试流程节点调整测试流程节点调整测试流程节点调整测试", "cover": "/apaas/static/docs/image/image/blob_be461694-6e49-45d5-888e-f61faa0b1e27.blob", "url": "/market/detail/1413", "apply_count": 1, "up_time": "2022-09-15 11:36:11"}, {"service_id": 1206, "name": "测试其他定价", "descript": "测试其他定价", "cover": "/apaas/static/docs/image/image/blob_1f59e0a9-b6ae-4624-957b-0aa19361e035.blob", "url": "/market/detail/1206", "apply_count": 1, "up_time": "2021-12-03 15:56:56"}, {"service_id": 1847, "name": "离线数据包-1124", "descript": "基于国家部委、央企权威、合法、多源的数据资源，针对企业360°身份鉴定推出的一款大数据画像产品，可灵活应用于金融、保险、物流、汽车、互联网、电子商务、生活服务等行业的企业画像。", "cover": "/apaas/static/docs/image/image/blob_77cde59f-74c8-40be-9834-82da9b876127.blob", "url": "/market/detail/1847", "apply_count": 0, "up_time": "2023-02-25 11:46:38"}, {"service_id": 1402, "name": "算法交换测试02", "descript": "算法交换测试02算法交换测试02算法交换测试02算法交换测试02算法交换测试02", "cover": "/apaas/static/docs/image/image/blob_0339d7a5-6a82-4733-8e76-530f79ff92a2.blob", "url": "/market/detail/1402", "apply_count": 0, "up_time": "2022-08-17 15:26:00"}, {"service_id": 1187, "name": "RDS数据库", "descript": "RDS数据库", "cover": "/apaas/static/docs/image/image/blob_3905601a-00ad-402b-887c-e2ac72ba5e98.blob", "url": "/market/detail/1187", "apply_count": 0, "up_time": "2021-11-10 16:19:40"}, {"service_id": 1186, "name": "4-16ECS服务器", "descript": "4-16ECS服务器", "cover": "/apaas/static/docs/image/image/blob_990258bb-f976-45a4-a3e1-296dc0f72b6a.blob", "url": "/market/detail/1186", "apply_count": 0, "up_time": "2021-11-10 16:05:20"}]}, {"precinct_id": 5, "precinct_name": "交通数据专区", "precinct_seq": 1, "service_list": []}]}