{"name": "dsjgw", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"axios": "^1.6.8", "chalk": "^5.4.1", "dayjs": "^1.11.10", "dotenv": "^16.4.5", "less": "^4.2.0", "lodash-es": "^4.17.21", "particles.vue3": "1.43.1", "pinia": "^2.1.7", "qs": "^6.12.0", "swiper": "^11.1.0", "video.js": "^8.12.0", "vue": "^3.4.15", "vue-axios": "^3.5.2", "vue-countup-v3": "^1.4.1", "vue-router": "^4.2.5", "vue-tsc": "^2.0.6", "vue3-scroll-seamless": "^1.0.6", "vue3-seamless-scroll": "^2.0.1"}, "devDependencies": {"@tsconfig/node20": "^20.1.2", "@types/fs-extra": "^11.0.1", "@types/node": "^20.11.10", "@types/qs": "^6.9.14", "@vicons/ionicons5": "^0.12.0", "@vitejs/plugin-legacy": "^5.3.2", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/tsconfig": "^0.5.1", "cross-env": "^7.0.3", "esno": "^0.16.3", "fs-extra": "11.1.1", "less-loader": "^12.2.0", "naive-ui": "^2.38.1", "npm-run-all2": "^6.1.1", "rollup-plugin-visualizer": "5.12.0", "typescript": "~5.3.0", "vfonts": "^0.0.3", "vite": "^5.0.11", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-pwa": "^0.19.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.9.3"}}