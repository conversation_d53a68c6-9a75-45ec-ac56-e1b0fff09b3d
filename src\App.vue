<template>
  <!-- 
        <RouterLink to="/">Home</RouterLink>
        <RouterLink to="/about">About</RouterLink> -->
  <Header />
  <RouterView />
  <Footer />
</template>

<script setup lang="ts">
import { unref, computed, reactive } from "vue";
import { RouterLink, RouterView } from "vue-router";
import FooterStyle2 from "/@/components/FooterStyle2.vue";
import { getDeviceWidth } from "/@/utils/box";
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();
const getPath = computed(() => userStore.getPath);
getDeviceWidth(window);
</script>

<style scoped></style>
