import { defHttp } from "/@/utils/http/axios";

enum Api {
  Prefix = "/manageParty",
}

// 获取党建头部
export function getHeader(data: any) {
  return defHttp.get({
    url: Api.Prefix + `/getManagePartyHeaderList.do`,
    data,
  });
}

// 获取党建底部
export function getList(data: any) {
  return defHttp.get({
    url: Api.Prefix + `/getManagePartyBottomList.do`,
    data,
  });
}

// 获取党建详情
export function getDetail(data: any) {
    return defHttp.get({
      url: Api.Prefix + `/getManagePartyDetail.do`,
      data,
    });
  }
