import { defHttp } from "/@/utils/http/axios";

enum Api {
  Prefix = "/homePage",
}

// 获取首页背景
export function getBg(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/carouselList.do`,
    data,
  });
}

// 获取标题配置
export function getConfig(data: any) {
  return defHttp.post({
    url: Api.Prefix + "/getModuleConfig.do",
    data,
  });
}

// 获取如何加入数商
export function getApplication(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/dataApplication.do`,
    data,
  });
}

// 获取最新活动
export function getActivities(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/getLatestActivities.do`,
    data,
  });
}

// 获取最新活动
export function getBrand(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/pageGetPartners.do`,
  });
}

// 获取合作数商
export function getCooper(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/cooperativeNumberQuotient.do`,
  });
}
// 获取合作数商
export function getAdvantage(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/getAdvantageList.do`,
    data,
  });
}