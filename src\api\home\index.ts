import { defHttp } from "/@/utils/http/axios";

enum Api {
  Prefix = "/homePage",
}

// 获取首页背景
export function getBg(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/carouselList.do`,
    data,
  });
}

// 获取创新应用
export function getApplication(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/innovateApplication.do`,
    data,
  });
}

// 获取政策制度
export function getPolicy() {
  return defHttp.post({
    url: Api.Prefix + `/policyGuidance.do`,
  });
}

// 获取数商配置
export function getNumerical() {
  return defHttp.post({
    url: Api.Prefix + `/numericalQuotient.do`,
  });
}

// 获取政策树列表配置
export function getPolicyTree() {
  return defHttp.post({
    url: Api.Prefix + `/policyGuidanceTree.do`,
  });
}

// 获取头部配置
export function getTopList() {
  return defHttp.post({
    url: Api.Prefix + "/getMenu.do",
  });
}

// 获取政策详情
export function getPolicyGuidanceDetail(data: any) {
  return defHttp.post({
    url: Api.Prefix + "/policyGuidanceDetail.do",
    data,
  });
}

// 获取标题配置
export function getConfig(data: any) {
  return defHttp.post({
    url: Api.Prefix + "/getModuleConfig.do",
    data,
  });
}

// 获取标题配置
export function getDetailBg(data: any) {
  return defHttp.post({
    url: Api.Prefix + "/innovateApplicationDetail.do",
    data,
  });
}

// 获取标题配置
export function getPartner(data: any) {
  return defHttp.post({
    url: Api.Prefix + "/getPartners.do",
    data,
  });
}

// 获取标题配置
export function getVisit(url: any) {
  return defHttp.get({
    url: url,
  });
}

// 获取标题配置
export function getAdvantage(data: any) {
  return defHttp.post({
    url: Api.Prefix + '/getAdvantageList.do',
    data
  });
}

// 获取按钮链接
export function getBtnLink(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/entryPointConfig.do`,
    data,
  });
}

// 底部品牌
export function cooperativeZZNumberQuotient(data: any) {
  return defHttp.post({
    url: Api.Prefix + `/cooperativeZZNumberQuotient.do`,
    data,
  });
}
