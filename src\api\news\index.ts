import { defHttp } from "/@/utils/http/axios";

enum Api {
  Prefix = "/manageNews",
}
 
// 获取底部数据列表（分页）
export function getNewslist(data: any) {
  return defHttp.get({
    url: Api.Prefix + `/getManageNewsBottomList.do`,
    data,
  });
}

// 获取tab列表（分页）
export function getTablist(data: any) {
  return defHttp.get({
    url: Api.Prefix + `/getManageNewsHeaderList.do`,
    data,
  });
}

// 获取搜索内容（分页）
export function getSearch(data: any) {
  return defHttp.get({
    url: Api.Prefix + `/getSearch.do`,
    data,
  });
}

// 获取文章详情
export function getDetail(data: any) {
  return defHttp.get({
    url: Api.Prefix + `/getManageNewsDetail.do`,
    data,
  });
}

// 获取文章详情
export function getActive(data: any) {
  return defHttp.post({
    url: `/homePage/getLatestActivitiesDetail.do`,
    data,
  });
}