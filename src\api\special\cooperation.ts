import { defHttp } from "/@/utils/http/axios";

// 获取头部轮播
export function getZoneCarousel(data) {
  return defHttp.post({
    url: "/homePage/carouselList.do",
    data,
  });
}
// 关于我们
export function getModuleConfig(data) {
  return defHttp.post({
    url: "/homePage/getModuleConfig.do",
    data,
  });
}
// 我们的优势
export function dataApplication(data) {
  return defHttp.post({
    url: "/homePage/dataApplication.do",
    data,
  });
}
// 合作资讯
export function cooperationConsultation(data) {
  return defHttp.post({
    url: "/homePage/cooperationConsultation.do",
    data,
  });
}
