import { defHttp } from "/@/utils/http/axios";

// 获取头部轮播
export function getZoneCarousel(data) {
  return defHttp.post({
    url: "/homePage/carouselList.do",
    data,
  });
}
// 获取应用案例 || 优势介绍
export function getDataApplication(data) {
  return defHttp.post({
    url: "/homePage/getZoneUseCases.do",
    data,
  });
}
// 获取特色产品分类
export function getBrandType(data) {
  return defHttp.post({
    url: "/homePage/getBrandType.do",
    data,
  });
}
// 获取特色产品
export function getProductTypeList(data) {
  return defHttp.post({
    url: "/homePage/getProductTypeList.do",
    data,
  });
}
// 活动交流
export function getActivityExchange(data) {
  return defHttp.post({
    url: "/homePage/getActivityExchange.do",
    data,
  });
}
// 合作资讯
export function cooperationConsultation(data) {
  return defHttp.post({
    url: "/homePage/cooperationConsultation.do",
    data,
  });
}
// 自定义标题
export function getModuleConfig(data) {
  return defHttp.post({
    url: "/homePage/getModuleConfig.do",
    data,
  });
}

// 自定义标题
export function getDataDirectory(data) {
  return defHttp.post({
    url: "/homePage/publicDataDirectory.do",
    data,
  });
}

//获取表格 
export function tradingUpdates(data) {
  return defHttp.post({
    url: "/homePage/tradingUpdates.do",
    data,
  });
}
//获取最新登记动态表格
export function registerUpdates(data) {
  return defHttp.post({
    url: "/homePage/registerUpdates.do",
    data,
  });
}


//获取特色产品
export function getProductListByAppName(data) {
  return defHttp.post({
    url: "/homePage/getProductListByAppName.do",
    data,
  });
}

//需求列表
export function demandList(data) {
  return defHttp.post({
    url: "/homePage/demandList.do",
    data,
  });
}

//应用场景的分类接口
export function getApplicationType(data) {
  return defHttp.post({
    url: "/homePage/getApplicationType.do",
    data,
  });
}

//专区特色案例分类
export function getSpecialType(data) {
  return defHttp.post({
    url: "/homePage/getSpecialType.do",
    data,
  });
}

//专区特色产品分类
export function gettsProductType(data) {
  return defHttp.post({
    url: "/homePage/gettsProductType.do",
    data,
  });
}
