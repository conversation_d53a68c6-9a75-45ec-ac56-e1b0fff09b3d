<template>
  <div class="footer">
    <div class="main">
      <div class="info-left">
        <img :src="logo1" />
        <div class="line">
          <div class="line-title">主办单位</div>
          <div class="line-context">福建省大数据集团有限公司</div>
        </div>
        <div class="line">
          <div class="line-title">承办单位</div>
          <div class="line-context">福建省数据流通控股有限公司</div>
        </div>
        <div class="line">
          <div class="line-title">投诉电话</div>
          <div class="line-context">0591-83050001</div>
        </div>
      </div>
      <div class="info-middle">
        <img :src="logo2" />
        <div class="line">
          <div class="line-title">电子邮箱</div>
          <div class="line-context">{{ email }}</div>
        </div>
        <div class="line">
          <div class="line-title">企业地址</div>
          <div class="line-context">
            {{ address }}
          </div>
        </div>
      </div>
      <div class="info-qrcode">
        <img :src="logo3" />
        <div class="info-qrcode-tips">
          {{ "福建大数据交易公司" }}
        </div>
      </div>
      <div class="info-qrcode">
        <img :src="logo4" />
        <div class="info-qrcode-tips">
          {{ name + "公众号" }}
        </div>
      </div>
    </div>

    <div class="copyright">
      <div class="copyright-info">
        <div style="cursor: pointer" @click="openHtml">
          闽ICP备2022004007号-1
        </div>
        <div>闽公网安备35012102500601号</div>
        <div>增值业务经营许可证：闽B2-20240652</div>
        <div>
          Copyright ©2024 FBDE All Rights Reserved.福建省数据流通控股有限公司
          版权所有
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { reactive, computed, ref, onMounted, toRefs, nextTick } from "vue";
import { useRouter } from "vue-router";
import { bottomBarConfiguration } from "/@/api/footer/index";
const router = useRouter();
import { useGlobSetting } from "/@/hooks/setting";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);

const state = reactive({
  classId: {
    footerId: 11127, //底图信息
  },
  logo1: new URL(`/@/assets/footer/c-logo.png`, import.meta.url).href,
  logo2: new URL(`/@/assets/footer/logo2.png`, import.meta.url).href,
  logo3: new URL(`/@/assets/home/<USER>
  logo4: new URL(`/@/assets/footer/qrcode.jpg`, import.meta.url).href,
  email: "",
  address: "",
  name: "",
});
const { classId, logo1, logo2, logo3, logo4, email, address, name } =
  toRefs(state);
function getInfo() {
  bottomBarConfiguration({ classId: state.classId.footerId }).then((res) => {
    state.email = res.email;
    state.address = res.address;
    state.name = res.name;
    state.logo2 = apiUrl.value + res.logo;
    state.logo4 = apiUrl.value + res.official;
  });
  function openHtml() {
    window.open("https://beian.miit.gov.cn/#/Integrated/index", "_blank");
  }
}
onMounted(() => {
  getInfo();
});
</script>
<style lang="less" scoped>
@media screen and (min-width: 769px) {
  .footer {
    position: relative;
    padding: 145rem 0 0 0;
    // min-width: 1024px;
    width: 100%;
    height: 549rem;
    // background: url("/@/assets/footer/bg.png") no-repeat center center / 100%
    //   549rem;
    background-color: #01071a;
    overflow: hidden;
    .main {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      margin: 0 auto;
      width: calc(100% - 320rem);
      .info-left {
        width: 473rem;
        & > img {
          margin: 0 0 12.5rem;
          width: 100%;
          height: 62rem;
        }
        .line {
          display: flex;
          margin-top: 22.5rem;
          width: 100%;
          // height: 32px;
          line-height: 31.82rem;
          color: rgba(255, 255, 255, 0.6);
          font-size: 24rem;
          .line-title {
            margin: 0 29rem 0 21rem;
            color: rgba(255, 255, 255, 1);
            font-weight: 700;
          }
          .line-context {
            letter-spacing: 3rem;
          }
        }
      }
      .info-middle {
        width: 537rem;
        & > img {
          margin: 0 0 12.5rem;
          width: 153rem;
          height: 62rem;
        }
        .line {
          display: flex;
          margin-top: 22.5rem;
          width: 100%;
          // height: 32px;
          line-height: 31.82rem;
          color: rgba(255, 255, 255, 0.6);
          font-size: 24rem;
          .line-title {
            margin: 0 29rem 0 0rem;
            color: rgba(255, 255, 255, 1);
            font-weight: 700;
          }
          .line-context {
            width: 412rem;
            letter-spacing: 3rem;
          }
        }
      }
      .info-qrcode {
        width: 193rem;
        height: 193rem;
        & > img {
          width: 193rem;
          height: 193rem;
        }
        .info-qrcode-tips {
          margin-top: 12rem;
          width: 100%;
          text-align: center;
          font-size: 16rem;
          font-weight: 700;
          letter-spacing: 1rem;
          line-height: 21.22rem;
          color: rgba(255, 255, 255, 1);
        }
      }
    }

    .copyright {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: calc(89.5 / 1080 * 100vh);
      border-top: 1rem solid rgba(255, 255, 255, 0.2);
      .copyright-info {
        display: flex;
        justify-content: space-between;
        width: 61.1%;
        font-size: 16rem;
        font-weight: 300;
        letter-spacing: 0rem;
        line-height: 21.22rem;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}
@media screen and (max-width: 768px) {
  .footer {
    position: relative;
    padding: 45rem 0 0 0;
    width: 100%;
    // background: url("/@/assets/footer/bg.png") no-repeat center center / 100%
    //   549rem;
    background-color: #01071a;
    overflow: hidden;
    .main {
      display: grid;
      grid-template-columns: repeat(2, minmax(0rem, 1fr));
      gap: 20rem;
      flex-wrap: nowrap;
      justify-content: space-between;
      width: 100%;
      padding: 10rem;
      .info-left {
        /* width: 473rem; */
        & > img {
          margin: 0 0 12.5rem;
          width: 100%;
          height: 62rem;
        }
        .line {
          display: flex;
          margin-top: 22.5rem;
          width: 100%;
          // height: 32px;
          line-height: 31.82rem;
          color: rgba(255, 255, 255, 0.6);
          font-size: 24rem;
          .line-title {
            margin: 0 29rem 0 21rem;
            color: rgba(255, 255, 255, 1);
            font-weight: 700;
          }
          .line-context {
            letter-spacing: 3rem;
          }
        }
      }
      .info-middle {
        /* width: 537rem; */
        & > img {
          margin: 0 0 12.5rem;
          width: 153rem;
          height: 62rem;
        }
        .line {
          display: flex;
          margin-top: 22.5rem;
          width: 100%;
          // height: 32px;
          line-height: 31.82rem;
          color: rgba(255, 255, 255, 0.6);
          font-size: 24rem;
          .line-title {
            margin: 0 29rem 0 0rem;
            color: rgba(255, 255, 255, 1);
            font-weight: 700;
          }
          .line-context {
            width: 412rem;
            letter-spacing: 3rem;
          }
        }
      }
      .info-qrcode {
        width: 193rem;
        /* height: 193rem; */
        & > img {
          width: 193rem;
          height: 193rem;
        }
        .info-qrcode-tips {
          margin-top: 12rem;
          width: 100%;
          text-align: center;
          font-size: 16rem;
          font-weight: 700;
          letter-spacing: 1rem;
          line-height: 21.22rem;
          color: rgba(255, 255, 255, 1);
        }
      }
    }

    .copyright {
      /* position: absolute;
      bottom: 0;
      left: 0; */
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: calc(89.5 / 1080 * 100vh);
      border-top: 1rem solid rgba(255, 255, 255, 0.2);
      .copyright-info {
        display: flex;
        justify-content: space-between;
        display: grid;
        grid-template-columns: repeat(2, minmax(0rem, 1fr));
        gap: 20rem;
        font-size: 16rem;
        font-weight: 300;
        letter-spacing: 0rem;
        line-height: 21.22rem;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}
</style>
