<template>
  <div class="home_box home_box_7">
    <div class="home_box_7_top">
      <div class="home_box_7_top_text">非常期待<br />您与我们的联系</div>
      <n-popover
        placement="top"
        trigger="click"
        @update:show="handleUpdateShow"
      >
        <template #trigger>
          <n-button type="warning"> 联系我们 </n-button>
        </template>
        <span>{{ Phone }}</span>
      </n-popover>

      <div class="home_box_7_dot_1"></div>
      <div class="home_box_7_dot_2"></div>
    </div>

    <div class="home_box_7_content">
      <div class="home_box_7_content_left">
        <block style="display: flex; margin-bottom: 20rem">
          <div class="home_box_7_content_title" style="font-weight: 700">
            主办单位
          </div>
          <div class="home_box_7_content_text">福建省大数据集团有限公司</div>
        </block>
        <block style="display: flex; margin-bottom: 20rem">
          <div class="home_box_7_content_title" style="font-weight: 700">
            承办单位
          </div>
          <div class="home_box_7_content_text">福建省数据流通控股有限公司</div>
        </block>
        <block style="display: flex; margin-bottom: 24rem">
          <div class="home_box_7_content_title" style="font-weight: 700">
            投诉电话
          </div>
          <div class="home_box_7_content_text">0591-83050001</div>
        </block>
        <block>
          <n-tabs
            :value="checkTab"
            size="large"
            justify-content="space-evenly"
            @update:value="changeTab"
          >
            <n-tab
              v-for="item in tabList.slice(0, 4)"
              :name="item.classId"
              @click="goPage(item.outerLink)"
            >
              <span>{{ item.className }}</span>
            </n-tab>
          </n-tabs>
          <n-tabs
            :value="checkTab"
            size="large"
            justify-content="space-evenly"
            @update:value="changeTab"
          >
            <n-tab
              v-for="item in tabList.slice(4, 7)"
              :name="item.classId"
              @click="goPage(item.outerLink)"
            >
              <span>{{ item.className }}</span>
            </n-tab>
          </n-tabs>
          <n-tabs
            :value="checkTab"
            size="large"
            justify-content="space-evenly"
            @update:value="changeTab"
          >
            <n-tab
              v-for="item in tabList.slice(7, 99)"
              :name="item.classId"
              @click="goPage(item.outerLink)"
            >
              <span>{{ item.className }}</span>
            </n-tab>
          </n-tabs>
        </block>
      </div>
      <div class="home_box_7_content_right">
        <img src="../assets/home/<USER>" alt="" style="width: 100%" />
      </div>
    </div>
    <div class="home_box_7_foot">
      <div style="cursor: pointer" @click="openHtml">闽ICP备2022004007号-1</div>
      <div>闽公网安备35012102500601号</div>
      <div>增值业务经营许可证：闽B2-20240652</div>
      <div>
        Copyright ©2024 FBDE All Rights Reserved.福建省数据流通控股有限公司
        版权所有
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { getTopList } from "/@/api/home/<USER>";
import { getContract } from "/@/api/footer/index";
import { collectUserVisitInfoAndSendToServer } from "/@/utils/visit.js";
const router = useRouter();
const checkTab = ref("");
const Phone = ref("");
const tabList = ref([]);
function goPage(e) {
  const regex = /(https?)/;
  if (regex.test(e)) {
    window.open(unescapeHTML(e));
  } else {
    router.push(e);
  }
}
function getList() {
  getTopList().then((res) => {
    tabList.value = res;
  });
}
function changeTab(e) {
  checkTab.value = e;
  if (e != 11037) {
    localStorage.setItem("pagePath", e);
  } else {
    checkTab.value = localStorage.getItem("pagePath");
  }
  let params = {
    contentId: e,
  };
  // collectUserVisitInfoAndSendToServer(params);
}
function changePage() {
  tabList.value.filter((e) => {
    if (e.outerLink == localStorage.getItem("savedPath")) {
      if (e.classId != checkTab.value) {
        changeTab(e.classId);
      }
    }
  });
}
function getPhone() {
  let params = {
    classId: 11133,
  };
  getContract(params).then((res) => {
    Phone.value = res.PHONE;
  });
}
function handleUpdateShow(show: boolean) {
  message.success(show ? "show" : "hide");
}
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
function openHtml() {
  window.open("https://beian.miit.gov.cn/#/Integrated/index", "_blank");
}
onMounted(() => {
  getPhone();
  checkTab.value = localStorage.getItem("pagePath") || "11036";
  getList();
  changeTab(checkTab.value);
  window.addEventListener("setItemEvent", function (e) {
    if (e.key === "pagePath") {
      // pagePath 是需要监听的键名
      if (e.newValue != checkTab.value) {
        if (e.newValue != 11037) {
          changeTab(e.newValue); // 这里的newValue就是localStorage中，键名为pagePath对应的变化后的值。
        } else {
          goPage(localStorage.getItem("savedPath"));
          tabList.filter((item) => {
            if (item.outerLink == localStorage.getItem("savedPath")) {
              changeTab(item.classId);
            }
          });
        }
      }
    }
  });
});
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  :deep(.n-button) {
    width: 256rem !important;
    height: 72rem !important;
    border-radius: 5rem;
    background: rgba(242, 76, 39, 1);
    --n-border: 0 !important;
    .n-button__content {
      font-size: 24rem;
      font-weight: 700;
      color: #ffffff;
    }
  }
  :deep(.n-tabs) {
    width: 130%;
    --n-tab-text-color-hover: #ffffff !important;
    --n-tab-text-color-active: #ffffff !important;
    .n-tabs-nav {
      height: 100%;
    }
  }
  :deep(.n-tabs-wrapper) {
    width: 100% !important;
    justify-content: start !important;
    .n-tabs-tab-wrapper {
      margin-right: 68rem;
      width: 100rem;
    }
  }
  :deep(.n-tabs-tab) {
    font-weight: 400;
    font-size: 24rem;
    color: #757983;
    font-weight: 700;
    letter-spacing: 0rem;
    line-height: 41rem;
    line-height: 27rem;
  }
  :deep(.n-tabs-bar) {
    height: 5rem;
    border-radius: 3rem;
    --n-bar-color: #005ced;
  }
  .home_box_7 {
    width: 100%;
    background: rgba(1, 7, 26, 1);
    height: 702rem;
    // min-width: 1600rem;
    display: flex;
    flex-direction: column;
    padding-top: 30rem;
    filter: brightness(90%);
    position: relative;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    overflow: hidden;
    .home_box_7_top {
      width: 1218rem;
      height: 186rem;
      opacity: 1;
      border-radius: 60rem 0rem 60rem 0rem;
      background: rgba(22, 28, 48, 1);
      margin: 55rem auto 48rem;
      padding: 44rem 120rem;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .home_box_7_top_text {
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: -1.44rem;
        line-height: 51.12rem;
        color: rgba(255, 255, 255, 1);
      }
      .home_box_7_dot_1 {
        width: 32rem;
        height: 32rem;
        background: rgba(242, 76, 39, 1);
        position: absolute;
        left: -76rem;
        top: 0rem;
      }
      .home_box_7_dot_2 {
        width: 16rem;
        height: 16rem;
        background: rgba(13, 46, 153, 1);
        position: absolute;
        right: -42rem;
        bottom: 0rem;
      }
    }

    .home_box_7_content {
      display: flex;
      flex: 1;
      justify-content: space-between;
      padding: 0 calc(375 / 1903 * 100%);
      .home_box_7_content_left {
        flex-direction: column;
        font-size: 24rem;
        font-weight: 700;
        color: #fff;
        .home_box_7_content_text {
          font-size: 500;
          opacity: 0.6;
          margin-left: 30rem;
        }
      }
      .home_box_7_content_right {
        width: 220rem;
        height: 220rem;
        background: #ffffff;
      }
    }
    .home_box_7_foot {
      width: 100%;
      text-align: center;
      border-top: 1rem solid rgba(255, 255, 255, 0.2);
      height: 90rem;
      font-size: 16rem;
      font-weight: 300;
      display: flex;
      align-items: center;
      justify-content: space-around;
      color: #aaa;
      /* position: absolute;
      bottom: 0;
      left: 0; */
    }
  }
}
@media screen and (max-width: 768px) {
  :deep(.n-button) {
    width: 200rem !important;
    height: 56rem !important;
    border-radius: 5rem;
    background: rgba(242, 76, 39, 1);
    --n-border: 0 !important;
    .n-button__content {
      font-size: 24rem;
      font-weight: 700;
      color: #ffffff;
    }
  }
  :deep(.n-tabs) {
    width: 130%;
    --n-tab-text-color-hover: #ffffff !important;
    --n-tab-text-color-active: #ffffff !important;
    .n-tabs-nav {
      height: 100%;
    }
  }
  :deep(.n-tabs-wrapper) {
    width: 100% !important;
    justify-content: start !important;
    .n-tabs-tab-wrapper {
      margin-right: 68rem;
      width: 80rem;
      height: 80rem;
    }
  }
  :deep(.n-tabs-tab) {
    font-weight: 400;
    font-size: 24rem;
    color: #757983;
    font-weight: 700;
    letter-spacing: 0rem;
    line-height: 41rem;
    line-height: 27rem;
  }
  :deep(.n-tabs-bar) {
    height: 5rem;
    border-radius: 3rem;
    --n-bar-color: #005ced;
  }
  .home_box_7 {
    width: 100%;
    background: rgba(1, 7, 26, 1);
    /* height: 702rem; */
    display: flex;
    flex-direction: column;
    padding-top: 30rem;
    filter: brightness(90%);
    /* position: relative; */
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    overflow: hidden;
    .home_box_7_top {
      width: 720rem;
      height: 186rem;
      opacity: 1;
      border-radius: 60rem 0rem 60rem 0rem;
      background: rgba(22, 28, 48, 1);
      margin: 55rem auto 48rem;
      padding: 44rem 60rem;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .home_box_7_top_text {
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: -1.44rem;
        line-height: 51.12rem;
        color: rgba(255, 255, 255, 1);
      }
      .home_box_7_dot_1 {
        width: 32rem;
        height: 32rem;
        background: rgba(242, 76, 39, 1);
        position: absolute;
        left: -76rem;
        top: 0rem;
      }
      .home_box_7_dot_2 {
        width: 16rem;
        height: 16rem;
        background: rgba(13, 46, 153, 1);
        position: absolute;
        right: -42rem;
        bottom: 0rem;
      }
    }

    .home_box_7_content {
      display: flex;
      flex: 1;
      justify-content: space-between;
      width: 100%;
      padding: 0 60rem;
      .home_box_7_content_left {
        flex-direction: column;
        font-size: 24rem;
        font-weight: 700;
        color: #fff;
        width: 70%;
        .home_box_7_content_text {
          font-size: 500;
          opacity: 0.6;
          margin-left: 30rem;
        }
      }
      .home_box_7_content_right {
        width: 189rem;
        height: 189rem;
        background: #ffffff;
      }
    }
    .home_box_7_foot {
      width: 100%;
      text-align: center;
      border-top: 1rem solid rgba(255, 255, 255, 0.2);
      /* height: 90rem; */
      font-size: 16rem;
      font-weight: 300;
      display: flex;
      align-items: center;
      justify-content: space-around;
      color: #aaa;
      display: grid;
      grid-template-columns: repeat(2, minmax(0rem, 1fr));
      gap: 20rem;
      padding: 10rem;
      /* position: absolute;
      bottom: 0;
      left: 0; */
    }
  }
}
</style>
