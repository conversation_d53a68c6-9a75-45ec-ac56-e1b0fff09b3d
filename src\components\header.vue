<template>
  <div class="fixed">
    <div class="header">
      <div class="first-nav">
        <div class="logo" @click="goHome()">
          <img src="../assets/logo.png" alt="" />
        </div>
        <div class="tabLan">
          <n-tabs
            :value="checkTab"
            size="large"
            justify-content="space-evenly"
            @update:value="changeTab"
          >
            <n-tab
              v-for="item in tabList"
              :name="item.classId"
              :class="{
                'has-child': item.childList && item.childList.length > 0,
              }"
              @click="goPage(item)"
              @mousemove="openChild(item)"
            >
              <span>{{ item.className }}</span>
            </n-tab>
          </n-tabs>
        </div>
      </div>

      <div
        class="second-nav"
        v-if="Array.isArray(secondTabList) && secondTabList.length > 0"
      >
        <div v-if="secondTabList[0]?.childList?.length !== 0">
          <div
            class="swiper-container"
            v-for="(item, index) in secondTabList"
            :key="index"
          >
            <div>
              <div class="slide-title">{{ item.className }}</div>
              <div class="slide-wrapper">
                <div
                  class="swiperSlideBox"
                  v-for="(item2, index2) in item.childList"
                  :key="index2"
                >
                  <img
                    class="swiper-img"
                    v-if="item2.image"
                    :src="apiUrl + item2.image"
                    @click="goPage(item2)"
                  />
                  <div
                    class="swiper-img"
                    style="background: rgba(255, 255, 255, 0.3)"
                    v-else
                  ></div>
                  <div class="swiper-context">
                    <div class="second-header">
                      <span class="second-title" @click="goPage(item2)">{{
                        item2.className
                      }}</span>
                      <div class="second-intro">
                        {{ item2.subTitle }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="swiper-containerX" v-else>
          <div
            class="slide-wrapper"
            v-for="(item, index2) in secondTabList"
            :key="index2"
          >
            <div class="swiperSlideBox">
              <img
                class="swiper-img"
                v-if="item.image"
                :src="apiUrl + item.image"
                @click="goPage(item)"
              />
              <div
                class="swiper-img"
                style="background: rgba(255, 255, 255, 0.3)"
                v-else
              ></div>
              <div class="swiper-context">
                <div class="second-header">
                  <span class="second-title" @click="goPage(item)">{{
                    item.className
                  }}</span>
                  <div class="second-intro">{{ item.subTitle }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="overlay"
        v-if="showOverlay"
        :style="{ top: overlayTop + 'px' }"
        @click="hideSecondNav"
      ></div>
    </div>
    <div class="m-header">
      <div class="logo-box">
        <img src="../assets/logo.png" alt="" />
      </div>
      <div class="menu-btn">
        <div
          class="buttonBase"
          :class="showMenu ? 'closeShow' : 'closeHide'"
          style="transition: all 0.3s ease 0s; position: absolute; right: 0"
          @click="changeMenuShow"
        >
          <CloseOutline style="color: #fff"></CloseOutline>
        </div>
        <div
          class="buttonBase"
          :class="showMenu ? 'openHide' : ''"
          style="transition: all 0.3s ease 0s"
          @click="changeMenuShow"
        >
          <Menu style="color: #fff"></Menu>
        </div>
      </div>
    </div>
    <div class="bg" v-if="showMenu"></div>
    <div class="menu-list" v-if="showMenu">
      <div
        class="menuItem"
        v-for="(item, index) in tabList"
        :key="item.classId"
      >
        <mobile-sidebar-item
          :item="item"
          @closeMenu="closeMenu"
        ></mobile-sidebar-item>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  reactive,
  toRefs,
  ref,
  onMounted,
  onUnmounted,
  nextTick,
  watch,
} from "vue";
import { useRouter } from "vue-router";
import { getTopList } from "/@/api/home/<USER>";
import { collectUserVisitInfoAndSendToServer } from "/@/utils/visit.js";
import {
  Menu,
  CloseOutline,
  CaretBackOutline,
  CaretForward,
} from "@vicons/ionicons5";
import mobileSidebarItem from "./mobileSidebarItem.vue";
import { useGlobSetting } from "/@/hooks/setting";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);

interface TabItem {
  classId: string;
  outerLink: string;
  className: string;
  childList?: Array<{ outerLink: string; className: string }>;
}

const state = reactive({
  showMenu: false,
  useSwiper: null,
  secondTabList: [],
  showOverlay: false,
  overlayTop: 0,
});
const { showMenu, useSwiper, secondTabList, showOverlay, overlayTop } =
  toRefs(state);
const router = useRouter();
const checkTab = ref<string>("11036");
const OldCheckTab = ref<string>("11036");
const tabList = ref<TabItem[]>([]);

function goPage(e) {
  // if (Array.isArray(e.childList) && e.childList.length > 0) {
  //   secondTabList.value = e.childList;
  //   console.log("看看这个", e);

  //   nextTick(() => {
  //     calculateOverlayTop();
  //   });
  // } else {
  OldCheckTab.value = e.classId;
  secondTabList.value = [];
  const regex = /(https?)/;
  if (regex.test(e.outerLink)) {
    // 如果是外部链接，则在新窗口打开
    window.open(e.outerLink, "_blank");
  } else {
    // 如果是内部链接，则进行内部路由跳转
    router.push(e.outerLink);
    // 检查是否包含 "/special" 并更新 tab 相关状态
    if (e.outerLink.includes("/special")) {
      checkTab.value = 11038;
      OldCheckTab.value = 11038;
      changeTab(11038);
    }
  }
  // }
}
function openChild(e) {
  if (Array.isArray(e.childList) && e.childList.length > 0) {
    secondTabList.value = e.childList;
    checkTab.value = e.classId;

    nextTick(() => {
      calculateOverlayTop();
    });
  } else {
    closeChild();
  }
}
function closeChild() {
  console.log("出来了", checkTab.value, "-,", OldCheckTab.value);
  secondTabList.value = [];
  checkTab.value = OldCheckTab.value;
}
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
function getList() {
  getTopList().then((res: any) => {
    tabList.value = res;
  });
}
function changeTab(e) {
  checkTab.value = e;

  if (tabList.value.length != 0) {
    let path = tabList.value.filter((item) => item.classId == e)[0].outerLink;
    const regex = /(https?)/;
    if (!regex.test(path)) {
      localStorage.setItem("pagePath", e);
    } else {
      checkTab.value = localStorage.getItem("pagePath");
    }
  }
  let params = {
    contentId: e,
  };
  collectUserVisitInfoAndSendToServer(params);
}

// 控制遮罩层位置
function calculateOverlayTop() {
  const headerElement = document.querySelector(".header");
  if (headerElement) {
    const rect = headerElement.getBoundingClientRect();
    overlayTop.value = rect.bottom;
  }
}
// 遮罩层是否显示
watch(secondTabList, (newVal) => {
  showOverlay.value = newVal.length > 0;
});
// 关闭遮罩层
function hideSecondNav() {
  secondTabList.value = [];
  showOverlay.value = false;
}
function goHome() {
  checkTab.value = "11036";
  goPage("/home");
}
function changeMenuShow() {
  if (showMenu.value) return closeMenu();
  openMenu();
}
function openMenu() {
  showMenu.value = true;
  sessionStorage.setItem("scrollTop", document.documentElement.scrollTop);
  document.body.style.overflow = "hidden";
  document.body.style.position = "fixed";
  document.body.style.top =
    0 - Number(sessionStorage.getItem("scrollTop")) + "px";
}
function closeMenu() {
  showMenu.value = false;
  document.body.style.overflow = "auto";
  document.body.style.position = "static";
  window.scrollTo({ top: Number(sessionStorage.getItem("scrollTop")) });
}

onMounted(() => {
  getList();
  nextTick(() => {
    checkTab.value = localStorage.getItem("pagePath") || "11036";
    changeTab(checkTab.value);
  });
  window.addEventListener("setItemEvent", function (e) {
    if (e.key == "pagePath") {
      // pagePath 是需要监听的键名
      if (e.newValue != checkTab.value) {
        console.log("e.newValue", e.newValue);
        if (e.newValue != 11037) {
          changeTab(e.newValue); // 这里的newValue就是localStorage中，键名为pagePath对应的变化后的值。
        } else {
          goPage(localStorage.getItem("savedPath"));
          tabList.value.forEach((item) => {
            if (item.outerLink == localStorage.getItem("savedPath")) {
              changeTab(item.classId);
            }
          });
        }
      }
    }
    if (e.key == "savedPath") {
      console.log(e.newValue);
    }
  });
});
onUnmounted(() => {});
</script>
<style scoped lang="less">
.fixed {
  position: fixed;
  top: 0;
  z-index: 99999;
  width: 100%;
}

@media screen and (min-width: 769px) {
  :deep(.n-tabs) {
    height: 100%;
    /* --n-tab-text-color-hover: #005ced !important; */
    --n-tab-text-color-hover: #ffffff !important;
    --n-tab-text-color-active: #ffffff !important;

    .n-tabs-nav {
      height: 100%;
    }

    .n-tabs-pane-wrapper {
      height: 100%;
    }
  }

  :deep(.n-tabs-tab--active) {
    font-weight: 400;
    /* font-size: 18rem; */
    font-size: 22.2rem;
    color: #ffffff;
    line-height: 27rem;
  }

  :deep(.n-tabs-tab) {
    /* opacity: 0.6; */
    font-size: 22.2rem;
    font-weight: 400;
    letter-spacing: 0rem;
    line-height: 29rem;
    color: rgba(255, 255, 255, 0.6);
  }

  :deep(.n-tabs-tab-wrapper) {
    margin-right: 30rem;
  }

  :deep(.n-tabs-bar) {
    height: 5rem;
    border-radius: 3rem;
    --n-bar-color: #ffffff;
  }

  :deep(.v-x-scroll) {
    height: 100%;
  }

  .first-nav {
    width: 100%;
    /* height: 83rem; */
    height: calc(120 / 1080 * 100vh);
    opacity: 0.9;
    background: rgba(13, 46, 153, 1);
    box-shadow: 0rem 6rem 20rem rgba(36, 55, 171, 0.45);
    backdrop-filter: blur(6rem);
    padding: 0 48rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .logo {
      width: 473rem;
      height: 62rem;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .tabLan {
      /* width: 711rem; */
      /* width: 780rem; */
    }
  }

  .second-nav {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    padding: 40rem 0rem 20rem;
    width: 100%;
    background-color: rgba(247, 251, 255, 0.95);

    .swiper-container {
      margin: 0 auto;
      width: calc(100% - 180rem);
      display: flex;
      flex-direction: column;
      .slide-title {
        font-size: 30rem;
        font-weight: 700;
        width: 100%;
        text-align: left;
        margin-bottom: 20rem;
        padding-left: calc((33% - 380rem) / 2);
      }
      .slide-wrapper {
        display: grid; // 使用grid布局
        grid-template-columns: repeat(3, 1fr); // 一行三列
        gap: 20rem; // 列间距
        width: 100%;
      }

      .swiperSlideBox {
        display: flex;
        justify-content: center;
        align-items: stretch;
        margin-bottom: 20rem;

        .swiper-img {
          width: 180rem;
          height: 80rem;
          cursor: pointer;
        }

        .swiper-context {
          // flex: 1;
          width: 200rem;
          padding-left: 20rem;

          .second-header {
            font-size: 20rem;
            line-height: 20rem;

            .second-title {
              max-width: 100%;
              font-weight: bold;
              padding-bottom: 5rem;
              border-bottom: 2px solid #6fbeff;
              cursor: pointer;
            }
            .second-intro {
              margin-top: 15rem;
              width: 100%;
              min-height: 20rem;
              color: #3a3b3d;
              font-size: 17rem;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              cursor: default;
            }
          }
        }
      }
    }
    .swiper-containerX {
      margin: 0 auto;
      width: calc(100% - 180rem);
      display: flex;
      .slide-title {
        font-size: 30rem;
        font-weight: 700;
        width: 100%;
        text-align: left;
        margin-bottom: 20rem;
        padding-left: calc((33% - 380rem) / 2);
      }
      .slide-wrapper {
        display: grid; // 使用grid布局
        grid-template-columns: repeat(3, 1fr); // 一行三列
        gap: 20rem; // 列间距
        width: 100%;
      }

      .swiperSlideBox {
        display: flex;
        justify-content: center;
        align-items: stretch;
        margin-bottom: 20rem;

        .swiper-img {
          width: 180rem;
          height: 80rem;
          cursor: pointer;
        }

        .swiper-context {
          // flex: 1;
          width: 200rem;
          padding-left: 20rem;

          .second-header {
            font-size: 20rem;
            line-height: 20rem;

            .second-title {
              max-width: 100%;
              font-weight: bold;
              padding-bottom: 5rem;
              border-bottom: 2px solid #6fbeff;
              cursor: pointer;
            }
            .second-intro {
              margin-top: 15rem;
              width: 100%;
              min-height: 20rem;
              color: #3a3b3d;
              font-size: 17rem;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              cursor: default;
            }
          }
        }
      }
    }
  }

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    z-index: 997;
  }

  .m-header {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  .header {
    display: none;
  }

  .m-header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 75px;
    background-color: #0d2e99;

    .logo-box {
      overflow: hidden;
      width: 100%;

      & > img {
        width: 473rem;
        height: 62rem;
      }
    }

    .menu-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      margin-right: 16rem;
      width: 84rem;
      height: 48px;
      pointer-events: auto;

      .buttonBase {
        width: 100%;
        height: 100%;
      }

      .closeShow {
        transform: rotate(0deg);
        opacity: 1;
      }

      .closeHide {
        transform: rotate(-45deg);
        opacity: 0;
      }

      .openHide {
        transform: rotate(45deg);
        opacity: 0;
      }
    }
  }

  .bg {
    position: fixed;
    top: 75px;
    right: 0;
    z-index: 998;
    width: 100%;
    height: calc(100vh - 75px);
    background-color: black;
    opacity: 0.5;
  }

  .menu-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    // flex-wrap: wrap;
    position: fixed;
    top: 75px;
    right: 0;
    z-index: 999;
    padding-top: 10px;
    width: 564rem;
    height: calc(100vh - 75px);
    background-color: #fff;
    transition: all 0.3s ease 0s;
    opacity: 1;
    overflow-y: auto;

    .menuItem {
      width: 535rem;
    }
  }
}
</style>
