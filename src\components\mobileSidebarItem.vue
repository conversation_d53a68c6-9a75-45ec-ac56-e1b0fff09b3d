<template>
  <div v-if="item.childList && item.childList.length">
    <div class="title">
      {{ props.item.className }}
    </div>
    <mobile-sidebar-item
      v-for="child in item.childList"
      :item="child"
      @closeMenu="closeMenu"
      class="nest-menu"
    ></mobile-sidebar-item>
  </div>
  <div
    v-else
    class="title"
    :class="
      router.currentRoute.value.path == props.item.outerLink ? 'menuActive' : ''
    "
    @click="goPage(item.outerLink)"
  >
    {{ props.item.className }}
  </div>
</template>

<script lang="ts">
export default {
  name: "mobileSidebarItem", //给组件命名
};
</script>

<script lang="ts" setup>
import { reactive, toRefs, ref, onMounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
console.log("router", router.currentRoute.value.path);

const props = defineProps({
  item: {
    default: {},
    type: Object,
  },
});

const emit = defineEmits(["closeMenu"]);

function goPage(e) {
  closeMenu();
  const regex = /(https?)/;
  if (regex.test(e)) {
    window.open(unescapeHTML(e));;
  } else {
    router.push(e);
  }
}
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
function closeMenu() {
  emit("closeMenu");
}
</script>
<style scoped lang="less">
.title {
  padding-left: 40rem;
  height: 50px;
  line-height: 50px;
  color: #333333;
  background-color: #fff;
  font-size: 18px;
  font-weight: 600;
}
.title.nest-menu {
  padding-left: 78rem;
  font-size: 15px;
  font-weight: 600;
}
.menuActive {
  background-color: #005ced;
  color: #fff;
}
</style>
