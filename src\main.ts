import "./assets/main.css";

import { createApp } from "vue";
import { createPinia } from "pinia";

import App from "./App.vue";
import router from "./router";
import Particles from "particles.vue3";
import naive from "naive-ui";
import Header from "./components/header.vue";
import Footer from "./components/footer.vue";
import dispatchEventStroage from './utils/watchLocalStorage'
import vue3SeamlessScroll from "vue3-seamless-scroll";
import './styles/index.css'
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

import {vue3ScrollSeamless} from "vue3-scroll-seamless";

const app = createApp(App);

app.use(createPinia());
app.use(router).use(naive).use(Particles);
app.use(dispatchEventStroage);
// app.use(videojs);
app.use(vue3SeamlessScroll);

// 注册全局头部组件
app.component("Header", Header);
app.component("Footer", Footer);
app.component('vue3ScrollSeamless',vue3ScrollSeamless)

app.mount("#app");
