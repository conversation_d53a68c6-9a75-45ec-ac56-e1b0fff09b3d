import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
} from "vue-router";
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "/",
      redirect: "/home",
    },
    {
      path: "/home",
      name: "home",
      component: () => import("../views/home/<USER>"),
      // children: [
      //   {
      //     path: "/home/<USER>",
      //     name: "createDetail",
      //     component: () => import("../views/home/<USER>/createDetail.vue"),
      //   },
      // ],
    },
     {
      path: "/zhangzhou",
      name: "zhangzhou",
      component: () => import("../views/zhanzhou/index.vue"),
      // children: [
      //   {
      //     path: "/home/<USER>",
      //     name: "createDetail",
      //     component: () => import("../views/home/<USER>/createDetail.vue"),
      //   },
      // ],
    },
     {
      path: "/quanzhou",
      name: "quanzhou",
      component: () => import("../views/quanzhou/index.vue"),
      
    },
         {
      path: "/medicine",
      name: "medicine",
      component: () => import("../views/medicine/index.vue"),
      // children: [
      //   {
      //     path: "/home/<USER>",
      //     name: "createDetail",
      //     component: () => import("../views/home/<USER>/createDetail.vue"),
      //   },
      // ],
    },
    {
      path: "/about",
      name: "about",
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import("../views/AboutView.vue"),
    },
    {
      path: "/special",
      name: "special",
      component: () => import("../views/special/index.vue"),
    },
    {
      path: "/buildGarden",
      name: "buildGarden",
      component: () => import("../views/buildingGarden/index.vue"),
    },
    {
      path: "/buildGarden/buildDetail",
      name: "buildDetail",
      component: () => import("../views/buildingGarden/detail.vue"),
    },
    {
      path: "/news",
      name: "news",
      component: () => import("../views/news/index.vue"),
    },
    {
      path: "/newsDetail",
      name: "newsDetail",
      component: () => import("../views/news/newsDetail.vue"),
    },
    {
      path: "/home/<USER>",
      name: "createDetail",
      component: () => import("../views/home/<USER>/createDetail.vue"),
    },
    {
      path: "/digtalEcology",
      name: "digtalEcology",
      component: () => import("../views/digtalEcology/index.vue"),
    },
    {
      path: "/home/<USER>",
      name: "policyGuidce",
      component: () => import("../views/home/<USER>/policyGuidce.vue"),
    },
    {
      path: "/digtalEcology",
      name: "digtalEcology",
      component: () => import("../views/digtalEcology/index.vue"),
    },
    {
      path: "/cooperation",
      name: "cooperation",
      component: () => import("../views/cooperation/index.vue"),
    },
    {
      path: "/product",
      name: "product",
      component: () => import("../views/product/index.vue"),
    },
    {
      path: "/satelliteProduct",
      name: "satelliteProduct",
      component: () => import("../views/product/satelliteProduct.vue"),
    },
    {
      path: "/zhangzhouProduct",
      name: "zhangzhouProduct",
      component: () => import("../views/product/zhangzhouProduct.vue"),
    },
    
    {
      path: "/digitalTrade",
      name: "digitalTrade",
      component: () => import("../views/digitalTrade/index.vue"),
    },
    {
      path: "/special/public",
      name: "specialPublic",
      component: () => import("../views/special/public.vue"),
    },
    {
      path: "/special/Livelihood",
      name: "Livelihood",
      component: () => import("../views/special/Livelihood.vue"),
    },
    {
      path: "/special/wenlvData",
      name: "wenlvData",
      component: () => import("../views/special/wenlvData.vue"),
    },
    {
      path: "/about",
      name: "about",
      component: () => import("../views/about/index.vue"),
    },
    {
      path: "/special/Meteorology",
      name: "Meteorology",
      component: () => import("../views/special/Meteorology.vue"),
    },
    {
      path: "/special/satellite",
      name: "satellite",
      component: () => import("../views/special/satellite.vue"),
    },
    
  ],
  scrollBehavior: (to, from, savedPosition) => {
    if (savedPosition) {
      console.log('savedPosition',savedPosition)
      return savedPosition;
    } else {
      return {
        top: 0,
        left: 0,
      };
    }
  },
});

router.beforeEach((to, from, next) => {
  // 在每次路由切换前，检查是否是刷新操作
  if (from === undefined) {
    // 如果是刷新操作，则尝试从本地存储中获取之前保存的页面路径
    const savedPath = localStorage.getItem("savedPath");
    if (savedPath && savedPath !== to.path) {
      // 如果本地存储中有保存的路径，并且不等于当前要跳转的路径，则跳转到保存的路径
      next(savedPath);
    } else {
      // 否则，继续正常的路由导航
      next();
    }
  } else {
    // 如果不是刷新操作，则继续正常的路由导航
    next();
  }
});

router.afterEach((to, from) => {
  // 在每次路由切换后，将当前页面的路径保存到本地存储中
  localStorage.setItem("savedPath", to.path);
  userStore.setPath(to.path);
});
export default router;
