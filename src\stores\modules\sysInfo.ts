import { defineStore } from "pinia";
import { store } from "/@/stores";
interface UserState {
  path: string;
  deviceWidth: string | number;
}

export const useInfoStore = defineStore({
  id: "sys-info",
  state: (): UserState => ({
    path: "",
    deviceWidth: "",
  }),
  getters: {
    getPath(): string {
      return this.path || localStorage.getItem("savedPath");
    },
    getDeviceWidth(): string | number {
      return this.deviceWidth;
    },
  },
  actions: {
    setPath(info: string | undefined) {
      this.path = info ? info : ""; // for null or undefined value
    },
    setDeviceWidth(width: string | number) {
      this.deviceWidth = width;
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useInfoStore(store);
}
