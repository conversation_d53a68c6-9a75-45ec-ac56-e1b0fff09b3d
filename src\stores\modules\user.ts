import type { UserInfo, PermissionInfo } from "types/store";
import { defineStore } from "pinia";
import { store } from "/@/store";
import { TOKEN_KEY, USER_INFO_KEY } from "/@/enums/cacheEnum";
import { getAuthCache, setAuthCache } from "/@/utils/auth";
import { h } from "vue";
import { deepMerge } from "/@/utils";

interface UserState {
  userInfo: Nullable<UserInfo>;
  token?: string;
  //   permissionList: PermissionInfo[];
  //   backMenuList: BackMenu[];
  //   backRouterList: BackMenu[];
  sessionTimeout?: boolean;
  lastUpdateTime: number;
}

export const useUserStore = defineStore({
  id: "app-user",
  state: (): UserState => ({
    // user info
    userInfo: null,
    // token
    token: undefined,
    // permissionList
    // Whether the login expired
    sessionTimeout: false,
    // Last fetch time
    lastUpdateTime: 0,
  }),
  getters: {
    getUserInfo(): UserInfo {
      return this.userInfo || getAuthCache<UserInfo>(USER_INFO_KEY) || {};
    },
    getToken(): string {
      return this.token || getAuthCache<string>(TOKEN_KEY);
    },
    getSessionTimeout(): boolean {
      return !!this.sessionTimeout;
    },
    getLastUpdateTime(): number {
      return this.lastUpdateTime;
    },
  },
  actions: {
    setToken(info: string | undefined) {
      this.token = info ? info : ""; // for null or undefined value
      setAuthCache(TOKEN_KEY, info);
    },
    setUserInfo(info: DeepPartial<UserInfo> | null) {
      this.userInfo = info ? deepMerge(this.userInfo || {}, info as any) : info;
      this.lastUpdateTime = new Date().getTime();
      setAuthCache(USER_INFO_KEY, info);
    },
    setSessionTimeout(flag: boolean) {
      this.sessionTimeout = flag;
    },
    resetState() {
      this.userInfo = null;
      this.token = "";
      this.sessionTimeout = false;
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
