// (function (win) {
//   function refreshRem() {
//     let wW =
//       document.documentElement.clientWidth ||
//       document.body.innerWidth ||
//       document.body.clientWidth ||
//       window.screen.width ||
//       window.screen.availWidth ||
//       document.body.clientWidth; // 窗口宽度
//     let designSize = 1920; // 设计图尺寸
//     if (wW <= 768) {
//       designSize = 750;
//     }
//     let rem = wW / designSize;
//     document.documentElement.style.fontSize = rem + "px";
//   }
//   // f12改变窗口大小调试的时候方便
//   win.addEventListener("resize", function () {
//     refreshRem();
//   });

//   refreshRem();
// })(window);

import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

export function getDeviceWidth(win: Window) {
  function refreshRem() {
    let wW =
      document.documentElement.clientWidth ||
      document.body.innerWidth ||
      document.body.clientWidth ||
      window.screen.width ||
      window.screen.availWidth ||
      document.body.clientWidth; // 窗口宽度
    let designSize = 1920; // 设计图尺寸
    if (wW <= 768) {
      designSize = 750;
    }
    let rem = wW / designSize;
    document.documentElement.style.fontSize = rem + "px";
    userStore.setDeviceWidth(wW);
  }
  // f12改变窗口大小调试的时候方便
  win.addEventListener("resize", function () {
    refreshRem();
  });
  refreshRem();
}
