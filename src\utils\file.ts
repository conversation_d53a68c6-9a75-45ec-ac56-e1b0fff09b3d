export function getFileType(filename: string): string {
  const imageExtensions: string[] = [".jpg", ".jpeg", ".png", ".gif"];
  const videoExtensions: string[] = [".mp4", ".avi", ".mov", ".mkv"];

  // 获取文件名的后缀（假设后缀始终位于最后一个点之后）
  const fileExtension: string = filename
    .substring(filename.lastIndexOf("."))
    .toLowerCase();

  if (imageExtensions.includes(fileExtension)) {
    return "1"; //图片
  } else if (videoExtensions.includes(fileExtension)) {
    return "2"; //视频
  } else {
    return ""; //未知类型
  }
}
