import { getVisit } from "/@/api/home/<USER>";

var req;
var visitID;
var cidTemp;

var contentId = -1;
var classId = -1;
var siteId = 14;
var siteUrl = "";
var domain = "";
var notHost = "false";
export function collectUserVisitInfoAndSendToServer(param) {
  var statData;
  var link = window.location.href;
  var host = window.location.host;
  var prevLink = document.referrer;
  var contentId;
  var classId;
  if (param.contentId) {
    contentId = param.contentId;
  } else if (param.classId) {
    classId = param.classId;
  }
  // var titleName = document.title;
  var screen = window.screen.width + "*" + window.screen.height;
  statData =
    "/stat/collStat.do?" +
    "host=" +
    encode(host) +
    "&url=" +
    encode(link) +
    "&reffer=" +
    encode(prevLink) +
    "&system=" +
    getOsInfo() +
    "&screen=" +
    screen +
    "&browser=" +
    getBr() +
    "&refferKey=" +
    getKeyword(prevLink) +
    "&lang=" +
    getLan() +
    "&contentId=" +
    contentId +
    "&classId=" +
    classId +
    "&siteId=" +
    siteId;
  //只加载一次jquery资源
  //   if (typeof jQuery == "undefined") {
  //     var fileObj = null;
  //     fileObj = document.createElement("script");
  //     fileObj.src = domain + "/core/javascript/jquery-3.7.1.min.js";
  //     fileObj.onload = fileObj.onreadystatechange = function () {
  //       //只有js加载完成后,才可以执行
  //       if (
  //         !this.readyState ||
  //         "loaded" === this.readyState ||
  //         "complete" === this.readyState
  //       ) {
  //         //跨域
  //         $.getScript(statData, function () {});
  //       }
  //     };
  //     document.getElementsByTagName("head")[0].appendChild(fileObj);
  //   } else {
  //     //跨域
  //     $.getScript(statData, function () {});
  //   }
  getVisit(statData).then((res) => {
  });
}

function encode(target) {
  return encodeURIComponent(encodeURIComponent(target));
}

// 获取来自搜索引擎的关键词
function getKeyword(url) {
  if (url.toString().indexOf(".baidu.com") > 0) {
    return request(url, "wd");
  } else if (url.toString().indexOf(".so.com") > 0) {
    return request(url, "q");
  } else if (url.toString().indexOf(".haosou.com") > 0) {
    return request(url, "q");
  }
  //else if (url.toString().indexOf("google") > 0)
  // {
  // return request(url, "q");
  //}
  else if (url.toString().indexOf(".sogou.com") > 0) {
    return request(url, "query");
  } else if (url.toString().indexOf(".soso.com") > 0) {
    return request(url, "query");
  } else {
    return "";
  }
}

// 获取链接地址中某个参数的值
function request(url, paras) {
  var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
  var paraObj = {};
  for (i = 0; (j = paraString[i]); i++) {
    paraObj[j.substring(0, j.indexOf("=")).toLowerCase()] = j.substring(
      j.indexOf("=") + 1,
      j.length
    );
  }
  var returnValue = paraObj[paras.toLowerCase()];
  if (typeof returnValue == "undefined") {
    return "";
  } else {
    return returnValue;
  }
}

// 回调函数，可以获取添加后的访问ID，以便其他操作。
function callback() {
  if (req.readyState == 4) {
    if (req.status == 200) {
      visitID = req.responseText.toString();
    } else {
    }
  } else {
  }
}

/*
 * 获取系统版本信息
 */
function getOsInfo() {
  let userAgent = window.navigator.userAgent.toLowerCase();
  let version = "";
  if (userAgent.indexOf("win") > -1) {
    if (
      userAgent.indexOf("Windows nt 5.0") > -1 ||
      userAgent.indexOf("Windows 2000") > -1
    ) {
      version = "Windows 2000";
    } else if (
      userAgent.indexOf("Windows nt 5.1") > -1 ||
      userAgent.indexOf("Windows XP") > -1
    ) {
      version = "Windows XP";
    } else if (
      userAgent.indexOf("Windows nt 5.2") > -1 ||
      userAgent.indexOf("Windows 2003") > -1
    ) {
      version = "Windows 2003";
    } else if (
      userAgent.indexOf("Windows nt 6.0") > -1 ||
      userAgent.indexOf("Windows Vista") > -1
    ) {
      version = "Windows Vista";
    } else if (
      userAgent.indexOf("Windows nt 6.1") > -1 ||
      userAgent.indexOf("Windows 7") > -1
    ) {
      version = "Windows 7";
    } else if (
      userAgent.indexOf("Windows nt 6.2") > -1 ||
      userAgent.indexOf("Windows 8") > -1
    ) {
      version = "Windows 8";
    } else if (userAgent.indexOf("Windows nt 6.3") > -1) {
      version = "Windows 8.1";
    } else if (
      userAgent.indexOf("Windows nt 6.4") > -1 ||
      userAgent.indexOf("Windows nt 10") > -1
    ) {
      version = "Windows 10";
    } else {
      version = "Unknown";
    }
  } else if (userAgent.indexOf("iphone") > -1) {
    version = "Iphone";
  } else if (userAgent.indexOf("mac") > -1) {
    version = "Mac";
  } else if (
    userAgent.indexOf("x11") > -1 ||
    userAgent.indexOf("unix") > -1 ||
    userAgent.indexOf("sunname") > -1 ||
    userAgent.indexOf("bsd") > -1
  ) {
    version = "Unix";
  } else if (userAgent.indexOf("linux") > -1) {
    if (userAgent.indexOf("android") > -1) {
      version = "Android";
    } else {
      version = "Linux";
    }
  } else {
    version = "其他";
  }
  return version;
}

// 获取系统信息
function getSystemInfo_NOTUSE() {
  var ua = navigator.userAgent.toLowerCase();
  isWin8 = ua.indexOf("nt 6.2") > -1;
  isWin7 = ua.indexOf("nt 6.1") > -1;
  isVista = ua.indexOf("nt 6.0") > -1;
  isWin2003 = ua.indexOf("nt 5.2") > -1;
  isWinXp = ua.indexOf("nt 5.1") > -1;
  isWin2000 = ua.indexOf("nt 5.0") > -1;
  isWindows = ua.indexOf("Windows") != -1 || ua.indexOf("win32") != -1;
  isMac = ua.indexOf("macintosh") != -1 || ua.indexOf("mac os x") != -1;
  isAir = ua.indexOf("adobeair") != -1;
  isLinux = ua.indexOf("linux") != -1;
  isAndroid = ua.match(/android/i) === "android";
  isIpad = ua.match(/ipad/i) === "ipad";
  isIphoneOs = ua.match(/iphone os/i) === "iphone os";

  var broser = "";
  if (isWin7) {
    sys = "Windows 7";
  } else if (isWin8) {
    sys = "Windows 8";
  } else if (isVista) {
    sys = "Vista";
  } else if (isWinXp) {
    sys = "Windows XP";
  } else if (isWin2003) {
    sys = "Windows 2003";
  } else if (isWin2000) {
    sys = "Windows 2000";
  } else if (isWindows) {
    sys = "Windows";
  } else if (isMac) {
    sys = "Macintosh";
  } else if (isAir) {
    sys = "Adobeair";
  } else if (isLinux) {
    sys = "Linux";
  } else if (isAndroid) {
    sys = "Android";
  } else if (isIpad) {
    sys = "IPad";
  } else if (isIphoneOs) {
    sys = "Iphone";
  } else {
    sys = "其他";
  }
  return sys;
}

// 获取浏览器类型
function getBrowserType() {
  var ua = navigator.userAgent.toLowerCase();
  if (ua == null) return "ie";
  else if (ua.indexOf("chrome") != -1)
    return "c1"; //返回chrome有bug,只能返回代号
  else if (ua.indexOf("opera") != -1) return "opera";
  else if (ua.indexOf("msie") != -1) return "ie";
  else if (ua.indexOf("safari") != -1) return "safari";
  else if (ua.indexOf("firefox") != -1) return "firefox";
  else if (ua.indexOf("gecko") != -1) return "gecko";
  else return "ie";
}
// 获取浏览器版本
function getBrowserVersion() {
  var ua = navigator.userAgent.toLowerCase();
  if (ua == null) return "null";
  else if (ua.indexOf("chrome") != -1)
    return ua.substring(ua.indexOf("chrome") + 7, ua.length).split(" ")[0];
  else if (ua.indexOf("opera") != -1)
    return ua.substring(ua.indexOf("version") + 8, ua.length);
  else if (ua.indexOf("msie") != -1)
    return ua.substring(ua.indexOf("msie") + 5, ua.length - 1).split(";")[0];
  else if (ua.indexOf("safari") != -1)
    return ua.substring(ua.indexOf("safari") + 7, ua.length);
  else if (ua.indexOf("gecko") != -1)
    return ua.substring(ua.indexOf("firefox") + 8, ua.length);
  else return "null";
}

function getBr() {
  var userAgent = navigator.userAgent,
    rMsie = /(msie\s|trident.*rv:)([\w.]+)/,
    rFirefox = /(firefox)\/([\w.]+)/,
    rOpera = /(opera).+version\/([\w.]+)/,
    rChrome = /(chrome)\/([\w.]+)/,
    rSafari = /version\/([\w.]+).*(safari)/;
  var browser;
  var version;
  var ua = userAgent.toLowerCase();
  function uaMatch(ua) {
    var match = rMsie.exec(ua);
    if (match != null) {
      return { browser: "IE", version: match[2] || "0" };
    }
    var match = rFirefox.exec(ua);
    if (match != null) {
      return { browser: match[1] || "", version: match[2] || "0" };
    }
    var match = rOpera.exec(ua);
    if (match != null) {
      return { browser: match[1] || "", version: match[2] || "0" };
    }
    var match = rChrome.exec(ua);
    if (match != null) {
      //chrome返回代号
      return { browser: "c1" || "", version: match[2] || "0" };
    }
    var match = rSafari.exec(ua);
    if (match != null) {
      return { browser: match[2] || "", version: match[1] || "0" };
    }
    if (match != null) {
      return { browser: "", version: "0" };
    }
  }
  var browserMatch = uaMatch(userAgent.toLowerCase());
  if (typeof browserMatch != "undefined" && browserMatch.browser) {
    browser = browserMatch.browser;
    version = browserMatch.version;
  }
  return browser + version;
}
function getLan() {
  var lan = "";
  //ie
  if (
    navigator.browserLanguage != "undefined" &&
    navigator.browserLanguage != null
  ) {
    lan = navigator.systemLanguage;
  }
  //firefox、chrome,360
  else {
    lan = navigator.language;
  }
  return lan.toLowerCase();
}
