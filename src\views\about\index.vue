<template>
  <div id="home">
    <div
      class="home-wrapper"
      style="padding: 0; height: 100vh"
      v-if="!isMobile"
    >
      <n-carousel
        dot-type="line"
        autoplay
        :draggable="bannerList.list && bannerList.list.length > 1"
        :show-dots="false"
      >
        <template v-for="(item, index) in bannerList.list">
          <template v-if="item.type == '2'">
            <video
              :src="item.url"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%"
              :poster="apiUrl + coverImg"
            ></video>
          </template>
          <template v-else-if="item.type == '1'">
            <img class="carousel-img" :src="item.url" />
          </template>
        </template>
      </n-carousel>
      <div class="glass">
        <div class="banner-content">
          <div class="title">{{ titleContext.title }}</div>
          <div class="context" v-html="titleContext.context"></div>
        </div>
        <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div>
      </div>
    </div>
    <div class="home-wrapper" style="padding: 0; height: 50vh" v-else>
      <n-carousel
        dot-type="line"
        autoplay
        :draggable="bannerList.list && bannerList.list.length > 1"
        :show-dots="false"
      >
        <template v-for="(item, index) in bannerList.list">
          <template v-if="item.type == '2'">
            <video
              :src="item.url"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%"
            ></video>
          </template>
          <template v-else-if="item.type == '1'">
            <img class="carousel-img" :src="item.url" />
          </template>
        </template>
      </n-carousel>
      <div class="glass">
        <div class="banner-content">
          <div class="title">{{ titleContext.title }}</div>
          <div class="context" v-html="titleContext.context"></div>
        </div>
        <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div>
      </div>
    </div>
    <div class="home-wrapper">
      <div class="about-box">
        <div class="title">
          {{ aboutList.title }}
        </div>
        <div class="subTitle" v-if="false">
          {{ aboutList.subTitle }}
        </div>

        <div class="about-context">
          {{ aboutList.detail }}
        </div>
        <img :src="aboutList.image" />
      </div>
    </div>
    <div class="home-wrapper-free">
      <div class="advantage-box">
        <div class="title">
          {{ titleContext.wrapper3.title }}
        </div>
        <div class="subTitle" v-if="false">
          {{ titleContext.wrapper3.subTitle }}
        </div>
        <div class="subTitle" v-else></div>
        <div class="main">
          <div class="advantage-item" v-for="(item, index) in advantageList">
            <img :src="item.image" />
            <div class="advantage-glass">
              <!-- <div class="advantage-subTitle">{{ item.subTitle }}</div> -->
              <div class="advantage-title">{{ item.title }}</div>
              <div class="advantage-context">
                <n-tooltip trigger="hover" :style="{ maxWidth: '400rem' }">
                  <template #trigger>
                    {{ item.detail }}
                  </template>
                  {{ item.detail }}
                </n-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home-wrapper" style="height: 50vh">
      <img :src="advertisementList.image" class="advertisement-image" />
      <div class="advertisement-box">
        <div class="advertisement-contain">
          <div class="advertisement-contain-title">
            {{ advertisementList.title }}
          </div>
          <div
            class="advertisement-contain-subTitle"
            v-if="false"
            v-html="advertisementList.subTitle"
          ></div>
          <!-- <div class="line">
              <div class="icon-box">
                <img :src="advertisementList.icon1" />
              </div>
              <div class="tel-text">
                {{ advertisementList.tel }}
              </div>
            </div> -->
          <div class="line">
            <!-- <div class="icon-box">
                <img :src="advertisementList.icon2" />
              </div> -->
            <div class="address-text">
              {{ advertisementList.address }}
            </div>
          </div>
          <!-- <div class="map">
              <iframe
                :src="`https://m.amap.com/navi/?dest=${advertisementList.longitude},${advertisementList.latitude}&destName=${advertisementList.address}&hideRouteIcon=1&key=9e759312e18f22e5370766a3106ebd0d`"
                width="100%"
                height="100%"
                frameborder="0"
              ></iframe>
            </div> -->
        </div>
      </div>
    </div>
    <!-- <div class="placeholder"></div> -->
  </div>
</template>
<script lang="ts" setup>
import { reactive, computed, ref, onMounted, toRefs, nextTick } from "vue";
import {
  getZoneCarousel,
  getModuleConfig,
  dataApplication,
  cooperationConsultation,
} from "/@/api/special/cooperation";

import { getFileType } from "/@/utils/file";
import { useGlobSetting } from "/@/hooks/setting";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const state = reactive({
  classId: {
    topBanner: 11198, //顶部轮播
    aboutList: [11199, 11201], //关于 || 各个模块头部
    advantageList: 11200, //活动
    advertisementList: 11202, //更多合作
  },
  bannerList: {
    list: [],
    img: [],
    video: [],
  },
  titleContext: {
    title: "交流与合作",
    wrapper3: {
      title: "我们的优势",
      subTitle: "Our strengths",
    },
  },
  aboutList: {
    title: "关于我们",
    subTitle: "About Us",
    detail:
      "福建大数据交易所以“立足福建、面向全国、走向海丝”为发展愿景，以“培育壮大数据要素市场，建设数字经济产业生态”为主责主业，为各类市场主体提供数据交易服务。",
    image: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
  },
  advantageList: [
    {
      detail:
        "交通部数据源，数据合规，数源稳定，全量数据，多场景设计应用。依托网络货运信用大数据联合实验室，已接入交通部运政类数据，主机厂数据。覆盖全国轻中重卡，新能源车辆数近900万辆。",
      icon: new URL(`/@/assets/special/eye.png`, import.meta.url).href,
      image: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
      subTitle: "Trading license",
      title: "合规的数据交易牌照优势",
      view: "236点击",
      id: 1,
    },
  ],
  advertisementList: {
    image: new URL(`/@/assets/special/service.png`, import.meta.url).href,
    icon1: new URL(`/@/assets/special/tel.png`, import.meta.url).href,
    icon2: new URL(`/@/assets/special/address.png`, import.meta.url).href,
    title: "更多合作欢迎垂询",
    subTitle: "For more cooperation,<br/>please feel free to inquire",
    tel: "0591-83050001",
    address: "福建省福州市闽侯县 永丰社区杜坞村43号大数据科技园A1座8层",
    longitude: "",
    latitude: "",
  },
});
const {
  classId,
  bannerList,
  titleContext,
  aboutList,
  advantageList,
  advertisementList,
} = toRefs(state);
const coverImg = ref("");

onMounted(() => {
  getBannerList();
  getAboutList();
  getAdvantageList();
  getAdvertisementList();
});
function getBannerList() {
  getZoneCarousel({ classId: classId.value.topBanner }).then((res: any) => {
    state.titleContext.title = res.title;
    state.titleContext.subTitle = res.subTitle;
    state.titleContext.context = res.detail;
    coverImg.value = res.cover;
    let tempArray = [...res.videoUrl, ...res.imgUrls];
    state.bannerList.list = tempArray.map((fileName: string) => {
      return {
        url: apiUrl.value + fileName,
        type: getFileType(fileName),
      };
    });

    if (res.videoUrl && res.videoUrl.length > 0) {
      state.bannerList.videoUrl = [];
      res.videoUrl.forEach((element: string) => {
        state.bannerList.videoUrl.push({
          url: apiUrl.value + element,
        });
      });
    }
    if (res.imgUrls && res.imgUrls.length > 0) {
      state.bannerList.img = [];
      res.imgUrls.forEach((element: string) => {
        state.bannerList.img.push({
          url: apiUrl.value + element,
        });
      });
    }
  });
}
function getAboutList() {
  getModuleConfig({ classId: classId.value.aboutList }).then((res) => {
    let aboutObj = res.find((i) => i.classId == state.classId.aboutList[0]);
    let advantageObj = res.find((i) => i.classId == state.classId.aboutList[1]);
    state.aboutList = aboutObj;
    state.aboutList.image = apiUrl.value + aboutObj.image;
    //
    state.titleContext.wrapper3.title = advantageObj.title;
    state.titleContext.wrapper3.subTitle = advantageObj.subTitle;
  });
}
function getAdvantageList() {
  dataApplication({ classId: classId.value.advantageList }).then((res) => {
    if (res && res.length > 0) {
      state.advantageList = res.map((i) => {
        return {
          ...i,
          image: apiUrl.value + i.logo,
        };
      });
    }
  });
}
function getAdvertisementList() {
  cooperationConsultation({ classId: classId.value.advertisementList }).then(
    (res: any) => {
      state.advertisementList.address = res.ADDRESS;
      state.advertisementList.tel = res.PHONE;
      state.advertisementList.subTitle = res.SUBTITLE;
      state.advertisementList.title = res.TITLE;
      state.advertisementList.image = apiUrl.value + res.image;
      state.advertisementList.image = apiUrl.value + res.image;
      state.advertisementList.latitude = res.latitude;
      state.advertisementList.longitude = res.longitude;
    }
  );
}
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  ::-webkit-scrollbar {
    display: none;
  }
  #home {
    min-width: 1200px;
    .home-wrapper {
      position: relative;
      /* padding-top: calc(99 / 1080 * 100vh); */
      width: 100%;
      /* height: 100vh; */
      padding: 60rem 0 40rem;
    }
    .home-wrapper-free {
      position: relative;
      /* padding: calc(99 / 1080 * 100vh) 0; */
      padding: 60rem 0 40rem;
      width: 100%;
      background: rgba(249, 250, 251, 1);
    }
    .carousel-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: All 0.3s ease-in-out;
    }
    .carousel-img:hover {
      transform: scale(1.04);
    }
    .glass {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      background: linear-gradient(
        to right,
        rgba(13, 46, 153, 0.8) 0%,
        rgba(70, 128, 255, 0) 100%
      );
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        position: absolute;
        bottom: 7%;
        left: 50%;
        transform: translateX(-50%);
        animation: blink 2s infinite;
      }
    }
    .banner-content {
      margin: 270rem 0 0 160rem;
      width: 75%;
      font-size: 20rem;
      letter-spacing: 1px;
      color: rgba(255, 255, 255, 1);
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .context {
        margin-top: 26px;
        line-height: 28rem;
        font-size: 18rem;
        font-weight: 300;
      }
    }
    .about-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      // width: 84%;
      // min-width: 1600px;
      height: calc(100% - calc(99 / 1080 * 100vh));
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        /* line-height: 96rem; */
      }
      & > .subTitle {
        margin-bottom: 26rem;
        height: 47rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .about-context {
        font-size: 24rem;
        /* line-height: 30rem; */
        color: rgba(128, 128, 128, 1);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 24rem;
        font-weight: 300;
        letter-spacing: 1rem;
        width: 1133rem;
        margin-top: 24rem;

        text-wrap: wrap;
      }
      & > img {
        margin-top: 35rem;
        width: 100%;
        height: 595rem;
        transition: All 0.3s ease-in-out;
      }
      & > img:hover {
        transform: scale(1.03);
      }
    }
    .advantage-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      // width: 84%;
      // min-width: 1600px;

      & > .title {
        width: 100%;
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        /* line-height: 104.26rem; */
        text-align: center;
      }
      & > .subTitle {
        width: 100%;
        margin-bottom: 26rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
        text-align: center;
      }
      & > .main {
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 499rem));
        gap: 50rem;
        justify-content: center;
        width: 100%;
        .advantage-item:hover {
          transform: scale(1.04);
          box-shadow: 0 0.25rem 1.25rem #757575;
        }
        .advantage-item {
          position: relative;
          height: 594rem;
          background-color: #fff;
          transition: All 0.3s ease-in-out;
          cursor: default;
          & > img {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            width: 100%;
            height: 100%;
          }
          .advantage-glass {
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 20;
            width: 100%;
            height: 373rem;
            background: linear-gradient(
              360deg,
              rgba(13, 46, 153, 1) 0%,
              rgba(13, 46, 153, 0) 100%
            );
            .advantage-subTitle {
              margin-top: 138rem;
              padding: 0 20rem;
              width: 100%;
              font-size: 24rem;
              font-weight: 300;
              letter-spacing: 0rem;
              line-height: 31.82rem;
              color: rgba(255, 255, 255, 1);
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .advantage-title {
              margin-top: 138rem;
              // margin-top: 8rem;
              padding: 0 20rem;
              width: 100%;
              font-size: 24rem;
              font-weight: 700;
              letter-spacing: 0rem;
              line-height: 31.82rem;
              color: rgba(255, 255, 255, 1);
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .advantage-context {
              margin: 8rem auto 0;
              // width: 73.3%;
              width: 366rem;
              font-size: 18rem;
              font-weight: 300;
              letter-spacing: 1.6rem;
              /* line-height: 21.22rem; */
              color: rgba(255, 255, 255, 1);
              // text-align: center;
              text-align: justify;
              overflow-wrap: break-word;
              hyphens: auto;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 6;
              -webkit-box-orient: vertical;
            }
          }
        }
      }
    }
    .advertisement-image {
      position: absolute;
      right: 0;
      top: 0;
      width: 968rem;
      // width: 50.4%;
      height: 100%;
    }
    .advertisement-box {
      position: relative;
      z-index: 10;
      margin: 0 auto;
      width: 100%;
      // width: calc(100% - 320px);
      // width: 84%;
      // min-width: 1600px;
      height: 100%;

      .advertisement-contain {
        position: relative;
        z-index: 10;
        margin: 0 0 0 160rem;
        padding-top: 65rem;
        width: 620rem;
        // width: 31%;
        height: 100%;
        .advertisement-contain-title {
          width: 100%;
          font-size: 72rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .advertisement-contain-subTitle {
          margin-bottom: 18rem;
          width: 470rem;
          font-size: 36rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 47.74rem;
          color: rgba(128, 128, 128, 1);
        }
        .line {
          display: flex;
          align-items: center;
          margin-top: 26rem;
          width: calc(100% - 192rem);

          .icon-box {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 25rem;
            width: 53rem;
            height: 53rem;
            background: rgba(232, 232, 232, 1);
            border-radius: 50%;
            img {
              width: 28rem;
              height: 28rem;
            }
          }
          .tel-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: rgba(128, 128, 128, 1);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .address-text {
            width: calc(100% - 80rem);
            font-size: 20rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 26.52rem;
            color: rgba(128, 128, 128, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            /* -webkit-line-clamp: 2; */
            -webkit-box-orient: vertical;
          }
        }
        .map {
          margin: 68rem 0 0;
          width: 100%;
          height: 375rem;
          background-color: #e5e5e5;
        }
      }
    }
    .placeholder {
      width: 100%;
      height: 101rem;
    }
  }
}
@media screen and (max-width: 768px) {
  ::-webkit-scrollbar {
    display: none;
  }
  #home {
    .home-wrapper {
      position: relative;
      /* padding-top: calc(99 / 1080 * 100vh); */
      padding: 60rem 0 40rem;
      width: 100%;
      /* height: 100vh; */
    }
    .home-wrapper-free {
      position: relative;
      padding: 60rem 0 40rem;
      width: 100%;
      background: rgba(249, 250, 251, 1);
    }
    .carousel-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      // transition: All 0.3s ease-in-out;
    }
    // .carousel-img:hover {
    //   transform: scale(1.04);
    // }
    .glass {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      background: linear-gradient(
        to right,
        rgba(13, 46, 153, 0.8) 0%,
        rgba(70, 128, 255, 0) 100%
      );
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        position: absolute;
        bottom: 7%;
        left: 50%;
        transform: translateX(-50%);
        animation: blink 2s infinite;
      }
    }
    .banner-content {
      width: 720rem;
      margin: 150rem auto 0;
      font-size: 20rem;
      letter-spacing: 1px;
      color: rgba(255, 255, 255, 1);
      & > .title {
        font-size: 32rem;
        font-weight: 900;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .context {
        margin-top: 6rem;
        line-height: 28rem;
        font-size: 18rem;
        font-weight: 300;
        :deep(p) {
          font-size: 22rem !important;
          span {
            font-size: 22rem !important;
          }
        }
      }
    }
    .about-box {
      position: relative;
      margin: 0 auto;
      padding: 0 16px;
      width: 100%;
      height: calc(100% - calc(99 / 1080 * 100vh));
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 96rem;
        text-align: center;
      }
      & > .subTitle {
        margin-bottom: 26rem;
        height: 47rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .about-context {
        margin-top: 28rem;
        width: 100%;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 1px;
        // line-height: 30rem;
        color: rgba(170, 170, 170, 1);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 8;
        -webkit-box-orient: vertical;
      }
      & > img {
        margin-top: 35rem;
        width: 100%;
        /* height: 300px; */
        transition: All 0.3s ease-in-out;
      }
      & > img:hover {
        transform: scale(1.03);
      }
    }
    .advantage-box {
      position: relative;
      margin: 0 auto;
      padding: 0 16px;
      width: 100%;

      & > .title {
        width: 100%;
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 104.26rem;
        text-align: center;
      }
      & > .subTitle {
        width: 100%;
        margin-bottom: 26rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
        text-align: center;
      }
      & > .main {
        display: grid;
        grid-template-columns: repeat(1, minmax(0, 550rem));
        gap: 50rem;
        justify-content: center;
        width: 100%;
        .advantage-item:hover {
          transform: scale(1.04);
          box-shadow: 0 0.25rem 1.25rem #757575;
        }
        .advantage-item {
          position: relative;
          height: 594rem;
          background-color: #fff;
          transition: All 0.3s ease-in-out;
          cursor: default;
          & > img {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            width: 100%;
            height: 100%;
          }
          .advantage-glass {
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 20;
            width: 100%;
            height: 373rem;
            background: linear-gradient(
              360deg,
              rgba(13, 46, 153, 1) 0%,
              rgba(13, 46, 153, 0) 100%
            );
            .advantage-subTitle {
              margin-top: 138rem;
              padding: 0 20rem;
              width: 100%;
              font-size: 24rem;
              font-weight: 300;
              letter-spacing: 0rem;
              line-height: 31.82rem;
              color: rgba(255, 255, 255, 1);
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .advantage-title {
              margin-top: 138rem;
              // margin-top: 8rem;
              padding: 0 20rem;
              width: 100%;
              font-size: 18px;
              font-weight: 700;
              letter-spacing: 0rem;
              color: rgba(255, 255, 255, 1);
              text-align: center;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .advantage-context {
              margin: 8rem auto 0;
              // width: 73.3%;
              width: 366rem;
              font-size: 12px;
              font-weight: 300;
              letter-spacing: 0;
              color: rgba(255, 255, 255, 1);
              // text-align: center;
              text-align: justify;
              overflow-wrap: break-word;
              hyphens: auto;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 4;
              -webkit-box-orient: vertical;
            }
          }
        }
      }
    }
    .advertisement-image {
      position: absolute;
      right: 0;
      top: 0;
      width: 968rem;
      // width: 50.4%;
      height: 100%;
    }
    .advertisement-box {
      display: flex;
      justify-content: center;
      position: relative;
      z-index: 10;
      margin: 0 auto;
      width: 100%;
      height: 100%;

      .advertisement-contain {
        position: relative;
        z-index: 10;
        // margin: 0 0 0 160rem;
        // padding-top: 129rem;
        width: 620rem;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        .advertisement-contain-title {
          width: 100%;
          font-size: 60rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(256, 256, 256, 1);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .advertisement-contain-subTitle {
          margin-bottom: 18rem;
          width: 470rem;
          font-size: 36rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 47.74rem;
          color: rgba(256, 256, 256, 1);
        }
        .line {
          display: flex;
          align-items: center;
          margin-top: 26rem;
          width: 100%;

          .icon-box {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 25rem;
            width: 53rem;
            height: 53rem;
            background: rgba(232, 232, 232, 1);
            border-radius: 50%;
            img {
              width: 28rem;
              height: 28rem;
            }
          }
          .tel-text {
            width: calc(100% - 80rem);
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 1rem;
            // line-height: 31.82rem;
            // color: rgba(256, 256, 256, 1);
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .address-text {
            width: calc(100% - 80rem);
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 1rem;
            // line-height: 26.52rem;
            // color: rgba(128, 128, 128, 1);
            color: #ffffff;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
        .map {
          margin: 68rem 0 0;
          width: 100%;
          height: 612rem;
          background-color: #e5e5e5;
        }
      }
    }
    .placeholder {
      width: 100%;
      height: 101rem;
    }
  }
}
</style>
