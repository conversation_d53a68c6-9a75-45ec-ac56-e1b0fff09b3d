<template>
  <div id="build_detail">
    <div class="detail_1">
      <div class="detail_1_top"></div>
      <div class="detail_1_top_bread">
        <n-breadcrumb separator=">">
          <n-breadcrumb-item @click="goPage('/')">首页</n-breadcrumb-item>
          <n-breadcrumb-item @click="goPage('/buildGarden')">
            党建园地</n-breadcrumb-item
          >
          <n-breadcrumb-item> 党建详情</n-breadcrumb-item>
        </n-breadcrumb>
      </div>
      <div class="detail_1_content">
        <div class="content_title">{{ detail.partyName }}</div>
        <div class="content_author">
          <span>发布时间： {{ detail.addTime }}</span>
          <span>来源单位：{{ detail.partySource }}</span>
          <span>浏览量：{{ detail.views }}次</span>
        </div>
        <div class="content_text" v-html="detail.partyContent"></div>
      </div>
      <div class="detail_1_footer">
        <div class="footer_left" @click="getData(down)">
          <span style="text-align: left; cursor: pointer"><上一篇</span>
          <span style="color: #999999"> {{ up.partyName }}</span>
        </div>
        <div class="footer_left" @click="getData(up)">
          <span style="text-align: right; cursor: pointer">下一篇></span>
          <span style="color: #999999"> {{ down.partyName }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getDetail } from "/@/api/buildingGarden/index";
import { collectUserVisitInfoAndSendToServer } from "/@/utils/visit.js";
import { useGlobSetting } from "/@/hooks/setting";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const route = useRoute();
const router = useRouter();
const detail = ref({});
const up = ref({});
const down = ref({});
onMounted(() => {
  getData();
});
function getData(e) {
  let contentID = 0;
  if (e) {
    contentID = e.contentId;
  } else {
    contentID = route.query.contentId;
  }
  let params = {
    contentId: contentID,
  };
  collectUserVisitInfoAndSendToServer(params);
  getDetail(params).then((res) => {
    detail.value = res.news;
    up.value = res.up;
    down.value = res.down;
    const regex = /src="\/local\//;
    if (regex.test(detail.value.partyContent)) {
      detail.value.partyContent = detail.value.partyContent.replace(
        /src="\/local\//g,
        `src="${apiUrl.value}/local/`
      );
    }
  });
}
function goPage(path) {
  router.push(path);
}
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  #build_detail {
    min-width: 1600rem;
    .detail_1 {
      position: relative;
      padding-bottom: 40rem;
      background: #fff8f8;
      .detail_1_top {
        background: url(../../assets/build/build_detail.png);
        height: 60vh;
        background-size: cover;
      }
      .detail_1_top_bread {
        width: 1600rem;
        margin: -180rem auto 0;
      }
      .detail_1_content {
        margin: 0 auto 0;
        width: 1600rem;
        /* height: 1694rem; */
        border: 1rem solid #f0f0f0;
        background: #ffffff;
        .content_title {
          /* height: 30rem; */
          font-weight: 500;
          font-size: 30rem;
          color: #000000;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 68rem 52rem 0;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow: hidden;
          text-align: center;
        }
        .content_author {
          width: 1000rem;
          height: 16rem;
          font-weight: 400;
          font-size: 16rem;
          color: #999999;
          line-height: 16rem;
          margin: 42rem auto 0;
          display: flex;
          justify-content: space-between;
        }
        .content_text {
          margin: 108rem 52rem 0;
          font-weight: 400;
          font-size: 16rem;
          color: #000000;
          line-height: 30rem;
          overflow: auto;
          /* height: 83%; */
        }
        /* .content_footer {
        width: 1290rem;
        height: 105rem;
        background: #ffffff;
        border: 1rem solid #f0f0f0;
      } */
      }
      .detail_1_footer {
        width: 1600rem;
        height: 105rem;
        border: 1rem solid #f0f0f0;
        margin: 20rem auto 0;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        padding: 0 18rem;
        justify-content: space-between;
        .footer_left {
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #build_detail {
    min-width: 100%;
    .detail_1 {
      position: relative;
      padding-bottom: 40rem;
      background: #fff8f8;
      .detail_1_top {
        background: url(../../assets/build/build_detail.png);
        height: 50vh;
        background-size: cover;
      }
      .detail_1_top_bread {
        width: 720rem;
        margin: -180rem auto 0;
      }
      .detail_1_content {
        margin: 0 auto 0;
        width: 720rem;
        /* height: 1694rem; */
        border: 1rem solid #f0f0f0;
        background: #ffffff;
        .content_title {
          /* height: 30rem; */
          font-weight: 500;
          font-size: 30rem;
          color: #000000;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 68rem 52rem 0;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow: hidden;
          text-align: center;
        }
        .content_author {
          width: 635rem;
          height: 16rem;
          font-weight: 400;
          font-size: 16rem;
          color: #999999;
          line-height: 16rem;
          margin: 42rem auto 0;
          display: flex;
          justify-content: space-between;
        }
        .content_text {
          margin: 108rem 52rem 0;
          font-weight: 400;
          font-size: 16rem;
          color: #000000;
          line-height: 30rem;
          overflow: auto;
          /* height: 83%; */
          :deep(img) {
            width: 100% !important;
          }
        }
        /* .content_footer {
        width: 1290rem;
        height: 105rem;
        background: #ffffff;
        border: 1rem solid #f0f0f0;
      } */
      }
      .detail_1_footer {
        width: 720rem;
        height: 105rem;
        border: 1rem solid #f0f0f0;
        margin: 20rem auto 0;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        padding: 0 18rem;
        justify-content: space-between;
        .footer_left {
          display: flex;
          flex-direction: column;
          width: 48%;
          height: 100%;
          span {
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
      }
    }
  }
}
</style>
