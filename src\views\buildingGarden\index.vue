<template>
  <div id="build">
    <template v-if="!isMobile">
      <div class="build_box build_box_1">
        <n-carousel
          ref="carousel"
          draggable
          autoplay
          dot-type="line"
          class="build_box_1_carousel"
        >
          <n-carousel-item
            class="n-carousel-item"
            v-if="bannerList.video && bannerList.video.length > 0"
            v-for="(item, index) in bannerList.video"
            style="width: 100%; height: 100%"
          >
            <div class="build_box_1_item">
              <video
                :src="apiUrl + item"
                autoplay
                loop
                muted
                style="width: 100%; height: 100%; object-fit: fill"
                :poster="apiUrl + coverImg"
              ></video>
            </div>
          </n-carousel-item>
          <n-carousel-item
            class="n-carousel-item"
            v-else
            v-for="(item, index) in bannerList.images"
            style="width: 100%; height: 100%"
          >
            <div class="build_box_1_item">
              <img :src="apiUrl + item" alt="" />
            </div>
          </n-carousel-item>
          <template #dots="{ total, currentIndex, to }">
            <ul class="custom-dots">
              <li
                v-for="index of total"
                :key="index"
                :class="{ ['is-active']: currentIndex === index - 1 }"
                @click="to(index - 1)"
              />
            </ul>
          </template>
        </n-carousel>
        <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div>
      </div>
      <div class="build_box build_box_2">
        <div class="card">
          <div class="left" @click="goDetail(recMsg)">
            <div style="position: relative; width: 100%; height: 100%">
              <div class="recommend">推荐</div>
              <img
                :src="apiUrl + recMsg.partyPicture"
                alt=""
                v-if="recMsg.partyPicture"
              />
              <img src="../../assets/build/build_2_3.png" alt="" v-else />
            </div>
            <div class="footer">
              <div class="name">{{ recMsg.partyName }}</div>
              <div class="arrow">
                <img src="../../assets/build/arrow.png" alt="" />
              </div>
            </div>
          </div>
          <div class="right">
            <div
              class="detail"
              v-for="item in headerList"
              @click="goDetail(item)"
            >
              <div class="dot"></div>
              <div class="date">
                {{ item.addTime.split("-")[1] }}-{{
                  item.addTime.split("-")[2].split(" ")[0]
                }}
                <div class="year">{{ item.addTime.split("-")[0] }}</div>
              </div>
              <div class="content dynamicText">
                <div class="inTitle">{{ item.partyName }}</div>
                <div class="inText dynamicText">{{ item.detail }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="build_2_header">
          <img src="../../assets/build/head.png" alt="" />
        </div>
        <div class="mesExpress">
          <div
            class="mesCard dynamicUp"
            v-for="item in mesList"
            @click="goDetail(item)"
          >
            <div style="height: 214rem">
              <img
                v-if="item.partyPicture"
                :src="apiUrl + item.partyPicture"
                alt=""
                style="height: 214rem; width: 378rem"
              />
              <img
                v-else
                :src="mesImg"
                alt=""
                style="height: 214rem; width: 378rem"
              />
            </div>
            <div style="padding: 16rem 0 0 22rem; margin: 0">
              <div class="mesTitle">{{ item.partyName }}</div>
              <div class="mesText">{{ item.detail }}</div>
              <div class="mesLine"></div>
              <div class="mesDate">{{ item.addTime }}</div>
            </div>
          </div>
        </div>
        <div class="pages">
          <n-pagination
            :page-count="pageCount"
            v-model:page="page"
            @update:page="getData()"
          >
            <template #prev> 上一页 </template>
            <template #next> 下一页 </template>
          </n-pagination>
        </div>
        <div class="bird"></div>
        <div class="build_2_footer">
          <img
            src="../../assets/build/build_2_footer.png"
            alt=""
            style="width: 100%"
          />
        </div>
      </div>
    </template>
    <template v-else>
      <div class="wrapper">
        <div class="banner">
          <n-carousel
            ref="carousel"
            draggable
            autoplay
            dot-type="line"
            class="build_box_1_carousel"
          >
            <n-carousel-item
              class="n-carousel-item"
              v-if="bannerList.video && bannerList.video.length > 0"
              v-for="(item, index) in bannerList.video"
              style="width: 100%; height: 100%"
            >
              <div class="build_box_1_item">
                <video
                  :src="apiUrl + item"
                  autoplay
                  loop
                  muted
                  style="width: 100%; height: 100%; object-fit: fill"
                ></video>
              </div>
            </n-carousel-item>
            <n-carousel-item
              class="n-carousel-item"
              v-else
              v-for="(item, index) in bannerList.images"
              style="width: 100%; height: 100%"
            >
              <div class="build_box_1_item">
                <img :src="apiUrl + item" alt="" />
              </div>
            </n-carousel-item>
            <template #dots="{ total, currentIndex, to }">
              <ul class="custom-dots">
                <li
                  v-for="index of total"
                  :key="index"
                  :class="{ ['is-active']: currentIndex === index - 1 }"
                  @click="to(index - 1)"
                />
              </ul>
            </template>
          </n-carousel>
        </div>
        <div class="main">
          <div class="headLine">
            <div class="font" @click="goDetail(recMsg)">
              <div class="recommend">推荐</div>
              <img
                :src="
                  recMsg.partyPicture
                    ? apiUrl + recMsg.partyPicture
                    : headLineImg
                "
              />
              <div class="bar">
                <div class="font-title">{{ recMsg.partyName }}</div>
                <div class="arrow"></div>
              </div>
            </div>
            <div class="sub">
              <div
                class="sub-item"
                v-for="item in headerList"
                @click="goDetail(item)"
              >
                <div class="date">
                  <div class="date-top">
                    <div class="dot"></div>
                    {{ item.addTime.split("-")[1] }}-{{
                      item.addTime.split("-")[2].split(" ")[0]
                    }}
                  </div>
                  <div class="date-bottom">
                    <div class="year">{{ item.addTime.split("-")[0] }}</div>
                  </div>
                </div>
                <div class="sub-item-content">
                  <div class="sub-item-inTitle">{{ item.partyName }}</div>
                  <div class="sub-item-inText">{{ item.detail }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="break"></div>
          <div class="wrapper">
            <div
              class="newsItem"
              v-for="item in mesList"
              @click="goDetail(item)"
            >
              <img
                class="newsImg"
                :src="item.partyPicture ? apiUrl + item.partyPicture : mesImg"
              />
              <div class="newsTitle">
                {{ item.partyName }}
              </div>
              <div class="newsContext">
                {{ item.detail }}
              </div>
              <div class="line"></div>
              <div class="newsTime">{{ item.addTime }}</div>
            </div>
            <div
              class="loadMore"
              v-if="hasNextPage"
              @click="getMoreDataOnPhone"
            >
              加载更多
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import mesImg from "/@/assets/build/mesImg.png";
import headLineImg from "/@/assets/build/build_2_3.png";
import { getHeader, getList } from "/@/api/buildingGarden/index";
import { useGlobSetting } from "/@/hooks/setting";
import { getBg } from "/@/api/home/<USER>";
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const mesList = ref([]);
const headerList = ref([]);
const router = useRouter();
const page = ref(1);
const pageSize = ref(8);
const hasNextPage = ref(true);
const recMsg = ref({});
const pageCount = ref(1);
const bannerList = reactive({
  video: [],
  images: [],
});
const coverImg = ref("");
function goDetail(e) {
  router.push({ name: "buildDetail", query: { contentId: e.contentId } });
}
function getHeaderList() {
  getHeader().then((res) => {
    res.forEach((e) => {
      if (e.isRec == "1") {
        recMsg.value = e;
      } else {
        headerList.value.push(e);
      }
    });
  });
}
function getData() {
  let params = {
    page: page.value,
    pageSize: pageSize.value,
  };
  getList(params).then((res) => {
    mesList.value = res.bottomList;
    hasNextPage.value = res.pageInfo.hasNext;
    mesList.value.forEach((item) => {
      item.detail = item.detail.split(" ").join("");
    });
  });
}
function getMoreDataOnPhone() {
  page.value += 1;
  getList({
    page: page.value,
    pageSize: pageSize.value,
  }).then((res) => {
    mesList.value.push(...res.bottomList);
    hasNextPage.value = res.pageInfo.hasNext;
    pageCount.value = res.pageInfo.pageCount;
    mesList.value.forEach((item) => {
      item.detail = item.detail.split(" ").join("");
    });
  });
}
function getBgs() {
  let params = {
    classId: 11126,
  };
  getBg(params).then((res) => {
    bannerList.images = res.imgUrls;
    bannerList.video = res.videoUrl;
    coverImg.value = res.cover;
  });
}

onMounted(() => {
  getHeaderList();
  getData();
  getBgs();
});
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  #build {
    .build_box {
      height: 100vh;
      width: 100%;
      position: relative;
      :deep(.n-pagination-item) {
        background: #ffffff;
        border-radius: 2rem 2rem 2rem 2rem;
        border: 1rem solid #e6e6e6;
        padding: 15rem;
      }
      :deep(.n-pagination-item):hover {
        color: #005ced;
      }
      :deep(.n-pagination-item--active) {
        background: #005ced;
        border-radius: 2rem 2rem 2rem 2rem;
        color: #ffffff;
      }
    }
    .build_box_1 {
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      .build_box_1_carousel {
        height: 100%;
        position: relative;
        .build_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        bottom: 7%;
        left: 50%;
        position: absolute;
        background: url("../../assets/down.png");
        animation: blink 2s infinite;
        transform: translateX(50%);
      }
    }
    .build_box_2 {
      /* height: 220vh; */
      height: 100%;
      display: flex;
      flex-direction: column;
      background: url(../../assets/build/build_3.png);
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      padding-top: 45rem;
      .card {
        width: 1600rem;
        height: 539rem;
        background: rgba(255, 255, 255, 0.82);
        box-shadow: 0rem 0rem 22rem 0rem rgba(204, 24, 4, 0.33);
        border-radius: 12rem;
        margin: 0 auto;
        display: flex;
        z-index: 9;
        .left {
          width: 60%;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
          .recommend {
            font-weight: 500;
            font-size: 20rem;
            color: #ffffff;
            position: absolute;
            z-index: 999;
            top: 10rem;
            left: 20rem;
          }
          .footer {
            max-width: 100%;
            height: 104rem;
            background: linear-gradient(
              0deg,
              rgba(0, 0, 0, 0.52),
              rgba(0, 0, 0, 0)
            );
            position: absolute;
            bottom: 0;
            display: flex;
            justify-content: space-between;
            padding: 0 40rem;
            align-items: center;
            .name {
              font-weight: bold;
              font-size: 20rem;
              width: 60%;
              color: #ffffff;
              line-height: 32rem;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
          .content {
            margin-left: 56rem;
            width: 72%;
            .inTitle {
              width: 100%;
              height: 25rem;
              line-height: 23rem;
              overflow: hidden;
              font-weight: bold;
              font-size: 20rem;
              color: #444444;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .inText {
              font-weight: 400;
              font-size: 18rem;
              color: #888888;
              line-height: 28rem;
              margin-top: 14rem;
              width: 100%;
              -webkit-line-clamp: 1;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow: hidden;
            }
          }
        }
        .right {
          width: 40%;
          height: 100%;
          overflow: hidden;
          .detail {
            height: 20%;
            display: flex;
            align-items: center;
            .dot {
              width: 14rem;
              height: 14rem;
              background: #e82706;
              border-radius: 50%;
              margin: 0 20rem;
            }
            .date {
              font-weight: bold;
              font-size: 20rem;
              color: #666666;
              line-height: 32rem;
              .year {
                width: 60rem;
                height: 26rem;
                border-radius: 4rem;
                border: 1rem solid #999999;
                font-weight: 400;
                font-size: 14rem;
                color: #888888;
                line-height: 32rem;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
            .content {
              margin-left: 56rem;
              width: 72%;
              .inTitle {
                width: 100%;
                height: 25rem;
                line-height: 23rem;
                overflow: hidden;
                font-weight: bold;
                font-size: 20rem;
                color: #444444;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              .inText {
                font-weight: 400;
                font-size: 18rem;
                color: #888888;
                line-height: 28rem;
                margin-top: 14rem;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
                -webkit-line-clamp: 1;
                text-overflow: ellipsis;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
              }
            }
          }
        }
      }
      .build_2_header {
        width: 1297rem;
        height: 58rem;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 50rem auto 0;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .mesExpress {
        width: 1600rem;
        /* height: 900rem; */
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        margin: 85rem auto 0;
        :nth-child(4n + 2) {
          margin: 0 25rem;
        }
        :nth-child(4n + 3) {
          margin-right: 25rem;
        }
        .mesCard {
          width: 380rem;
          /* height: 390rem; */
          background: rgba(255, 255, 255, 0.82);
          box-shadow: 0rem 0rem 22rem 0rem rgba(204, 24, 4, 0.33);
          border: 1rem solid #f9f9f9;
          margin-top: 25rem;
          .mesTitle {
            font-weight: bold;
            font-size: 18rem;
            color: #323232;
            /* line-height: 18rem; */
            width: 95%;
            -webkit-line-clamp: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            text-wrap: nowrap;
            /* display: -webkit-box; */
          }
          .mesText {
            font-weight: 400;
            font-size: 18rem;
            color: #969696;
            line-height: 26rem;
            height: 78rem;
            width: 95%;
            overflow: hidden;
            margin: 18rem 0 0;
            -webkit-line-clamp: 3;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden;
          }
          .mesLine {
            width: 349rem;
            height: 1rem;
            background: #e6e6e6;
            margin: 17rem 0 0 0;
          }
          .mesDate {
            font-weight: 400;
            font-size: 14rem;
            color: #969696;
            line-height: 14rem;
            margin: 16rem 0;
          }
        }
      }
      .pages {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20rem 0 140rem;
      }
      .bird {
        background: url(../../assets/build/bird.png);
        width: 583rem;
        height: 329rem;
        position: absolute;
        top: 180rem;
        right: 7rem;
        z-index: -1;
      }
      .build_2_footer {
        width: 100%;
        height: 197rem;
        position: absolute;
        bottom: 0;
      }
    }
  }
}
// 移动端
@media screen and (max-width: 768px) {
  .wrapper {
    padding: 75px 0 0px;
    .banner {
      width: 100%;
      height: 290rem;
      .build_box_1_carousel {
        height: 100%;
        position: relative;
        .build_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
    }
    .main {
      padding: 0 30rem;
      width: 100%;
      background: url(../../assets/build/build_3.png) no-repeat center / 100%
        100%;
      .headLine {
        margin-top: 29rem;
        margin-bottom: 35rem;
        width: 100%;
        box-shadow: 0px 0px 9px 0px rgba(204, 24, 4, 0.33);
        border-radius: 5px;
        .font {
          position: relative;
          width: 100%;
          height: 400rem;
          .recommend {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 67rem;
            height: 35rem;
            line-height: 35rem;
            text-align: center;
            background: #e82706;
            border-radius: 5rem;
            color: #fff;
            font-size: 12px;
          }
          & > img {
            position: absolute;
            top: 0;
            width: 100%;
            height: 100%;
          }
          .bar {
            position: absolute;
            bottom: 10rem;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 0 15rem;
            .font-title {
              width: calc(100% - 50rem);
              color: #fff;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .arrow {
              width: 20rem;
              height: 17rem;
              background: url("../../assets/build/arrow.png") no-repeat
                center/100% 100%;
            }
          }
        }
        .sub {
          padding: 28rem 20rem 0 20rem;
          background: rgba(255, 255, 255, 0.7);
          .sub-item {
            margin-bottom: 33rem;
            display: flex;

            .date {
              /* width: 80rem; */
              font-weight: bold;
              font-size: 12px;
              color: #666666;
              .date-top {
                display: flex;
                align-items: center;
                .dot {
                  margin-right: 10rem;
                  width: 10rem;
                  height: 10rem;
                  background: #e82706;
                  border-radius: 50%;
                }
              }
              .date-bottom {
                padding-left: 20rem;
                .year {
                  margin-top: 18rem;
                  width: 100%;
                  height: 27px;
                  border-radius: 2px;
                  border: 0px solid #999999;
                  text-align: center;
                }
              }
            }
            .sub-item-content {
              width: 86%;
              .sub-item-inTitle {
                padding-left: 24rem;
                width: 100%;
                font-weight: bold;
                font-size: 15px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .sub-item-inText {
                padding-left: 24rem;
                width: 100%;
                font-size: 14px;
                color: #888888;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
      .break {
        width: 100%;
        height: 43rem;
        background: url("../../assets/build/head.png") no-repeat center center /
          100% 100%;
      }
      .wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0px 0 20px;
        .newsItem {
          margin-top: 16px;
          padding-bottom: 28rem;
          width: 690rem;
          background: #ffffff;
          box-shadow: 0px 0px 9px 0px rgba(13, 46, 153, 0.33);
          border: 1px solid #f9f9f9;
          .newsImg {
            display: block;
            width: 100%;
            height: 389rem;
          }
          .newsTitle {
            padding: 30rem 16rem 0;
            font-weight: bold;
            font-size: 16px;
            color: #323232;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .newsContext {
            margin-bottom: 20rem;
            padding: 22rem 30rem 0rem 16rem;
            font-weight: 400;
            font-size: 14px;
            color: #969696;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }
          .line {
            margin: 0 auto 26rem;
            width: 667rem;
            height: 1px;
            background: #e6e6e6;
          }
          .newsTime {
            padding: 0 16rem;
            font-size: 12px;
            color: #969696;
          }
        }
        .loadMore {
          padding-top: 20px;
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}
</style>
