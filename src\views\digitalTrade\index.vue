<template>
  <div id="trade">
    <div class="trade trade_box_1 dynamic">
      <n-carousel
        ref="carousel"
        effect="slide"
        draggable
        keyboard
        autoplay
        class="trade_box_1_carousel"
        :show-dots="true"
      >
        <n-carousel-item
          class="n-carousel-item"
          v-if="trade_1_videoUrl.length != 0"
          v-for="(item, index) in trade_1_videoUrl"
          style="width: 100%; height: 100%"
        >
          <div class="trade_box_1_item">
            <video
              :src="apiUrl + item"
              autoplay
              loop
              muted
              style="width: 100%; height: 100%"
              :poster="apiUrl + coverImg"
            ></video>
          </div>
        </n-carousel-item>
        <n-carousel-item
          class="n-carousel-item"
          v-else
          v-for="(item, index) in trade_1_imgUrls"
          style="width: 100%; height: 100%"
        >
          <div class="trade_box_1_item">
            <img :src="apiUrl + item" alt="" />
          </div>
        </n-carousel-item>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              @click="to(index - 1)"
            />
          </ul>
        </template>
      </n-carousel>
      <div class="toDown">
        <img src="../../assets/special/down.png" alt="" />
      </div>
      <div class="trade_box_1_header">
        <div class="trade_box_1_glass">
          <div class="trade_box_1_header_title">
            {{ trade_1_title }}
          </div>
          <div class="trade_box_1_header_context" v-html="trade_1_detail"></div>
        </div>
      </div>
    </div>
    <div class="trade trade_box_2">
      <div class="trade_box_top">
        <div>
          <div class="trade_title">{{ trade_2_msg.title }}33</div>
          <div class="trade_title_1" style="color: #aaa">
            {{ trade_2_msg.subTitle }}
          </div>
        </div>
        <div class="trade_detail">{{ trade_2_msg.detail }}</div>
      </div>
      <div class="trade_box_2_container">
        <div class="container_left">
          <div v-for="item in trade_2_left_list" class="fItem">
            <div class="fRight">
              <img :src="apiUrl + item.imageUrl" alt="" />
            </div>
            <div class="flTitle">{{ item.TITLE }}</div>
            <div class="flText">{{ item.INTRO }}</div>
          </div>
        </div>
        <div class="container_right">
          <div class="advantage-swiper">
            <swiper
              @swiper="setSwiper"
              @slideChange="onSlideChange"
              :direction="'vertical'"
              :spaceBetween="10"
              :autoplay="{
                delay: 3000,
                disableOnInteraction: false,
              }"
              :modules="modules"
              class="mySwiper"
            >
              <swiper-slide
                v-for="(item, index) in trade_2_right_list"
                @click="goPage(item.KIPURL)"
              >
                <div class="ad-swiper-img dynamic">
                  <img :src="item.url" style="width: 100%" />
                </div>
                <div class="swiper_title">{{ item.title }}</div>
                <div class="swiper_text">
                  <n-tooltip trigger="hover" :style="{ maxWidth: '500rem' }">
                    <template #trigger>
                      {{ item.text }}
                    </template>
                    {{ item.text }}
                  </n-tooltip>
                </div>
              </swiper-slide>
            </swiper>
            <div class="swiper_btn">
              <n-button
                color="#0D2E99"
                round
                v-if="btnConfig.URL1"
                @click="goPage(btnConfig.URL1)"
              >
                {{ btnConfig.BUTTON1 }}
              </n-button>
              <n-button
                color="#0D2E99"
                round
                style="margin-left: 46rem"
                v-if="btnConfig.URL2"
                @click="goPage(btnConfig.URL2)"
              >
                {{ btnConfig.BUTTON2 }}
              </n-button>
            </div>
          </div>
          <div class="advantage-select">
            <div
              class="advantage-select-item"
              :class="item.active == true ? 'active' : ''"
              v-for="(item, index) in trade_2_right_list"
              @click="changeAdSwiper(index)"
            >
              <img :src="item.url" />
              <div class="ad-select-glass">
                <div style="width: 80%">
                  <div class="ad-select-text">{{ item.title }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="trade trade_box_3">
      <div class="trade_box_top">
        <div>
          <div class="trade_title">{{ trade_3_msg.title }}</div>
          <div class="trade_title_1" style="color: #aaa">
            {{ trade_3_msg.subTitle }}
          </div>
        </div>
        <div class="trade_detail">{{ trade_3_msg.detail }}</div>
      </div>
      <div class="trade_box_3_content">
        <div
          class="trade_box_3_item"
          v-for="item in trade_3_list"
          @click="goPage(item.skipUrl)"
        >
          <div class="item_img">
            <img :src="item.imageUrl" alt="" />
          </div>
          <div class="item_container">
            <div class="container_title">
              <n-tooltip trigger="hover" :style="{ maxWidth: '500rem' }">
                <template #trigger>
                  {{ item.title }}
                </template>
                {{ item.title }}
              </n-tooltip>
            </div>
            <div class="container_detail">
              <n-tooltip trigger="hover" :style="{ maxWidth: '500rem' }">
                <template #trigger>
                  {{ item.intro }}
                </template>
                {{ item.intro }}
              </n-tooltip>
            </div>
            <!-- <div class="container_footer">
              <div class="prod-view">
                <img :src="item.icon" class="prod-view-eye" />
                <div class="prod-view-text">
                  {{ item.clickNum + "点击" }}
                </div>
              </div>
              <div class="prod-arrow-box">
                <img :src="item.rightIcon" alt="" />
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="trade trade_box_4">
      <div class="trade_box_top">
        <div>
          <div class="trade_title">{{ trade_4_msg.title }}</div>
          <div class="trade_title_1" style="color: #aaa">
            {{ trade_4_msg.subTitle }}
          </div>
        </div>
        <div class="trade_detail">{{ trade_4_msg.detail }}</div>
      </div>
      <div class="trade_4_subTitle">
        <div class="subTitle">{{ trade_4_title[0]?.title }}</div>
        <div class="trade_more dynamicMore" @click="goPage('/special/public')">
          More ——
        </div>
      </div>
      <div class="trade_4_content">
        <div
          class="prod-item"
          v-for="(item, index) in trade_4_list.firstList"
          @click="goPage(item.skipUrl)"
        >
          <div class="prod-info">
            <div class="title">
              <n-tooltip trigger="hover" :style="{ maxWidth: '500rem' }">
                <template #trigger>
                  {{ item.title }}
                </template>
                {{ item.title }}
              </n-tooltip>
            </div>
            <div class="context">
              <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                <template #trigger>
                  {{ item.intro }}
                </template>
                {{ item.intro }}
              </n-tooltip>
            </div>
            <div class="prod-tag">
              <div class="prod-tag-item" v-for="i in item.labels">
                {{ i }}
              </div>
            </div>
            <!-- <div class="prod-view">
              <img :src="item.icon" class="prod-view-eye" />
              <div class="prod-view-text">
                {{ item.clickNum + "点击" }}
              </div>
            </div> -->
          </div>
          <div class="prod-img">
            <div class="prod-arrow-box">
              <img :src="item.rightIcon" alt="" />
            </div>
            <img :src="item.imageUrl" />
          </div>
        </div>
      </div>
      <div class="trade_4_subTitle">
        <div class="subTitle">{{ trade_4_title[1]?.title }}</div>
        <div
          class="trade_more dynamicMore"
          @click="goPage('https://trade.fjbdex.com/ltywpt/yunData')"
        >
          More ——
        </div>
      </div>
      <div class="trade_4_content">
        <div
          class="prod-item"
          v-for="(item, index) in trade_4_list.secondList"
          @click="goPage(item.skipUrl)"
        >
          <div class="prod-info">
            <div class="title">
              <n-tooltip trigger="hover" :style="{ maxWidth: '500rem' }">
                <template #trigger>
                  {{ item.title }}
                </template>
                {{ item.title }}
              </n-tooltip>
            </div>
            <div class="context">
              <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                <template #trigger>
                  {{ item.intro }}
                </template>
                {{ item.intro }}
              </n-tooltip>
            </div>
            <div class="prod-tag">
              <div class="prod-tag-item" v-for="i in item.labels">
                {{ i }}
              </div>
            </div>
            <!-- <div class="prod-view">
              <img :src="item.icon" class="prod-view-eye" />
              <div class="prod-view-text">
                {{ item.clickNum + "点击" }}
              </div>
            </div> -->
          </div>
          <div class="prod-img">
            <div class="prod-arrow-box">
              <img :src="item.rightIcon" alt="" />
            </div>
            <img :src="item.imageUrl" />
          </div>
        </div>
      </div>
    </div>
    <div class="trade trade_box_5">
      <div class="trade_box_top">
        <div>
          <div class="trade_title">{{ trade_5_msg.title }}</div>
          <div class="trade_title_1" style="color: #aaa">
            {{ trade_4_msg.subTitle }}
          </div>
        </div>
        <!-- <div class="trade_detail">{{ trade_5_msg.detail }}</div> -->
      </div>
      <div class="trade_5_content">
        <div class="trade_5_item" v-for="item in trade_5_list">
          <div class="item_img">
            <img :src="apiUrl + item.imageUrl" alt="" />
          </div>
          <div class="item_title">{{ item.TITLE }}</div>
        </div>
      </div>
    </div>
    <div class="trade trade_box_6">
      <div class="trade_box_top">
        <div>
          <div class="trade_title">{{ trade_6_msg.title }}</div>
          <div class="trade_title_1" style="color: #aaa">
            {{ trade_4_msg.subTitle }}
          </div>
        </div>
        <div class="trade_detail">{{ trade_5_msg.detail }}</div>
      </div>
      <div class="trade_6_content" v-if="!isMobile">
        <div class="swiper-button-prev swiper-button-next1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-prev1">
          <img :src="arrowR" />
        </div>
        <swiper
          :slides-per-view="4"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :slidesPerGroup="4"
          class="swiperRef"
          :modules="modules"
        >
          <swiper-slide class="trade_6_card" v-for="item in trade_6_list">
            <div
              class="home_box_2_item dynamic"
              @click="goCreateDetail(item.contentId)"
            >
              <img :src="apiUrl + item.logo" alt="" />
              <div class="home_box_2_text">
                <div class="title">{{ item.title }}</div>
                <div class="fTitle">{{ item.subTitle }}</div>
                <div class="Hbar"></div>
                <div class="con">
                  <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                    <template #trigger>
                      {{ item.detail }}
                    </template>
                    {{ item.detail }}
                  </n-tooltip>
                </div>
                <!-- <div class="Hbottom">
                  <img
                    :src="toRight"
                    alt=""
                    style="width: 60rem; height: 9rem"
                  />
                </div> -->
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="trade_6_content" v-else>
        <!-- <div class="swiper-button-prev swiper-button-next1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-prev1">
          <img :src="arrowR" />
        </div> -->
        <swiper
          :slides-per-view="2"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :slidesPerGroup="2"
          class="swiperRef"
          :modules="modules"
        >
          <swiper-slide class="trade_6_card" v-for="item in trade_6_list">
            <div
              class="home_box_2_item"
              @click="goCreateDetail(item.contentId)"
            >
              <img :src="apiUrl + item.logo" alt="" />
              <div class="home_box_2_text">
                <div class="title">{{ item.title }}</div>
                <div class="fTitle">{{ item.subTitle }}</div>
                <div class="Hbar"></div>
                <div class="con">
                  <n-tooltip trigger="click" :style="{ maxWidth: '300rem' }">
                    <template #trigger>
                      {{ item.detail }}
                    </template>
                    {{ item.detail }}
                  </n-tooltip>
                </div>
                <!-- <div class="Hbottom">
                  <img
                    :src="toRight"
                    alt=""
                    style="width: 60rem; height: 9rem"
                  />
                </div> -->
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import {
  getBg,
  getConfig,
  getAdvantage,
  getApplication,
  getBtnLink,
} from "/@/api/home/<USER>";
import { useGlobSetting } from "/@/hooks/setting";
import { getProductTypeList } from "/@/api/special/traffic";
import arrowL from "/@/assets/home/<USER>";
import arrowR from "/@/assets/home/<USER>";
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";
import "swiper/css/navigation";
import { Autoplay, Navigation } from "swiper/modules";
import toRight from "/@/assets/home/<USER>";
const modules = [Autoplay, Navigation];
const useSwiper = ref(null);
import { useRouter } from "vue-router";
const router = useRouter();
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const trade_1_videoUrl = ref([]);
const trade_1_imgUrls = ref([]);
const trade_1_title = ref("");
const trade_1_detail = ref("");
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const trade_2_msg = ref({});
const trade_3_msg = ref({});
const trade_4_msg = ref({});
const trade_5_msg = ref({});
const trade_6_msg = ref({});
const trade_2_left_list = ref([]);
const trade_2_right_list = ref([]);
const trade_3_list = ref([]);
const trade_4_list = ref({
  firstList: [],
  secondList: [],
});
const trade_5_list = ref([]);
const trade_6_list = ref([]);
const trade_4_title = ref([]);
const selectedCase = ref({});
const btnConfig = ref({});
const coverImg = ref("");
const setSwiper = (swiper) => {
  useSwiper.value = swiper;
};
const onSlideChange = (swiper) => {
  trade_2_right_list.value.forEach((item) => {
    item.active = false;
  });
  console.log(
    "onSlideChange",
    trade_2_right_list.value[0].active,
    swiper.realIndex
  );
  trade_2_right_list.value[swiper.realIndex].active = true;
};
onMounted(() => {
  getboxBg();
  getTitleConfig();
  getAdvLeft();
  getAdvRight();
  getDataPro();
  getSpecialPro();
  getAirPro();
  getDataService();
  getPlatformFeatures();
  getBtn();
});
function getboxBg() {
  let params = {
    classId: 11139,
  };
  getBg(params).then((res) => {
    trade_1_imgUrls.value = res.imgUrls;
    trade_1_videoUrl.value = res.videoUrl;
    trade_1_title.value = res.title;
    trade_1_detail.value = res.detail;
    coverImg.value = res.cover;
  });
}
function getTitleConfig() {
  let params = {
    classId: ["11141", "11143", "11145", "11149", "11151", "11152", "11153"],
  };
  getConfig(params).then((res) => {
    res.forEach((e) => {
      if (e.classId == 11141) {
        trade_2_msg.value = e;
      } else if (e.classId == 11143) {
        trade_3_msg.value = e;
      } else if (e.classId == 11145) {
        trade_4_msg.value = e;
      } else if (e.classId == 11149) {
        trade_5_msg.value = e;
      } else if (e.classId == 11151) {
        trade_6_msg.value = e;
      } else if (e.classId == 11152) {
        trade_4_title.value.push(e);
      } else if (e.classId == 11153) {
        trade_4_title.value.push(e);
      }
    });
  });
}
function getAdvLeft() {
  let params = {
    classId: 11154,
  };
  getAdvantage(params).then((res) => {
    trade_2_left_list.value = res;
  });
}
function getAdvRight() {
  let params = {
    classId: 11140,
  };
  getAdvantage(params).then((res) => {
    trade_2_right_list.value = res;
    trade_2_right_list.value = res.map((i) => {
      return {
        text: i.INTRO,
        url: apiUrl.value + i.imageUrl,
        active: false,
        title: i.TITLE,
        id: i.CONTENTID,
        KIPURL: i.KIPURL,
      };
    });
    trade_2_right_list.value[0].active = true;
  });
}
function getDataPro() {
  let params = {
    classId: 11142,
  };
  getProductTypeList(params).then((res) => {
    // trade_3_list.value = res[0];
    trade_3_list.value = res[0].map((i) => {
      return {
        ...i,
        imageUrl: apiUrl.value + i.imageUrl,
        icon: new URL(`/@/assets/special/eye.png`, import.meta.url).href,
        rightIcon: new URL(`/@/assets/home/<USER>
      };
    });
  });
}
function getSpecialPro() {
  let params = {
    classId: 11146,
  };
  getProductTypeList(params).then((res) => {
    trade_4_list.value.firstList = res[0].map((i) => {
      return {
        ...i,
        imageUrl: apiUrl.value + i.imageUrl,
        icon: new URL(`/@/assets/special/eye.png`, import.meta.url).href,
        rightIcon: new URL(`/@/assets/home/<USER>
      };
    });
  });
}
function getAirPro() {
  let params = {
    classId: 11147,
  };
  getProductTypeList(params).then((res) => {
    trade_4_list.value.secondList = res[0].map((i) => {
      return {
        ...i,
        imageUrl: apiUrl.value + i.imageUrl,
        icon: new URL(`/@/assets/special/eye.png`, import.meta.url).href,
        rightIcon: new URL(`/@/assets/home/<USER>
      };
    });
  });
}
function getDataService() {
  let params = {
    classId: 11148,
  };
  getAdvantage(params).then((res) => {
    trade_5_list.value = res;
  });
}
function getPlatformFeatures() {
  let params = {
    classId: 11150,
  };
  getApplication(params).then((res) => {
    trade_6_list.value = res;
  });
}

function changeAdSwiper(num) {
  useSwiper.value.slideTo(num);
}
function goPage(e) {
  console.log(e);
  const regex = /(https?)/;
  if (regex.test(e)) {
    window.open(unescapeHTML(e));
  } else {
    router.push(e);
    if (e.includes("/special")) {
      localStorage.setItem("pagePath", 11038);
    }
  }
}

// function goPage(path: string, outer: boolean = false) {
//   if (outer && path) {
//     window.open(unescapeHTML(path), "_blank");
//     return;
//   } else {
//     router.push(unescapeHTML(path));

//   }
// }
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
function getBtn() {
  let params = {
    classId: 11155,
  };
  getBtnLink(params).then((res) => {
    btnConfig.value = res;
  });
}
</script>
<style lang="less" scoped>
@media screen and (min-width: 769px) {
  #trade {
    font-family: MiSans;
    .trade_box_top {
      display: flex;
      flex-direction: column;
      width: 1600rem;
      margin: 60rem auto 0;
    }
    .trade_title {
      /* line-height: 104rem; */
      color: rgba(56, 56, 56, 1);
      font-size: 50rem !important;
      font-weight: 900;
      margin-bottom: 8rem;
      width: 100%;
    }
    .trade_title_1 {
      font-size: 36rem;
      font-weight: 500;
      line-height: 47.74rem;
      color: rgba(128, 128, 128, 1);
      width: 100%;
    }
    .trade_detail {
      font-size: 24rem;
      font-weight: 300;
      letter-spacing: 1rem;
      /* line-height: 30rem; */
      width: 100%;
      overflow: hidden;
      margin-top: 24rem;
      color: rgba(128, 128, 128, 1);
      width: 1175rem;
      text-overflow: ellipsis;
      text-wrap: wrap;
    }
    .trade_more {
      /* position: absolute;
    right: 0;
    top: 0; */
      width: 130rem;
      height: 28rem;
      font-size: 16rem;
      font-weight: 500;
      line-height: 28rem;
      color: rgba(40, 41, 56, 1);
    }
    .trade_box_1 {
      height: 100vh;
      width: 100%;
      position: relative;
      .trade_box_1_carousel {
        height: 100%;
        .trade_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
      .trade_box_1_header {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        display: flex;
        flex-direction: column;
        .trade_box_1_glass {
          width: 75%;
          margin: 270rem 0 0 160rem;
          .trade_box_1_header_title {
            /* width: 344rem; */
            /* height: 80rem; */
            font-size: 50rem;
            font-weight: 700;
            /* line-height: 80rem; */
            color: rgba(255, 255, 255, 1);
            margin: auto;
            align-items: center;
          }
          .trade_box_1_header_context {
            margin-top: 26rem;
            line-height: 28rem;
            font-size: 20rem;
            font-weight: 300;
            color: #ffffff;
          }
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        bottom: 7%;
        left: 50%;
        position: absolute;
        background: url("../../assets/down.png");
        animation: blink 2s infinite;
        transform: translateX(50%);
      }
    }
    .trade_box_2 {
      display: flex;
      flex-direction: column;
      background: rgba(249, 250, 251, 1);
      .trade_box_2_container {
        width: 1600rem;
        margin: 30rem auto 0;
        display: flex;
        justify-content: space-between;
        .container_left {
          width: 31%;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          .fItem {
            display: flex;
            flex-direction: column;
            width: 40%;
            align-items: center;
            margin-bottom: 68rem;
            .fRight {
              width: 138rem;
              height: 138rem;
              border-radius: 50%;
              background-color: rgba(255, 255, 255, 1);
              box-shadow: 0px 7px 12px rgba(14, 31, 53, 0.08);
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 80rem;
                height: 80rem;
              }
            }
          }
          .flTitle {
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 32rem;
            color: rgba(128, 128, 128, 1);
            text-align: center;
            margin-top: 12rem;
          }
          .flText {
            font-size: 48rem;
            font-weight: 700;
            letter-spacing: 0;
            line-height: 64rem;
            color: rgba(56, 56, 56, 1);
            text-align: center;
          }
        }
        .container_right {
          width: 65%;
          display: flex;
          height: 810rem;
          justify-content: space-between;
          /* position: relative; */
          .advantage-swiper {
            width: 82%;
            /* height: 686px; */
            height: 80%;
            .mySwiper {
              width: 95%;
              height: 100%;
              :deep(.swiper-slide-active) {
                width: 100% !important;
                height: 100% !important;
              }
            }
            .ad-swiper-img {
              display: block;
              width: 100%;
              height: 68%;
              overflow: hidden;
              object-fit: cover;
              border-radius: 10rem;
              transition: All 0.3s ease-in-out;
            }
            .swiper_title {
              font-size: 40rem;
              font-weight: 700;
              letter-spacing: 0;
              color: rgba(56, 56, 56, 1);
            }
            .swiper_text {
              font-size: 18px;
              font-weight: 400;
              letter-spacing: 0px;
              color: rgba(85, 85, 85, 0.8);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            .swiper_btn {
              margin-top: 55rem;
              display: flex;
              flex-direction: row;
              :deep(.n-button) {
                --n-font-size: 30rem !important;
                font-weight: 700 !important;
                letter-spacing: 0px;
                padding: 16rem 70rem;
                --n-height: auto !important;
              }
            }
          }
          .advantage-select {
            width: 183rem;
            max-height: 100%;
            overflow-y: auto;
            .advantage-select-item {
              position: relative;
              margin: 0rem auto 0;
              width: 100%;
              height: 135rem;
              & > img {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                margin: auto auto;
                width: 164rem;
                height: 112rem;
              }
              .ad-select-glass {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                margin: auto auto;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 164rem;
                height: 112rem;
                pointer-events: none;
                background: rgba(13, 46, 153, 0.6);
                .ad-select-text {
                  margin-top: calc(4 / 1080 * 100vh);
                  width: 100%;
                  font-size: 18rem;
                  font-weight: 500;
                  letter-spacing: 0rem;
                  line-height: 23.87rem;
                  color: rgba(255, 255, 255, 1);
                  text-align: center;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  vertical-align: top;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  display: -webkit-box;
                }
              }
            }
            .advantage-select-item.active {
              border-radius: 6rem;
              border: 4rem solid rgba(204, 207, 219, 1);
            }
          }
        }
      }
    }
    .trade_box_3 {
      .trade_box_3_content {
        width: 1600rem;
        margin: 30rem auto 40rem;
        display: grid;
        grid-template-columns: repeat(3, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        display: flex;
        flex-wrap: wrap;
        .trade_box_3_item {
          width: 511rem;
          height: 568rem;
          /* margin-right: 41rem; */
          border: 1px solid rgba(224, 224, 224, 1);
          .item_img {
            width: 100%;
            height: 302rem;

            img {
              width: 100%;
              height: 100%;
            }
          }
          .item_container {
            padding: 40rem;
            .container_title {
              font-size: 36rem;
              font-weight: 700 !important;
              letter-spacing: 0.2rem;
              color: rgba(37, 43, 66, 1);
              line-height: 36rem;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              :deep(span) {
                font-weight: 700 !important;
              }
            }
            .container_detail {
              font-size: 18rem;
              font-weight: 400;
              height: 90rem;
              letter-spacing: 0.2rem;
              color: rgba(85, 85, 85, 1);
              margin-top: 20rem;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            }
            .container_footer {
              display: flex;
              justify-content: space-between;
              margin-top: 20rem;
              .prod-view {
                display: flex;
                align-items: center;
                width: 100%;
                .prod-view-eye {
                  margin-right: 17rem;
                  width: 32.02rem;
                  height: 28.08rem;
                }
                .prod-view-text {
                  font-size: 18rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
              .prod-arrow-box {
                box-sizing: border-box;
                // padding: 0 24;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 115.8rem;
                height: 46.13rem;
                opacity: 1;
                border-radius: 60rem;
                background: rgba(13, 46, 153, 1);
                text-align: center;
                img {
                  width: 67.07rem;
                  height: 8.02rem;
                  object-fit: cover;
                }
              }
            }
          }
        }
      }
    }
    .trade_box_4 {
      background: rgba(250, 250, 250, 1);
      margin-top: 50rem;
      display: flex;
      flex-direction: column;
      .trade_4_subTitle {
        width: 1600rem;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 40rem;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 64rem;
        color: rgba(56, 56, 56, 1);
        .subTitle {
          font-size: 40rem;
          font-weight: 700;
          letter-spacing: 0px;
          line-height: 64rem;
          color: rgba(56, 56, 56, 1);
        }
      }
      .trade_4_content {
        display: grid;
        grid-template-columns: repeat(2, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        width: 1600rem;
        margin: 40rem auto;
        height: 100%;
        .prod-item:hover {
          transform: translate(0, -10rem);
          box-shadow: 0 0.25rem 1.25rem #d4d4d4;
        }
        .prod-item {
          display: flex;
          justify-content: space-between;
          height: 344rem;
          background: rgba(255, 255, 255, 1);
          cursor: pointer;
          transition: All 0.3s ease-in-out;
          .prod-info {
            padding: 52rem 0 0 53rem;
            width: 390rem;
            height: 100%;
            overflow: hidden;
            .title {
              width: 100%;
              font-size: 32rem;
              font-weight: 700 !important;
              letter-spacing: 0.2rem;
              line-height: 40rem;
              color: rgba(37, 43, 66, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              :deep(span) {
                font-weight: 700 !important;
              }
            }
            .context {
              margin-top: 19rem;
              width: 100%;
              font-size: 18rem;
              font-weight: 400;
              letter-spacing: 0.2rem;
              line-height: 26rem;
              color: rgba(85, 85, 85, 1);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            }
            .prod-tag {
              display: flex;
              justify-content: space-evenly;
              margin-top: 20rem;
              width: 100%;
              height: 45rem;
              .prod-tag-item {
                width: 140rem;
                height: 45rem;
                line-height: 45rem;
                font-size: 16rem;
                font-weight: 400;
                text-align: center;
                border-radius: 60rem;
                background: rgba(245, 245, 245, 1);
                border: 1rem solid rgba(235, 235, 235, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            .prod-view {
              display: flex;
              align-items: center;
              margin-top: 40rem;
              width: 100%;
              .prod-view-eye {
                margin-right: 17rem;
                width: 32.02rem;
                height: 28.08rem;
              }
              .prod-view-text {
                font-size: 14rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
          .prod-img {
            position: relative;
            width: 337rem;
            height: 100%;
            .prod-arrow-box {
              box-sizing: border-box;
              position: absolute;
              bottom: 24rem;
              // padding: 0 24;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 115.8rem;
              height: 46.13rem;
              opacity: 1;
              border-radius: 60rem;
              background: rgba(13, 46, 153, 1);
              transform: translateX(-50%);
              text-align: center;
              img {
                width: 67.07rem;
                height: 8.02rem;
                object-fit: cover;
              }
            }
            & > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }
    }
    .trade_box_5 {
      display: flex;
      flex-direction: column;
      .trade_5_content {
        width: 1600rem;
        margin: 60rem auto 40rem;
        display: grid;
        grid-template-columns: repeat(4, minmax(0rem, 1fr));
        gap: 50rem;
        justify-content: center;
        .trade_5_item {
          display: flex;
          flex-direction: column;
          align-items: center;
          .item_img {
            width: 178rem;
            height: 178rem;
            border-radius: 53px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(
              226.12deg,
              rgba(13, 46, 153, 1) 0%,
              rgba(57, 159, 227, 1) 100%
            );
            box-shadow: 0px 14px 24px rgba(18, 60, 162, 0.33);
            img {
              width: 70%;
              height: 70%;
            }
          }
          .item_title {
            margin-top: 32rem;
            font-size: 32rem;
            font-weight: 700;
            letter-spacing: 0;
            line-height: 48rem;
            color: rgba(56, 56, 56, 1);
          }
        }
      }
    }
  }
  .trade_box_6 {
    background: #fafafa;
    display: flex;
    flex-direction: column;
    .trade_6_content {
      width: 1600rem;
      margin: 30rem auto 40rem;
      :deep(.swiper) {
        display: flex;
        overflow: hidden;
        width: 100%;
        .swiper-wrapper {
          display: flex;
        }
      }
      :deep(.swiper-button-prev1) {
        width: 68rem;
        height: 68rem;
        border-radius: 34rem;
        left: -80rem;
        transform: rotate(180deg);
        background: rgba(237, 237, 237, 0.6);
      }
      :deep(.swiper-button-next1) {
        width: 68rem;
        height: 68rem;
        border-radius: 34rem;
        right: -80rem;
        left: auto;
        transform: rotate(180deg);
        background: #f6f6f6;
      }
      .swiper-button-prev:after,
      .swiper-button-next:after {
        font-family: "";
      }
      .swiper-button-next:after {
        content: "";
      }
      .swiper-button-prev:after {
        content: "";
      }
      .trade_6_card {
        width: calc(370 / 1600 * 100%) !important;
        height: 100%;
        /* margin-right: 40rem; */
        .home_box_2_item {
          height: 100%;
          width: 100%;
          position: relative;
          overflow: hidden;
          border-radius: 40rem;
          transition: transform 1s;
          img {
            width: 100%;
            height: 100%;
            object-fit: fill;
            transition: transform 1s;
          }
          .home_box_2_text {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 464rem;
            /* opacity: 0.8; */

            .title {
              margin-top: 125rem;
              line-height: 47.74rem;
              color: #ffffff;
              font-size: 36rem;
              font-weight: 700;
              text-align: center;
              vertical-align: top;
            }
            .fTitle {
              opacity: 1;
              font-size: 14rem;
              font-weight: 300;
              letter-spacing: 2rem;
              line-height: 19rem;
              color: #ffffff;
              text-align: center;
              vertical-align: top;
            }
            .Hbar {
              width: 80rem;
              height: 8rem;
              background: #ffffff;
              margin: 16rem auto 0;
            }
            .con {
              width: 300rem;
              /* height: 52rem;
              font-size: 18rem; */
              font-weight: 300;
              font-size: 20rem;
              /* line-height: 18rem; */
              color: #ffffff;
              text-align: left;
              vertical-align: top;
              margin: 8rem auto 0;
              -webkit-line-clamp: 6;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow: hidden;
            }
            .Hbottom {
              width: 104rem;
              height: 54rem;
              border-radius: 60rem;
              border: 1rem solid #ffffff;
              margin: 20rem auto 0;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
          img {
            height: 100%;
            width: 100%;
          }
        }
        &:nth-child(n + 1) {
          margin-right: 34rem;
        }
      }

      .home_box_2_card:hover {
        cursor: pointer;
        img {
          transform: scale(1.5); /* 放大图片两倍 */
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #trade {
    font-family: MiSans;
    .trade_box_top {
      display: flex;
      flex-direction: column;
      width: 720rem;
      margin: 60rem auto 0;
    }
    .trade_title {
      /* line-height: 104rem; */
      color: rgba(56, 56, 56, 1);
      font-size: 50rem !important;
      font-weight: 900;
      margin-bottom: 8rem;
      width: 100%;
    }
    .trade_title_1 {
      font-size: 36rem;
      font-weight: 500;
      line-height: 47.74rem;
      color: rgba(128, 128, 128, 1);
      width: 100%;
    }
    .trade_detail {
      font-size: 24rem;
      font-weight: 300;
      letter-spacing: 1rem;
      /* line-height: 30rem; */
      width: 100%;
      overflow: hidden;
      margin-top: 24rem;
      color: rgba(128, 128, 128, 1);
      /* width: 1133rem; */
      text-overflow: ellipsis;
      text-wrap: wrap;
    }
    .trade_more {
      /* position: absolute;
    right: 0;
    top: 0; */
      width: 130rem;
      height: 28rem;
      font-size: 16rem;
      font-weight: 500;
      line-height: 28rem;
      color: rgba(40, 41, 56, 1);
    }
    .trade_box_1 {
      height: 50vh;
      width: 100%;
      position: relative;
      .trade_box_1_carousel {
        height: 100%;
        .trade_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
      .trade_box_1_header {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        display: flex;
        flex-direction: column;
        .trade_box_1_glass {
          width: 720rem;
          margin: 170rem auto 0;
          .trade_box_1_header_title {
            /* width: 344rem; */
            height: 80rem;
            font-size: 60rem;
            font-weight: 700;
            line-height: 80rem;
            color: rgba(255, 255, 255, 1);
            margin: auto;
            align-items: center;
          }
          .trade_box_1_header_context {
            margin-top: 6rem;
            line-height: 28rem;
            font-size: 20rem;
            font-weight: 300;
            color: #ffffff;
            :deep(p) {
              font-size: 22rem !important;
              span {
                font-size: 22rem !important;
              }
            }
          }
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        bottom: 7%;
        left: 50%;
        position: absolute;
        background: url("../../assets/down.png");
        animation: blink 2s infinite;
        transform: translateX(-50%);
      }
    }
    .trade_box_2 {
      display: flex;
      flex-direction: column;
      background: rgba(249, 250, 251, 1);
      .trade_box_2_container {
        width: 720rem;
        margin: 30rem auto 0;
        display: flex;
        flex-direction: column;
        .container_left {
          width: 100%;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          .fItem {
            display: flex;
            flex-direction: column;
            width: 50%;
            align-items: center;
            margin-bottom: 68rem;
            .fRight {
              width: 138rem;
              height: 138rem;
              border-radius: 50%;
              background-color: rgba(255, 255, 255, 1);
              box-shadow: 0px 7px 12px rgba(14, 31, 53, 0.08);
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 80rem;
                height: 80rem;
              }
            }
          }
          .flTitle {
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 32rem;
            color: rgba(128, 128, 128, 1);
            text-align: center;
            margin-top: 12rem;
          }
          .flText {
            font-size: 48rem;
            font-weight: 700;
            letter-spacing: 0;
            line-height: 64rem;
            color: rgba(56, 56, 56, 1);
            text-align: center;
          }
        }
        .container_right {
          width: 100%;
          display: flex;
          height: 675rem;
          justify-content: space-between;
          /* position: relative; */
          .advantage-swiper {
            width: 75%;
            /* height: 686px; */
            height: 75%;
            .mySwiper {
              width: 95%;
              height: 100%;
              :deep(.swiper-slide-active) {
                width: 100% !important;
                height: 100% !important;
              }
            }
            .ad-swiper-img {
              display: block;
              width: 100%;
              height: 280rem;
              overflow: hidden;
              object-fit: cover;
              border-radius: 10rem;
              transition: All 0.3s ease-in-out;
            }
            .swiper_title {
              font-size: 40rem;
              font-weight: 700;
              letter-spacing: 0;
              color: rgba(56, 56, 56, 1);
            }
            .swiper_text {
              font-size: 18px;
              font-weight: 400;
              letter-spacing: 0px;
              color: rgba(85, 85, 85, 0.8);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            .swiper_btn {
              margin-top: 55rem;
              display: flex;
              justify-content: space-between;
              :deep(.n-button) {
                --n-font-size: 30rem !important;
                font-weight: 700 !important;
                letter-spacing: 0px;
                padding: 16rem 50rem;
                --n-height: auto !important;
              }
            }
          }
          .advantage-select {
            width: 183rem;
            max-height: 100%;
            overflow-y: auto;
            .advantage-select-item {
              position: relative;
              margin: 0rem auto 0;
              width: 100%;
              height: 135rem;
              & > img {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                margin: auto auto;
                width: 164rem;
                height: 112rem;
              }
              .ad-select-glass {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                margin: auto auto;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 164rem;
                height: 112rem;
                pointer-events: none;
                background: rgba(13, 46, 153, 0.6);
                .ad-select-text {
                  margin-top: calc(4 / 1080 * 100vh);
                  width: 100%;
                  font-size: 18rem;
                  font-weight: 500;
                  letter-spacing: 0rem;
                  line-height: 23.87rem;
                  color: rgba(255, 255, 255, 1);
                  text-align: center;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  vertical-align: top;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  display: -webkit-box;
                }
              }
            }
            .advantage-select-item.active {
              border-radius: 6rem;
              border: 4rem solid rgba(204, 207, 219, 1);
            }
          }
        }
      }
    }
    .trade_box_3 {
      .trade_box_3_content {
        width: 720rem;
        margin: 30rem auto 40rem;
        display: grid;
        grid-template-columns: repeat(1, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        display: flex;
        flex-wrap: wrap;
        .trade_box_3_item {
          width: 720rem;
          /* height: 800rem; */
          /* margin-right: 41rem; */
          border: 1px solid rgba(224, 224, 224, 1);
          .item_img {
            width: 100%;
            height: 377rem;

            img {
              width: 100%;
              height: 100%;
            }
          }
          .item_container {
            padding: 40rem;
            .container_title {
              font-size: 36rem;
              font-weight: 700;
              letter-spacing: 0.2rem;
              color: rgba(37, 43, 66, 1);
              line-height: 36rem;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
            .container_detail {
              font-size: 24rem;
              font-weight: 400;
              /* height: 90rem; */
              letter-spacing: 0.2rem;
              color: rgba(85, 85, 85, 1);
              margin-top: 20rem;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            }
            .container_footer {
              display: flex;
              justify-content: space-between;
              margin-top: 20rem;
              .prod-view {
                display: flex;
                align-items: center;
                width: 100%;
                .prod-view-eye {
                  margin-right: 17rem;
                  width: 32.02rem;
                  height: 28.08rem;
                }
                .prod-view-text {
                  font-size: 18rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
              .prod-arrow-box {
                box-sizing: border-box;
                // padding: 0 24;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 115.8rem;
                height: 46.13rem;
                opacity: 1;
                border-radius: 60rem;
                background: rgba(13, 46, 153, 1);
                text-align: center;
                img {
                  width: 67.07rem;
                  height: 8.02rem;
                  object-fit: cover;
                }
              }
            }
          }
        }
      }
    }
    .trade_box_4 {
      background: rgba(250, 250, 250, 1);
      margin-top: 50rem;
      display: flex;
      flex-direction: column;
      .trade_4_subTitle {
        width: 720rem;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 40rem;
        font-weight: 700;
        letter-spacing: 0px;
        line-height: 64rem;
        color: rgba(56, 56, 56, 1);
        .subTitle {
          font-size: 40rem;
          font-weight: 700;
          letter-spacing: 0px;
          line-height: 64rem;
          color: rgba(56, 56, 56, 1);
        }
      }
      .trade_4_content {
        display: grid;
        grid-template-columns: repeat(1, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        width: 720rem;
        margin: 40rem auto;
        height: 100%;
        .prod-item:hover {
          transform: translate(0, -10rem);
          box-shadow: 0 0.25rem 1.25rem #d4d4d4;
        }
        .prod-item {
          display: flex;
          justify-content: space-between;
          height: 344rem;
          background: rgba(255, 255, 255, 1);
          cursor: pointer;
          transition: All 0.3s ease-in-out;
          .prod-info {
            padding: 52rem 0 0 53rem;
            width: calc(347 / 790 * 100%);
            height: 100%;
            overflow: hidden;
            .title {
              width: 100%;
              font-size: 32rem;
              font-weight: 700;
              letter-spacing: 0.2rem;
              line-height: 40rem;
              color: rgba(37, 43, 66, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .context {
              margin-top: 19rem;
              width: 100%;
              font-size: 24rem;
              font-weight: 400;
              letter-spacing: 0.2rem;
              /* line-height: 26rem; */
              color: rgba(85, 85, 85, 1);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            }
            .prod-tag {
              display: flex;
              justify-content: space-evenly;
              margin-top: 20rem;
              width: 100%;
              height: 45rem;
              .prod-tag-item {
                width: 120rem;
                height: 45rem;
                line-height: 45rem;
                font-size: 16rem;
                font-weight: 400;
                text-align: center;
                border-radius: 60rem;
                background: rgba(245, 245, 245, 1);
                border: 1rem solid rgba(235, 235, 235, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            .prod-view {
              display: flex;
              align-items: center;
              margin-top: 40rem;
              width: 100%;
              .prod-view-eye {
                margin-right: 17rem;
                width: 32.02rem;
                height: 28.08rem;
              }
              .prod-view-text {
                font-size: 14rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
          .prod-img {
            position: relative;
            width: 337rem;
            height: 100%;
            .prod-arrow-box {
              box-sizing: border-box;
              position: absolute;
              bottom: 24rem;
              // padding: 0 24;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 115.8rem;
              height: 46.13rem;
              opacity: 1;
              border-radius: 60rem;
              background: rgba(13, 46, 153, 1);
              transform: translateX(-50%);
              text-align: center;
              img {
                width: 67.07rem;
                height: 8.02rem;
                object-fit: cover;
              }
            }
            & > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }
    }
    .trade_box_5 {
      display: flex;
      flex-direction: column;
      .trade_5_content {
        width: 720rem;
        margin: 60rem auto 40rem;
        display: grid;
        grid-template-columns: repeat(2, minmax(0rem, 1fr));
        gap: 50rem;
        justify-content: center;
        .trade_5_item {
          display: flex;
          flex-direction: column;
          align-items: center;
          .item_img {
            width: 178rem;
            height: 178rem;
            border-radius: 53rem;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(
              226.12deg,
              rgba(13, 46, 153, 1) 0%,
              rgba(57, 159, 227, 1) 100%
            );
            box-shadow: 0px 14px 24px rgba(18, 60, 162, 0.33);
            img {
              width: 80%;
              height: 80%;
            }
          }
          .item_title {
            margin-top: 32rem;
            font-size: 32rem;
            font-weight: 700;
            letter-spacing: 0;
            line-height: 48rem;
            color: rgba(56, 56, 56, 1);
          }
        }
      }
    }
  }
  .trade_box_6 {
    background: #fafafa;
    display: flex;
    flex-direction: column;
    .trade_6_content {
      width: 720rem;
      margin: 30rem auto 40rem;
      :deep(.swiper) {
        display: flex;
        overflow: hidden;
        width: 100%;
        .swiper-wrapper {
          display: flex;
        }
      }
      :deep(.swiper-button-prev1) {
        width: 68rem;
        height: 68rem;
        border-radius: 34rem;
        left: -80rem;
        transform: rotate(180deg);
        background: rgba(237, 237, 237, 0.6);
      }
      :deep(.swiper-button-next1) {
        width: 68rem;
        height: 68rem;
        border-radius: 34rem;
        right: -80rem;
        left: auto;
        transform: rotate(180deg);
        background: #f6f6f6;
      }
      .swiper-button-prev:after,
      .swiper-button-next:after {
        font-family: "";
      }
      .swiper-button-next:after {
        content: "";
      }
      .swiper-button-prev:after {
        content: "";
      }
      .trade_6_card {
        width: calc(360 / 780 * 100%) !important;
        height: 100%;
        /* margin-right: 40rem; */
        .home_box_2_item {
          height: 100%;
          width: 100%;
          position: relative;
          overflow: hidden;
          border-radius: 40rem;
          transition: transform 1s;
          img {
            width: 100%;
            height: 100%;
            object-fit: fill;
            transition: transform 1s;
          }
          .home_box_2_text {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 464rem;
            /* opacity: 0.8; */

            .title {
              margin-top: 125rem;
              line-height: 47.74rem;
              color: #ffffff;
              font-size: 36rem;
              font-weight: 700;
              text-align: center;
              vertical-align: top;
            }
            .fTitle {
              opacity: 1;
              font-size: 14rem;
              font-weight: 300;
              letter-spacing: 2rem;
              line-height: 19rem;
              color: #ffffff;
              text-align: center;
              vertical-align: top;
            }
            .Hbar {
              width: 80rem;
              height: 8rem;
              background: #ffffff;
              margin: 16rem auto 0;
            }
            .con {
              width: 300rem;
              /* height: 52rem;
              font-size: 18rem; */
              font-weight: 300;
              font-size: 20rem;
              /* line-height: 18rem; */
              color: #ffffff;
              text-align: left;
              vertical-align: top;
              margin: 8rem auto 0;
              -webkit-line-clamp: 6;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow: hidden;
            }
            .Hbottom {
              width: 104rem;
              height: 54rem;
              border-radius: 60rem;
              border: 1rem solid #ffffff;
              margin: 20rem auto 0;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
          img {
            height: 100%;
            width: 100%;
          }
        }
        &:nth-child(n + 1) {
          margin-right: 32rem;
        }
      }

      .home_box_2_card:hover {
        cursor: pointer;
        img {
          transform: scale(1.5); /* 放大图片两倍 */
        }
      }
    }
  }
}
</style>
