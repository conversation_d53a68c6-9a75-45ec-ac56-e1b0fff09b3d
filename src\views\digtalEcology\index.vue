<template>
  <div id="digtal">
    <div class="digtal_box digtal_box_1 dynamic">
      <n-carousel
        ref="carousel"
        effect="slide"
        draggable
        keyboard
        autoplay
        class="digtal_box_1_carousel"
        :onUpdateCurrentIndex="onUpdate"
        :show-dots="true"
      >
        <n-carousel-item
          class="n-carousel-item"
          v-if="digtal_1_videoUrl.length != 0"
          v-for="(item, index) in digtal_1_videoUrl"
          style="width: 100%; height: 100%"
        >
          <div class="digtal_box_1_item">
            <video
              :src="apiUrl + item"
              autoplay
              loop
              muted
              style="width: 100%; height: 100%"
              :poster="apiUrl + coverImg"
            ></video>
          </div>
        </n-carousel-item>
        <n-carousel-item
          class="n-carousel-item"
          v-else
          v-for="(item, index) in digtal_1_imgUrls"
          style="width: 100%; height: 100%"
        >
          <div class="digtal_box_1_item">
            <img :src="apiUrl + item" alt="" />
          </div>
        </n-carousel-item>
      </n-carousel>
      <div class="toDown">
        <img src="../../assets/special/down.png" alt="" />
      </div>
      <div class="digtal_box_1_header">
        <div class="digtal_box_1_glass">
          <div class="digtal_box_1_header_title">{{ digtal_1_title }}</div>
          <div
            class="digtal_box_1_header_context"
            v-html="digtal_1_detail"
          ></div>
        </div>
      </div>
    </div>
    <div class="digtal_box digtal_box_2">
      <div class="digtal_box_2_content">
        <div class="digtal_box_2_left">
          <div class="home_title" style="margin-bottom: 15rem">
            {{ digtal_2_msg.title }}
          </div>
          <div class="home_title_1">
            {{ digtal_2_msg.subTitle }}
          </div>
          <div class="home_detail" style="overflow-wrap: break-word">
            {{ digtal_2_msg.detail }}
          </div>
          <div class="left_footer">
            <div v-for="item in digtal_2_list" class="fItem">
              <div class="fRight">
                <img :src="apiUrl + item.imageUrl" alt="" />
              </div>
              <div class="fLeft">
                <div class="flTitle">{{ item.TITLE }}</div>
                <div class="flText">{{ item.INTRO }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="digtal_box_2_right dynamic">
          <img :src="apiUrl + digtal_2_msg.image" alt="" />
        </div>
      </div>
    </div>
    <div class="digtal_box digtal_box_3">
      <div class="digtal_box_3_header">
        <div class="home_title">如何加入数商</div>
        <!-- <div class="home_title_1">How to join a digital business</div> -->
      </div>
      <div class="digtal_box_3_content">
        <div class="digtal_box_3_card" v-for="(item, index) in digtal_3_list">
          <div class="card_img dynamic">
            <img :src="apiUrl + item.logo" alt="" />
          </div>
          <div class="card_text">
            <div class="cText_1">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="digtal_box digtal_box_4">
      <div class="digtal_box_4_header">
        <div class="home_title">品牌数商</div>
        <!-- <div class="home_title_1">BRAND NUMBER QUOTIENT</div> -->
      </div>
      <div class="digtal_box_4_content" v-if="!isMobile">
        <div class="swiper-button-prev swiper-button-prev1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-next1">
          <img :src="arrowR" />
        </div>
        <swiper
          :slides-per-view="3"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            prevEl: '.swiper-button-prev1',
            nextEl: '.swiper-button-next1',
          }"
          loop
          :slidesPerGroup="3"
          class="swiper"
          :modules="modules"
          :spaceBetween="26"
        >
          <swiper-slide class="digtal_box_4_card" v-for="item in digtal_4_list">
            <!-- <div
              class="cImg"
              :style="{
                background: 'url(' + apiUrl + item.image + ')',
                backgroundSize: 'cover',
              }"
            > -->
            <div class="cImg">
              <img
                v-if="item.image"
                :src="apiUrl + item.image"
                alt=""
                style="width: 100%"
              />
              <img v-else :src="digtal_5" alt="" style="width: 100%" />
              <div class="company">
                <div class="company_icon">
                  <img v-if="item.logo" :src="apiUrl + item.logo" alt="" />
                  <img v-else :src="icon_5" alt="" />
                </div>
                <div class="company_1">{{ item.title }}</div>
                <div class="company_2">{{ item.subTitle }}</div>
              </div>
            </div>
            <div class="cText">
              <n-tooltip trigger="hover" :style="{ maxWidth: '400rem' }">
                <template #trigger>
                  {{ item.detail }}
                </template>
                {{ item.detail }}
              </n-tooltip>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="digtal_box_4_content" v-else>
        <div class="swiper-button-prev swiper-button-prev1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-next1">
          <img :src="arrowR" />
        </div>
        <swiper
          :slides-per-view="2"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            prevEl: '.swiper-button-prev1',
            nextEl: '.swiper-button-next1',
          }"
          loop
          :slidesPerGroup="2"
          class="swiper"
          :modules="modules"
          :spaceBetween="26"
        >
          <swiper-slide class="digtal_box_4_card" v-for="item in digtal_4_list">
            <div class="cImg">
              <img
                v-if="item.image"
                :src="apiUrl + item.image"
                alt=""
                style="width: 100%"
              />
              <img v-else :src="digtal_5" alt="" style="width: 100%" />
              <div class="company">
                <div class="company_icon">
                  <img v-if="item.logo" :src="apiUrl + item.logo" alt="" />
                  <img v-else :src="icon_5" alt="" />
                </div>
                <div class="company_1">{{ item.title }}</div>
                <div class="company_2">{{ item.subTitle }}</div>
              </div>
            </div>
            <div class="cText">
              <n-tooltip trigger="hover" :style="{ maxWidth: '400rem' }">
                <template #trigger>
                  {{ item.detail }}
                </template>
                {{ item.detail }}
              </n-tooltip>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <div class="digtal_box digtal_box_5">
      <div class="digtal_box_5_header">
        <div class="home_title">最新活动</div>
        <!-- <div class="home_title_1">Latest Activities</div> -->
        <!-- <div class="home_more">News More ——</div> -->
      </div>
      <div class="digtal_box_5_content">
        <div class="digtal_box_5_content_left" @click="goDetail(sideMsg)">
          <div class="left_img">
            <img :src="apiUrl + sideMsg.image" alt="" />
          </div>
          <div class="left_title">
            {{ sideMsg.title }}
          </div>
          <div class="left_text">
            {{ sideMsg.detail }}
          </div>
        </div>
        <div class="digtal_box_5_content_right">
          <div
            class="rightItem"
            v-for="item in digtal_5_list"
            @click="goDetail(item)"
          >
            <div class="right_img">
              <img :src="apiUrl + item.image" alt="" />
            </div>
            <div class="right_title">
              {{ item.title }}
            </div>
            <div class="right_text">{{ item.detail }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="digtal_box digtal_box_6">
      <div class="digtal_box_6_header">
        <div class="home_title">合作数商</div>
        <!-- <div class="home_title_1">COOPERATIVE'NUMBER QUOTIENT</div> -->
      </div>
      <n-tabs
        :defaultValue="checkTab"
        size="large"
        justify-content="space-evenly"
        v-model:value="checkTab"
        @update:value="changeTab"
      >
        <n-tab v-for="item in tabList" :name="item.key">{{ item.label }}</n-tab>
      </n-tabs>
      <div class="digtal_box_6_content">
        <n-carousel
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          loop
          draggable
          :current-index="curIndex"
          @update:current-index="onSlideChange"
          ref="myCarousel"
        >
          <n-carousel-item
            v-for="(im, index) in digtal_6_list[checkTab - 1]"
            style="margin-bottom: 20rem"
          >
            <div class="digtal_box_6_content">
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(0, 8)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(8, 14)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(14, 20)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(20, 26)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(26, 34)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="dItem">
                <img
                  src="../../assets/shushang.png"
                  alt=""
                  style="width: 100%"
                />
              </div>
            </div>
          </n-carousel-item>
          <template #dots="{ total, currentIndex, to }">
            <ul class="custom-dots">
              <li
                v-for="index of total"
                :key="index"
                :class="{ ['is-active']: currentIndex === index - 1 }"
                @click="to(index - 1)"
              />
            </ul>
          </template>
        </n-carousel>
        <!-- <swiper
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          loop
          draggable
          @slideChangeTransitionEnd="onSlideChange"
          :pagination="{
            el: '.swiper-pagination',
            type: 'bullets',
            clickable: true,
          }"
          :modules="modules"
        >
          <swiper-slide
            v-for="(im, index) in digtal_6_list[checkTab - 1]"
            style="margin-bottom: 20rem"
          >
            <div class="digtal_box_6_content">
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(0, 8)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(8, 14)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(14, 20)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(20, 26)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(26, 34)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="dItem">
                <img
                  src="../../assets/shushang.png"
                  alt=""
                  style="width: 100%"
                />
              </div>
            </div>
          </swiper-slide>
          <div class="swiper-pagination"></div>
        </swiper> -->
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import icon_1 from "/@/assets/digtal/icon_1.png";
import icon_2 from "/@/assets/digtal/icon_2.png";
import icon_3 from "/@/assets/digtal/icon_3.svg";
import icon_4 from "/@/assets/digtal/icon_4.png";
import icon_5 from "/@/assets/digtal/icon_5.png";
import digtal_2 from "/@/assets/digtal/digtal_2.png";
import digtal_3 from "/@/assets/digtal/digtal_3.png";
import digtal_4 from "/@/assets/digtal/digtal_4.png";
import digtal_5 from "/@/assets/digtal/digtal_5.png";
import active_1 from "/@/assets/digtal/active_1.png";
import active_2 from "/@/assets/digtal/active_2.png";
import { Swiper, SwiperSlide } from "swiper/vue";
import {
  getBg,
  getConfig,
  getApplication,
  getActivities,
  getBrand,
  getCooper,
  getAdvantage,
} from "/@/api/digtal/index";
import { useGlobSetting } from "/@/hooks/setting";
import { useRouter } from "vue-router";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import arrowL from "/@/assets/home/<USER>";
import arrowR from "/@/assets/home/<USER>";
import "swiper/css";
import "swiper/css/navigation";
const modules = [Autoplay, Navigation, Pagination];
const router = useRouter();
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const checkTab = ref(1);
const digtal_2_list = ref([]);
const digtal_3_list = ref([]);
const digtal_4_list = ref([]);
const digtal_5_list = ref([]);
const digtal_6_list = ref([]);
const sideMsg = ref({});
const tabList = ref([]);
const digtal_1_imgUrls = ref([]);
const digtal_1_videoUrl = ref([]);
const digtal_2_msg = ref({});
const swiperRef = ref(null);
const myCarousel = ref(null);
const digtal_1_title = ref("");
const digtal_1_detail = ref("");
const curIndex = ref(0);
const coverImg = ref("");
function getboxBg() {
  let params = {
    classId: 11056,
  };
  getBg(params).then((res) => {
    digtal_1_imgUrls.value = res.imgUrls;
    digtal_1_videoUrl.value = res.videoUrl;
    digtal_1_title.value = res.title;
    digtal_1_detail.value = res.detail;
    coverImg.value = res.cover;
  });
}
function getTitleConfig() {
  let params = {
    classId: ["11100"],
  };
  getConfig(params).then((res) => {
    res.forEach((e) => {
      if (e.classId == "11100") {
        digtal_2_msg.value = e;
      }
    });
  });
}
function getWayData() {
  let params = {
    classId: 11057,
  };
  getApplication(params).then((res) => {
    digtal_3_list.value = res;
  });
}
function getActive() {
  let params = {
    classId: 11067,
  };
  getActivities(params).then((res) => {
    res.forEach((e) => {
      if (e.isRec == "1") {
        sideMsg.value = e;
      } else {
        digtal_5_list.value.push(e);
      }
    });
  });
}
function getBrands() {
  getBrand().then((res) => {
    digtal_4_list.value = res;
  });
}
function getCP() {
  getCooper().then((res) => {
    let i = 0;
    let arr = res.pop();
    arr.forEach((e) => {
      i++;
      tabList.value.push({
        key: i,
        label: e,
      });
    });
    digtal_6_list.value = res;
    console.log('digtal_6_list.value1',digtal_6_list.value)
    digtal_6_list.value.forEach((item, index) => {
      item.forEach((e, i) => {
        if (e.length < 34) {
          item.splice(i, 1);
        }
      });
    });
    console.log(" digtal_6_list.value", digtal_6_list.value);
  });
}
function changeTab(e) {
  checkTab.value = e;
  console.log('checkTab.value',checkTab.value)
  onSlideChange(0);
}
function onSlideChange(e) {
  curIndex.value = e;
  let timer = null;
  let timer1 = null;
  let len = [];
  // checkTab.value = e + 1;
  if (digtal_6_list.value.length) {
    len = digtal_6_list.value[checkTab.value - 1];
    console.log('digtal_6_list.value',digtal_6_list.value)
    console.log('len',e+1,checkTab.value - 1,digtal_6_list.value[checkTab.value - 1],'12',len)
    if (e + 1 > len.length || e + 1 == len.length) {
      clearTimeout(timer);
      timer = setTimeout(() => {
        if (
          checkTab.value > digtal_6_list.value.length ||
          checkTab.value == digtal_6_list.value.length
        ) {
          checkTab.value = 1;
          curIndex.value = 0;
          onSlideChange(curIndex.value);
        } else {
          console.log('here')
          checkTab.value += 1;
          curIndex.value = 0;
          onSlideChange(curIndex.value);
        }
      }, 5000);
    } else {
      checkTab.value = 1;
      curIndex.value = 0;
      // console.log("else", curIndex.value, checkTab.value);
      onSlideChange(curIndex.value);
    }
  }
}
function getAdva() {
  let params = {
    classId: 11101,
  };
  getAdvantage(params).then((res) => {
    digtal_2_list.value = res;
  });
}
function goDetail(e) {
  router.push({
    name: "newsDetail",
    query: { contentId: e.contentId, flag: true },
  });
  localStorage.setItem("pagePath", 11040);
}
onMounted(() => {
  getboxBg();
  getTitleConfig();
  getWayData();
  getActive();
  getBrands();
  getCP();
  getAdva();
});
</script>
<style lang="less" scoped>
@media screen and (min-width: 769px) {
  #digtal {
    font-family: MiSans;
    .digtal_box {
      height: 100vh;
      width: 100%;
      position: relative;
      .home_title {
        line-height: 104rem;
        color: rgba(56, 56, 56, 1);
        font-size: 50rem !important;
        font-weight: 900;
        margin-bottom: 8rem;
        width: 100%;
      }
      .home_title_1 {
        font-size: 36rem;
        font-weight: 500;
        line-height: 47.74rem;
        color: rgba(128, 128, 128, 1);
        width: 100%;
      }
      .home_detail {
        font-size: 24rem;
        font-weight: 300;
        letter-spacing: 1rem;
        /* line-height: 30rem; */
        text-overflow: ellipsis;
        color: rgba(128, 128, 128, 1);
        width: 100%;
        overflow: hidden;
        margin-top: 34rem;
      }
      .home_more {
        position: absolute;
        right: 0;
        top: 0;
        width: 130rem;
        height: 28rem;
        font-size: 16rem;
        font-weight: 500;
        line-height: 28rem;
        color: rgba(40, 41, 56, 1);
      }
      :deep(.n-tabs) {
        --n-tab-text-color-hover: rgba(13, 46, 153, 1) !important;
        --n-tab-text-color-active: rgba(13, 46, 153, 1) !important;
        --n-bar-color: rgba(13, 46, 153, 1) !important;
        --n-tab-text-color: rgba(115, 115, 115, 1) !important;
        --n-tab-font-size: 22rem !important;
        width: 639rem;
        margin: 34rem auto 74rem;
      }
    }
    .digtal_box_1 {
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      .digtal_box_1_carousel {
        height: 100%;
        .digtal_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
      }
      .digtal_box_1_header {
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(13, 46, 153, 0.8) 0%,
          rgba(70, 128, 255, 0) 100%
        );
        position: absolute;
        top: 0;
        display: flex;
        flex-direction: column;
        .digtal_box_1_glass {
          width: 75%;
          margin: 270rem 0 0 160rem;
          .digtal_box_1_header_title {
            font-size: 50rem;
            font-weight: 900;
            color: rgba(255, 255, 255, 1);
            margin: auto;
            align-items: center;
          }
          .digtal_box_1_header_context {
            margin-top: 26rem;
            line-height: 28rem;
            font-size: 20rem;
            font-weight: 300;
            color: #ffffff;
          }
          .digtal_box_1_header_btn {
            width: 251rem;
            height: 71rem;
            opacity: 1;
            border-radius: 60rem;
            border: 2rem solid rgba(255, 255, 255, 1);
            font-size: 24rem;
            font-weight: 600;
            color: #ffffff;
            line-height: 32rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 25rem;
            margin: 36rem auto auto;
          }
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        bottom: 7%;
        left: 50%;
        position: absolute;
        background: url("../../assets/down.png");
        animation: blink 2s infinite;
        transform: translateX(50%);
      }
    }
    .digtal_box_2 {
      width: 100%;
      height: 100%;
      .digtal_box_2_content {
        width: 1600rem;
        margin: 60rem auto 40rem;
        display: flex;
        .digtal_box_2_left {
          width: 1000rem;
          /* height: 766rem; */
          .left_footer {
            width: 100%;
            /* height: 250rem; */
            display: flex;
            /* justify-content: space-between;
          flex-wrap: wrap;
          margin-top: 108rem; */
            display: flex;
            flex-direction: column;
            .fItem {
              display: flex;
              flex-direction: row;
              /* width: 50%; */
              height: 125rem;
              margin-top: 22rem;
              .fRight {
                width: 125rem;
                height: 125rem;
                border-radius: 50%;
                background-color: rgba(250, 250, 250, 1);
                display: flex;
                align-items: center;
                justify-content: center;
                img {
                  width: 51rem;
                  height: 51rem;
                }
              }
              .fLeft {
                margin-left: 16rem;
                display: flex;
                flex-direction: column;
                .flTitle {
                  font-size: 30rem;
                  font-weight: 700;
                  letter-spacing: 1rem;
                  /* line-height: 30rem; */
                  color: rgba(56, 56, 56, 1);
                }
                .flText {
                  /* width: 300rem; */
                  height: 52rem;
                  opacity: 1;
                  font-size: 24rem;
                  font-weight: 500;
                  letter-spacing: 1rem;
                  line-height: 26rem;
                  color: rgba(170, 170, 170, 1);
                  margin-top: 20rem;
                }
              }
            }
            .fItem:hover {
              cursor: pointer;
              .fRight {
                background: #0d2e99;
              }
            }
          }
        }
        .digtal_box_2_right {
          width: 707rem;
          height: 720rem;
          margin-left: 120rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: fill;
          }
        }
      }
    }
    .digtal_box_3 {
      /* height: 116vh; */
      height: 100%;
      display: flex;
      flex-direction: column;
      background-color: rgba(245, 245, 245, 1);
      .digtal_box_3_header {
        margin-top: 60rem;
        text-align: center;
      }
      .digtal_box_3_content {
        margin: 32rem auto 40rem;
        width: 1600rem;
        /* height: 658rem; */
        display: grid;
        grid-template-columns: repeat(5, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        flex-wrap: wrap;
        .digtal_box_3_card {
          /* width: 525rem; */
          /* height: 328rem; */
          margin-bottom: 12rem;
          position: relative;
          background-position: center;
          transition: transform 1s ease;
          overflow: hidden;

          .card_img {
            width: 296rem;
            height: 174rem;
            img {
              transition: transform 1s;
              width: 100%;
              height: 100%;
            }
          }
          .card_text {
            /* height: 302rem; */
            /* position: absolute; */
            /* bottom: 0; */
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            /* padding-left: 32rem; */
            .cText_1 {
              margin: 24rem 0 0;
              /* width: 90%; */
              /* height: 96rem; */
              font-size: 24rem;
              font-weight: 700;
              letter-spacing: 0;
              /* line-height: 48rem; */
              color: rgba(51, 51, 51, 1);
            }
          }
        }
        /* .digtal_box_3_card:hover {
        cursor: pointer;
        .card_img {
          img {
            transform: scale(1.2);
          }
        }
      } */
      }
    }
    .digtal_box_4 {
      /* height: 110vh; */
      height: 100%;
      display: flex;
      flex-direction: column;
      .digtal_box_4_header {
        width: 1600rem;
        /* margin: calc(100 / 1080 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .digtal_box_4_content {
        /* margin: 54rem auto 0; */
        margin: 32rem auto 40rem;
        height: 671rem;
        width: 1600rem;
        display: flex;
        position: relative;
        :deep(.swiper-button-prev1) {
          width: 68rem;
          height: 68rem;
          border-radius: 34rem;
          left: -80rem;
          background: rgba(237, 237, 237, 0.6);
        }
        :deep(.swiper-button-next1) {
          width: 68rem;
          height: 68rem;
          right: -80rem;
          border-radius: 34rem;
          background: #f6f6f6;
        }
        .swiper-button-prev:after,
        .swiper-button-next:after {
          font-family: "";
        }
        .swiper-button-next:after {
          content: "";
        }
        .swiper-button-prev:after {
          content: "";
        }
        .swiper {
          width: 100%;
          height: 100%;
          display: flex;
          --swiper-theme-color: #ff6600;
          --swiper-pagination-color: #00ff33; /* 两种都可以 */
          :deep(.swiper-wrapper) {
            width: 100%;
            height: 100%;
            display: flex;
          }
          .digtal_box_4_card {
            width: 516rem !important;
            height: 658rem;
            margin-bottom: 48rem;
            background: rgba(250, 250, 250, 1);
            .cImg {
              width: 516rem;
              height: 304rem;
              position: relative;
              .company {
                width: 100%;
                height: 100%;
                background: linear-gradient(
                  90deg,
                  rgba(13, 46, 153, 1) 0%,
                  rgba(13, 46, 153, 0) 100%
                );
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                position: absolute;
                top: 0;
                .company_icon {
                  /* width: 85rem; */
                  height: 85rem;
                  img {
                    width: 100%;
                    height: 100%;
                  }
                }
                .company_1 {
                  font-size: 36rem;
                  font-weight: 700;
                  letter-spacing: 0rem;
                  line-height: 48rem;
                  max-width: 468rem;
                  height: 96rem;
                  color: rgba(255, 255, 255, 1);
                  overflow: hidden;
                  margin-top: 28rem;
                  text-align: center;
                  display: flex;
                  justify-content: center;
                }
                .company_2 {
                  font-size: 12rem;
                  font-weight: 700;
                  letter-spacing: 0rem;
                  line-height: 15.91rem;
                  color: rgba(255, 255, 255, 1);
                  text-align: center;
                  display: flex;
                  justify-content: center;
                }
              }
            }
            .cText {
              width: 422rem;
              /* height: 264rem; */
              font-size: 18rem;
              font-weight: 400;
              letter-spacing: 1rem;
              line-height: 26rem;
              color: rgba(85, 85, 85, 1);
              overflow: hidden;
              margin: 48rem auto auto;
              text-align: justify;
              overflow: hidden;
              -webkit-line-clamp: 10;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:nth-child(n + 1) {
              margin-right: 17.5rem;
            }
            /* &:nth-child(3n){
            margin-right: 17.5rem;
          } */
          }
          .digtal_box_4_card:hover {
            cursor: pointer;
          }
        }
      }
    }
    .digtal_box_5 {
      /* height: 110vh; */
      height: 100%;
      display: flex;
      flex-direction: column;
      background: rgba(250, 250, 250, 1);
      .digtal_box_5_header {
        position: relative;
        width: 1600rem;
        /* margin: calc(100 / 1080 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .digtal_box_5_content {
        top: 0;
        left: 0;
        height: 100%;
        width: 1600rem;
        /* margin: calc(48 / 1080 * 116vh) auto; */
        margin: 32rem auto 40rem;
        display: flex;
        .digtal_box_5_content_left {
          height: 100%;
          min-width: 60%;
          margin-right: 48rem;
          display: flex;
          flex-direction: column;
          .left_img {
            width: 1138rem;
            height: 613rem;
            border-radius: 20rem 0rem 20rem 0rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left_title {
            font-size: 24rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            margin-top: 28rem;
            margin-bottom: 8rem;
          }
          .left_text {
            width: 1138rem;
            font-size: 18rem;
            font-weight: 400;
            line-height: 24rem;
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
        .digtal_box_5_content_left:hover {
          cursor: pointer;
          .left_text {
            color: #0d2e99;
          }
        }
        .digtal_box_5_content_right {
          display: flex;
          flex-direction: column;
          /* width: 26%; */
          .rightItem {
            display: flex;
            flex-direction: column;
            .right_img {
              width: 413rem;
              height: 222rem;
              margin-bottom: 28rem;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .right_title {
              font-size: 24rem;
              font-weight: 700;
              line-height: 32rem;
              color: rgba(56, 56, 56, 1);
              width: 402rem;
              height: 32rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 8rem;
            }
            .right_text {
              font-size: 18rem;
              font-weight: 400;
              line-height: 24rem;
              color: rgba(91, 91, 91, 1);
              width: 402rem;
              overflow: hidden;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:first-child {
              margin-bottom: 54rem;
            }
          }
          .rightItem:hover {
            cursor: pointer;
            .right_text {
              color: #0d2e99;
            }
          }
        }
      }
    }
    .digtal_box_6 {
      /* height: 116vh; */
      height: 100%;
      .digtal_box_6_header {
        margin: 60rem auto 0;
        width: 620rem;
        text-align: center;
      }
      .digtal_box_6_content {
        width: 1600rem;
        display: flex;
        /* margin: 0 auto; */
        margin: 0 auto 20rem;
        flex-wrap: wrap;
        position: relative;
        .cLine {
          width: 100%;
          justify-content: center;
          display: flex;
          &:nth-child(2n) {
            transform: translateX(-100rem);
            :nth-child(n + 4) {
              transform: translateX(200rem);
            }
          }
          &:nth-child(3) {
            &:nth-child(-n + 3) {
              transform: translateX(-200rem);
            }
            :nth-child(n + 4) {
              transform: translateX(400rem);
            }
          }
          &:not(:first-child) {
            margin-top: -100rem;
          }
          .cItem {
            width: 200rem;
            height: 200rem;
            clip-path: polygon(50% 5%, 95% 50%, 50% 95%, 5% 50%);
            background: rgba(242, 242, 242, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 54%;
              height: 40%;
            }
          }
        }
        .dItem {
          width: 380rem;
          height: 380rem;
          clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
          background: rgba(13, 46, 153, 1);
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          justify-content: center;
          align-items: center;

          .dIm {
            width: 140rem;
            height: 140rem;
            border: 1rem solid rgba(255, 255, 255, 1);
            transform: rotate(45deg);
            .dTitle {
              font-size: 48rem;
              font-weight: 900;
              letter-spacing: 0rem;
              line-height: 69.5rem;
              color: rgba(255, 255, 255, 1);
            }
            .dText {
              font-size: 16rem;
              font-weight: 900;
              letter-spacing: 0rem;
              line-height: 23.17rem;
              color: rgba(255, 255, 255, 1);
            }
          }
        }
        .swiper-pagination {
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 1000rem; /* 设置合适的宽度 */
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: #0d2e99;
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #digtal {
    font-family: MiSans;
    .digtal_box {
      height: 50vh;
      width: 100%;
      position: relative;
      .home_title {
        line-height: 104rem;
        color: rgba(56, 56, 56, 1);
        font-size: 50rem !important;
        font-weight: 900;
        margin-bottom: 8rem;
        width: 100%;
      }
      .home_title_1 {
        font-size: 36rem;
        font-weight: 500;
        line-height: 47.74rem;
        color: rgba(128, 128, 128, 1);
        width: 100%;
      }
      .home_detail {
        font-size: 20rem;
        font-weight: 300;
        letter-spacing: 1rem;
        line-height: 30rem;
        text-overflow: ellipsis;
        color: rgba(170, 170, 170, 1);
        width: 100%;
        overflow: hidden;
        margin-top: 34rem;
      }
      .home_more {
        position: absolute;
        right: 0;
        top: 0;
        width: 130rem;
        height: 28rem;
        font-size: 16rem;
        font-weight: 500;
        line-height: 28rem;
        color: rgba(40, 41, 56, 1);
      }
      :deep(.n-tabs) {
        --n-tab-text-color-hover: rgba(13, 46, 153, 1) !important;
        --n-tab-text-color-active: rgba(13, 46, 153, 1) !important;
        --n-bar-color: rgba(13, 46, 153, 1) !important;
        --n-tab-text-color: rgba(115, 115, 115, 1) !important;
        --n-tab-font-size: 22rem !important;
        width: 639rem;
        margin: 34rem auto 74rem;
      }
    }
    .digtal_box_1 {
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      .digtal_box_1_carousel {
        height: 100%;
        .digtal_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
      }
      .digtal_box_1_header {
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(13, 46, 153, 0.8) 0%,
          rgba(70, 128, 255, 0) 100%
        );
        position: absolute;
        top: 0;
        display: flex;
        flex-direction: column;
        .digtal_box_1_glass {
          width: 720rem;
          margin: 170rem auto 0;
          .digtal_box_1_header_title {
            font-size: 32rem;
            font-weight: 900;
            color: rgba(255, 255, 255, 1);
            margin: auto;
            align-items: center;
          }
          .digtal_box_1_header_context {
            margin-top: 6rem;
            line-height: 28rem;
            font-size: 20rem;
            font-weight: 300;
            color: #ffffff;
            :deep(p) {
              font-size: 22rem !important;
              span {
                font-size: 22rem !important;
              }
            }
          }
          .digtal_box_1_header_btn {
            width: 251rem;
            height: 71rem;
            opacity: 1;
            border-radius: 60rem;
            border: 2rem solid rgba(255, 255, 255, 1);
            font-size: 24rem;
            font-weight: 600;
            color: #ffffff;
            line-height: 32rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 25rem;
            margin: 36rem auto auto;
          }
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        bottom: 7%;
        left: 50%;
        position: absolute;
        background: url("../../assets/down.png");
        animation: blink 2s infinite;
        transform: translateX(-50%);
      }
    }
    .digtal_box_2 {
      width: 100%;
      height: 100%;
      .digtal_box_2_content {
        width: 720rem;
        margin: 60rem auto 40rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .digtal_box_2_left {
          /* width: 1000rem; */
          /* height: 766rem; */
          .left_footer {
            width: 100%;
            /* height: 250rem; */
            display: flex;
            /* justify-content: space-between;
          flex-wrap: wrap;
          margin-top: 108rem; */
            display: flex;
            flex-direction: column;
            .fItem {
              display: flex;
              flex-direction: row;
              /* width: 50%; */
              height: 125rem;
              margin-top: 36rem;
              .fRight {
                width: 125rem;
                height: 125rem;
                border-radius: 50%;
                background-color: rgba(250, 250, 250, 1);
                display: flex;
                align-items: center;
                justify-content: center;
                img {
                  width: 51rem;
                  height: 51rem;
                }
              }
              .fLeft {
                margin-left: 16rem;
                display: flex;
                flex-direction: column;
                .flTitle {
                  font-size: 30rem;
                  font-weight: 700;
                  letter-spacing: 1rem;
                  /* line-height: 30rem; */
                  color: rgba(56, 56, 56, 1);
                }
                .flText {
                  /* width: 300rem; */
                  height: 52rem;
                  opacity: 1;
                  font-size: 24rem;
                  font-weight: 500;
                  letter-spacing: 1rem;
                  line-height: 26rem;
                  color: rgba(170, 170, 170, 1);
                  margin-top: 20rem;
                }
              }
            }
            .fItem:hover {
              cursor: pointer;
              .fRight {
                background: #0d2e99;
              }
            }
          }
        }
        .digtal_box_2_right {
          width: 707rem;
          height: 880rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: fill;
          }
        }
      }
    }
    .digtal_box_3 {
      /* height: 116vh; */
      height: 100%;
      display: flex;
      flex-direction: column;
      background-color: rgba(245, 245, 245, 1);
      .digtal_box_3_header {
        margin-top: 60rem;
        text-align: center;
      }
      .digtal_box_3_content {
        margin: 32rem auto 40rem;
        width: 720rem;
        /* height: 658rem; */
        display: grid;
        grid-template-columns: repeat(2, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        flex-wrap: wrap;
        .digtal_box_3_card {
          /* width: 525rem; */
          /* height: 328rem; */
          margin-bottom: 12rem;
          position: relative;
          background-position: center;
          transition: transform 1s ease;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          align-items: center;
          .card_img {
            width: 296rem;
            height: 174rem;
            img {
              transition: transform 1s;
              width: 100%;
              height: 100%;
            }
          }
          .card_text {
            /* height: 302rem; */
            /* position: absolute; */
            /* bottom: 0; */
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            /* padding-left: 32rem; */
            .cText_1 {
              margin: 24rem 0 0;
              /* width: 90%; */
              /* height: 96rem; */
              font-size: 24rem;
              font-weight: 700;
              letter-spacing: 0;
              /* line-height: 48rem; */
              color: rgba(51, 51, 51, 1);
            }
          }
        }
        /* .digtal_box_3_card:hover {
        cursor: pointer;
        .card_img {
          img {
            transform: scale(1.2);
          }
        }
      } */
      }
    }
    .digtal_box_4 {
      /* height: 110vh; */
      height: 100%;
      display: flex;
      flex-direction: column;
      .digtal_box_4_header {
        width: 720rem;
        /* margin: calc(100 / 1080 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .digtal_box_4_content {
        /* margin: 54rem auto 0; */
        margin: 32rem auto 40rem;
        height: 671rem;
        width: 720rem;
        display: flex;
        position: relative;
        :deep(.swiper-button-prev1) {
          width: 68rem;
          height: 68rem;
          border-radius: 34rem;
          left: -80rem;
          background: rgba(237, 237, 237, 0.6);
        }
        :deep(.swiper-button-next1) {
          width: 68rem;
          height: 68rem;
          right: -80rem;
          border-radius: 34rem;
          background: #f6f6f6;
        }
        .swiper-button-prev:after,
        .swiper-button-next:after {
          font-family: "";
        }
        .swiper-button-next:after {
          content: "";
        }
        .swiper-button-prev:after {
          content: "";
        }
        .swiper {
          width: 100%;
          height: 100%;
          display: flex;
          --swiper-theme-color: #ff6600;
          --swiper-pagination-color: #00ff33; /* 两种都可以 */
          :deep(.swiper-wrapper) {
            width: 100%;
            height: 100%;
            display: flex;
          }
          .digtal_box_4_card {
            width: 516rem !important;
            height: 658rem;
            margin-bottom: 48rem;
            background: rgba(250, 250, 250, 1);
            .cImg {
              width: 516rem;
              height: 304rem;
              position: relative;
              .company {
                width: 100%;
                height: 100%;
                background: linear-gradient(
                  90deg,
                  rgba(13, 46, 153, 1) 0%,
                  rgba(13, 46, 153, 0) 100%
                );
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                position: absolute;
                top: 0;
                .company_icon {
                  /* width: 85rem; */
                  height: 85rem;
                  img {
                    width: 100%;
                    height: 100%;
                  }
                }
                .company_1 {
                  font-size: 36rem;
                  font-weight: 700;
                  letter-spacing: 0rem;
                  line-height: 48rem;
                  max-width: 468rem;
                  height: 96rem;
                  color: rgba(255, 255, 255, 1);
                  overflow: hidden;
                  margin-top: 28rem;
                  text-align: center;
                  display: flex;
                  justify-content: center;
                }
                .company_2 {
                  font-size: 12rem;
                  font-weight: 700;
                  letter-spacing: 0rem;
                  line-height: 15.91rem;
                  color: rgba(255, 255, 255, 1);
                  text-align: center;
                  display: flex;
                  justify-content: center;
                }
              }
            }
            .cText {
              width: 422rem;
              height: 264rem;
              font-size: 18rem;
              font-weight: 400;
              letter-spacing: 1rem;
              line-height: 26rem;
              color: rgba(85, 85, 85, 1);
              overflow: hidden;
              margin: 48rem auto auto;
              text-align: justify;
            }
            &:nth-child(n + 1) {
              margin-right: 17.5rem;
            }
            /* &:nth-child(3n){
            margin-right: 17.5rem;
          } */
          }
          .digtal_box_4_card:hover {
            cursor: pointer;
          }
        }
      }
    }
    .digtal_box_5 {
      /* height: 110vh; */
      height: 100%;
      display: flex;
      flex-direction: column;
      background: rgba(250, 250, 250, 1);
      .digtal_box_5_header {
        position: relative;
        width: 720rem;
        /* margin: calc(100 / 1080 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .digtal_box_5_content {
        top: 0;
        left: 0;
        height: 100%;
        width: 720rem;
        /* margin: calc(48 / 1080 * 116vh) auto; */
        margin: 32rem auto 40rem;
        display: flex;
        flex-direction: column;
        .digtal_box_5_content_left {
          height: 100%;
          min-width: 60%;
          /* margin-right: 48rem; */
          display: flex;
          flex-direction: column;
          .left_img {
            /* width: 1138rem; */
            /* height: 485rem; */
            width: 100%;
            height: 307rem;
            border-radius: 20rem 0rem 20rem 0rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left_title {
            font-size: 28rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            margin-top: 28rem;
            margin-bottom: 8rem;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
          }
          .left_text {
            /* width: 1138rem; */
            font-size: 24rem;
            font-weight: 400;
            /* line-height: 24rem; */
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
        .digtal_box_5_content_left:hover {
          cursor: pointer;
          .left_text {
            color: #0d2e99;
          }
        }
        .digtal_box_5_content_right {
          display: flex;
          flex-direction: column;
          margin-top: 22rem;
          /* width: 26%; */
          .rightItem {
            display: flex;
            flex-direction: column;
            .right_img {
              /* width: 413rem;
              height: 222rem; */
              width: 100%;
              height: 387rem;
              margin-bottom: 16rem;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .right_title {
              font-size: 28rem;
              font-weight: 700;
              line-height: 32rem;
              color: rgba(56, 56, 56, 1);
              /* width: 402rem; */
              height: 32rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 10rem;
            }
            .right_text {
              font-size: 24rem;
              font-weight: 400;
              line-height: 24rem;
              color: rgba(91, 91, 91, 1);
              /* width: 402rem; */
              overflow: hidden;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:first-child {
              margin-bottom: 14rem;
            }
          }
          .rightItem:hover {
            cursor: pointer;
            .right_text {
              color: #0d2e99;
            }
          }
        }
      }
    }
    .digtal_box_6 {
      /* height: 116vh; */
      height: 100%;
      .digtal_box_6_header {
        margin: 60rem auto 0;
        width: 620rem;
        text-align: center;
      }
      .digtal_box_6_content {
        width: 720rem;
        display: flex;
        /* margin: 0 auto; */
        margin: 0 auto 20rem;
        flex-wrap: wrap;
        position: relative;
        .cLine {
          width: 100%;
          justify-content: center;
          display: flex;
          &:nth-child(2n) {
            transform: translateX(-45rem);
            :nth-child(n + 4) {
              transform: translateX(90rem);
            }
          }
          &:nth-child(3) {
            &:nth-child(-n + 3) {
              transform: translateX(-90rem);
            }
            :nth-child(n + 4) {
              transform: translateX(180rem);
            }
          }
          &:not(:first-child) {
            margin-top: -45rem;
          }
          .cItem {
            width: 90rem;
            height: 90rem;
            clip-path: polygon(50% 5%, 95% 50%, 50% 95%, 5% 50%);
            background: rgba(242, 242, 242, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: 54%;
              height: 40%;
            }
          }
        }
        .dItem {
          width: 171rem;
          height: 171rem;
          clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
          background: rgba(13, 46, 153, 1);
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: #0d2e99;
        }
      }
    }
  }
}
</style>
