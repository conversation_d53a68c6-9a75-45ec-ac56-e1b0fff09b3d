<template>
  <div id="home">
    <div class="home_box home_box_1">
      <n-carousel
        ref="carousel"
        draggable
        autoplay
        dot-type="line"
        class="home_box_1_carousel"
      >
        <n-carousel-item
          class="n-carousel-item"
          v-if="home_1_videoUrl.length != 0"
          v-for="(item, index) in home_1_videoUrl"
          style="width: 100%; height: 100%"
        >
          <div class="home_box_1_item">
            <video
              :src="apiUrl + item"
              autoplay
              loop
              muted
              style="width: 100%; height: 100%; object-fit: fill"
              :poster="apiUrl + coverImg"
            >
              <!-- <source
                :src="apiUrl+`/local/video/2024-04-13/test/output.m3u8`"
                type="application/x-mpegURL"
            /> -->
            </video>
          </div>
        </n-carousel-item>
        <n-carousel-item
          class="n-carousel-item"
          v-else
          v-for="(item, index) in home_1_imgUrls"
          style="width: 100%; height: 100%"
        >
          <div class="home_box_1_item">
            <img :src="apiUrl + item" alt="" />
          </div>
        </n-carousel-item>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              @click="to(index - 1)"
            />
          </ul>
        </template>
      </n-carousel>
      <div class="glass">
        <div class="banner-content">
          <div class="title">{{ titleContext.title }}</div>
          <div>
            <n-button
              color="#ffffff"
              ghost
              round
              @click="goPage(titleContext.buttonUrl)"
              v-if="titleContext.buttonName"
            >
              {{ titleContext.buttonName }}
            </n-button>
          </div>
          <div class="context" v-html="titleContext.detail"></div>
        </div>
        <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div>
      </div>
    </div>
    <div class="home_box home_box_2">
      <div class="home_boex_2_bg" v-if="!isMobile"></div>
      <div
        class="home_boex_2_bg"
        style="left: calc(1624 / 1903 * 100%); top: calc(164 / 1080 * 116vh)"
        v-if="!isMobile"
      ></div>
      <div class="home_box_2_top">
        <div>
          <div class="home_title">{{ home_2_msg.title }}</div>
          <div class="home_title_1" style="color: #aaa">
            {{ home_2_msg.subTitle }}
          </div>
        </div>
        <div class="home_detail">{{ home_2_msg.detail }}</div>
      </div>
      <div class="content" v-if="!isMobile">
        <div class="swiper-button-prev swiper-button-next1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-prev1">
          <img :src="arrowR" />
        </div>
        <swiper
          :slides-per-view="4"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :slidesPerGroup="4"
          class="swiperRef"
          :modules="modules"
        >
          <swiper-slide class="home_box_2_card" v-for="item in home_2_list">
            <div
              class="home_box_2_item"
              @click="goCreateDetail(item.contentId)"
            >
              <img :src="apiUrl + item.logo" alt="" />
              <div class="home_box_2_text">
                <div class="title">{{ item.title }}</div>
                <div class="fTitle">{{ item.subTitle }}</div>
                <div class="Hbar"></div>
                <div class="con" v-html="item.detail"></div>
                <div class="Hbottom">
                  <img
                    :src="toRight"
                    alt=""
                    style="width: 60rem; height: 9rem"
                  />
                </div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="content" v-else>
        <!-- <div class="swiper-button-prev swiper-button-next1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-prev1">
          <img :src="arrowR" />
        </div> -->
        <swiper
          :slides-per-view="2"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :slidesPerGroup="2"
          class="swiperRef"
          :modules="modules"
        >
          <swiper-slide class="home_box_2_card" v-for="item in home_2_list">
            <div
              class="home_box_2_item"
              @click="goCreateDetail(item.contentId)"
            >
              <img :src="apiUrl + item.logo" alt="" />
              <div class="home_box_2_text">
                <div class="title">{{ item.title }}</div>
                <div class="fTitle">{{ item.subTitle }}</div>
                <div class="Hbar"></div>
                <div class="con" v-html="item.detail"></div>
                <div class="Hbottom">
                  <img
                    :src="toRight"
                    alt=""
                    style="width: 60rem; height: 9rem"
                  />
                </div>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <div class="home_box home_box_3">
      <div class="home_box_3_header">
        <div class="home_title">{{ home_3_msg.title }}</div>
        <div class="home_title_1">{{ home_3_msg.subTitle }}</div>
        <div class="home_detail">{{ home_3_msg.detail }}</div>
        <div class="home_more dynamicMore" @click="goDetail()">More ——</div>
      </div>

      <div class="home_box_3_list">
        <div
          class="list_item"
          v-for="(item, index) in home_3_list.slice(0, 3)"
          @click="goDetail(item.CONTENTID)"
        >
          <div class="list_img">
            <img :src="apiUrl + item.imageUrl" alt="" />
          </div>
          <div class="list_title">{{ item.TITLE }}</div>
          <div class="list_content" v-html="item.CONTENT"></div>
          <div class="list_learn">
            Learn More
            <img src="../../assets/home/<USER>" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div class="home_box home_box_4">
      <div class="home_box_header">
        <div class="home_title">{{ home_4_msg.title }}</div>
        <div class="home_title_1">{{ home_4_msg.subTitle }}</div>
        <div class="home_detail">{{ home_4_msg.detail }}</div>
        <div class="home_more dynamicMore" @click="goPage('/news')">
          More ——
        </div>
      </div>

      <div class="home_box_4_content">
        <div class="home_box_4_content_left" @click="goNews(sideMsg)">
          <div class="left_img">
            <img v-if="sideMsg.image" :src="apiUrl + sideMsg.image" alt="" />
            <img v-else src="../../assets/home/<USER>" alt="" />
          </div>
          <div class="left_title">{{ sideMsg.title }}</div>
          <div class="left_text">{{ sideMsg.detail }}</div>
        </div>
        <div class="home_box_4_content_right">
          <div
            class="rightItem"
            v-for="item in home_4_list"
            @click="goNews(item)"
          >
            <div class="right_img">
              <img v-if="item.image" :src="apiUrl + item.image" alt="" />
              <img v-else src="../../assets/home/<USER>" alt="" />
            </div>
            <div class="right_title">
              {{ item.title }}
            </div>
            <div class="right_text">
              {{ item.detail }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home_box home_box_6">
      <div class="home_box_header">
        <div class="home_title">{{ home_5_msg.title }}</div>
        <div class="home_title_1">{{ home_5_msg.subTitle }}</div>
        <div class="home_detail">{{ home_5_msg.detail }}</div>
      </div>
      <div class="home_box_6_content">
        <n-carousel autoplay loop draggable>
          <n-carousel-item
            v-for="(im, index) in home_6_list[0]"
            style="margin-bottom: 20rem"
          >
            <div class="home_box_6_carousel">
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(0, 8)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(8, 14)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(14, 20)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(20, 26)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(26, 34)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="dItem">
                <img
                  src="../../assets/shushang.png"
                  alt=""
                  style="width: 100%"
                />
              </div>
            </div>
          </n-carousel-item>
          <template #dots="{ total, currentIndex, to }">
            <ul class="custom-dots">
              <li
                v-for="index of total"
                :key="index"
                :class="{ ['is-active']: currentIndex === index - 1 }"
                @click="to(index - 1)"
              />
            </ul>
          </template>
        </n-carousel>
      </div>
    </div>
    <router-view></router-view>
    <!-- <div class=" customer-service" @click="showIm = !showIm">
      <img src="/@/assets/home/<USER>" alt="" />
    </div> -->
    <!-- <Im ref="imRef" v-if="showIm" @close="showIm = false"/> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import cBg from "/@/assets/home/<USER>";
import hone_bg_2 from "/@/assets/home/<USER>";
import toRight from "/@/assets/home/<USER>";
import arrowL from "/@/assets/home/<USER>";
import arrowR from "/@/assets/home/<USER>";
import {
  getBg,
  getPolicy,
  getApplication,
  getConfig,
  getPartner,
} from "/@/api/home/<USER>";
import { getActivities } from "/@/api/digtal/index";
import { getTablist } from "/@/api/news/index";
import { useGlobSetting } from "/@/hooks/setting";
import { useRouter } from "vue-router";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay, Navigation } from "swiper/modules";
import Im from './component/Im.vue';
const modules = [Autoplay, Navigation];
import "swiper/css";
import "swiper/css/navigation";
const router = useRouter();
const globSetting = useGlobSetting();
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const apiUrl = ref(globSetting.apiUrl);
const carousel = ref(null);
const home_2_list = ref([]);
const home_2_activ = ref(0);
const home_3_list = ref([]);
const home_3_activ = ref(0);
const home_4_list = ref([]);
const home_1_imgUrls = ref([]);
const home_1_videoUrl = ref([]);
const home_2_msg = ref({});
const home_3_msg = ref({});
const home_4_msg = ref({});
const home_5_msg = ref({});
const home_6_list = ref([]);
const sideMsg = ref({});
const titleContext = ref({});
const coverImg = ref("");
const showIm = ref(false);
function getbox1Bg() {
  let params = {
    classId: 11050,
  };
  getBg(params).then((res) => {
    home_1_imgUrls.value = res.imgUrls;
    home_1_videoUrl.value = res.videoUrl;
    titleContext.value = {
      title: res.title,
      subTitle: res.subTitle,
      detail: res.detail,
      buttonName: res.buttonName,
      buttonUrl: res.buttonUrl,
    };
    coverImg.value = res.cover;
  });
}
function getPolicyData() {
  getPolicy().then((res) => {
    home_3_list.value = res;
  });
}
function goDetail(e) {
  router.push({
    name: "policyGuidce",
    query: { contentId: e, title: home_3_msg.value.title },
  });
}
function getBox2Data() {
  let params = {
    classId: 11051,
  };
  getApplication(params).then((res) => {
    home_2_list.value = res;
  });
}
function getTitleConfig() {
  let params = {
    classId: ["11116", "11053", "11123", "11052"],
  };
  getConfig(params).then((res) => {
    res.forEach((e) => {
      if (e.classId == 11116) {
        home_2_msg.value = e;
      } else if (e.classId == 11053) {
        home_3_msg.value = e;
      } else if (e.classId == 11123) {
        home_4_msg.value = e;
      } else if (e.classId == 11052) {
        home_5_msg.value = e;
      }
    });
  });
}
function getActive() {
  let params = {
    classId: 11054,
  };
  getActivities(params).then((res) => {
    res.forEach((e) => {
      if (e.isRec == "1") {
        sideMsg.value = e;
      } else {
        home_4_list.value.push(e);
      }
    });
  });
}
function goNews(e) {
  router.push({
    name: "newsDetail",
    query: { contentId: e.contentId, flag: true },
  });
}

function goCreateDetail(e) {
  router.push({ name: "createDetail", query: { contentId: e } });
}
function getCP() {
  getPartner().then((res) => {
    home_6_list.value = res;
    home_6_list.value.forEach((res) => {
      res.forEach((e) => {
        if (e.length < 34) {
          let num = 34 - e.length;
          for (let i = 0; i < num; i++) {
            e.push({
              contentId: i,
              detail: "",
              image: "",
              logo: "",
              subTitle: "",
              title: "",
              type: "",
            });
          }
        }
      });
    });
  });
}
function goPage(path) {
  const regex = /(https?)/;
  if (regex.test(path)) {
    window.open(unescapeHTML(path));
  } else {
    router.push(path);
    localStorage.setItem("pagePath", path);
  }
}
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
// function getVisition(){
//   let spt = document.createElement('script')
//   let contentId = localStorage.getItem('pagePath')
//   spt.src = apiUrl.vue
// }
onMounted(() => {
  getbox1Bg();
  getPolicyData();
  getBox2Data();
  getTitleConfig();
  getCP();
  getActive();
});
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  #home {
    font-family: MiSans;
    :deep(.swiper) {
      display: flex;
      overflow: hidden;
      width: 100%;
      .swiper-wrapper {
        display: flex;
      }
    }
    :deep(.swiper-button-prev1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      left: -80rem;
      transform: rotate(180deg);
      background: rgba(237, 237, 237, 0.6);
    }
    :deep(.swiper-button-next1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      right: -80rem;
      left: auto;
      transform: rotate(180deg);
      background: #f6f6f6;
    }
    .swiper-button-prev:after,
    .swiper-button-next:after {
      font-family: "";
    }
    .swiper-button-next:after {
      content: "";
    }
    .swiper-button-prev:after {
      content: "";
    }
    .home_box {
      height: 100vh;
      width: 100%;
      position: relative;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      position: relative;
      .home_title {
        line-height: 96rem;
        color: rgba(56, 56, 56, 1);
        font-size: 50rem !important;
        font-weight: 900;
        letter-spacing: 0rem;
        margin-bottom: 4rem;
      }
      .home_title_1 {
        font-size: 32rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47.74rem;
        color: rgba(128, 128, 128, 1);
      }
      .home_detail {
        font-size: 24rem;
        font-weight: 300;
        letter-spacing: 1rem;
        /* line-height: 24rem; */
        color: rgba(128, 128, 128, 1);
        width: 1133rem;
        margin-top: 22rem;
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: wrap;
      }
      .home_more {
        position: absolute;
        right: 0;
        top: 0;
        /* width: 130rem; */
        height: 28rem;
        font-size: 16rem;
        font-weight: 500;
        line-height: 28rem;
        color: rgba(40, 41, 56, 1);
      }
      .home_box_header {
        display: flex;
        flex-direction: column;
        position: relative;
        /* margin: calc(57 / 1080 * 100%) auto 0; */
        margin: 60rem auto 0;
        width: 1600rem;
      }
      .carousel-img {
        width: 100%;
        height: 240rem;
        object-fit: cover;
      }
    }
    img {
      // width: 100%;
    }

    .home_box_1 {
      /* background: url("../../assets/home1.gif"); */
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      .glass {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        /* background: linear-gradient(
          to right,
          rgba(13, 46, 153, 0.8) 0%,
          rgba(70, 128, 255, 0) 100%
        ); */
      }
      .banner-content {
        position: absolute;
        top: calc(308 / 1080 * 100vh);
        left: 160rem;
        font-size: 20rem;
        letter-spacing: 1rem;
        width: 75%;
        color: rgba(255, 255, 255, 1);
        & > .title {
          font-size: 50rem;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .subTitle {
          font-size: 36rem;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .context {
          margin-top: 26rem;
          line-height: 28rem;
          font-size: 18rem;
          font-weight: 300;
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        position: absolute;
        bottom: 7%;
        left: 50%;
        transform: translateX(50%);
        animation: blink 2s infinite;
      }
      .home_box_1_carousel {
        height: 100%;
        position: relative;
        .home_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
    }
    .home_box_2 {
      position: relative;
      /* height: 110vh; */
      height: 100%;
      .home_boex_2_bg {
        width: 139rem;
        height: 95rem;
        left: calc(59 / 1903 * 100%);
        top: calc(813 / 1080 * 116vh);
        position: absolute;
        background: url(../../assets//home/<USER>
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
      }
      .home_box_2_top {
        display: flex;
        flex-direction: column;
        /* margin-left: 160rem; */
        /* margin-top: 100rem; */
        width: 1600rem;
        /* margin: calc(100 / 1080 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .content {
        z-index: 1;
        display: flex;
        width: 1600rem;
        margin: 30rem auto 40rem;
        position: relative;
        .home_box_2_card {
          width: calc(370 / 1600 * 100%) !important;
          height: 100%;
          /* margin-right: 40rem; */
          .home_box_2_item {
            height: 100%;
            width: 100%;
            position: relative;
            overflow: hidden;
            transition: transform 1s;
            img {
              width: 100%;
              height: 100%;
              object-fit: fill;
              transition: transform 1s;
            }
            .home_box_2_text {
              position: absolute;
              bottom: 0;
              width: 100%;
              height: 464rem;
              /* opacity: 0.8; */
              background: linear-gradient(
                0deg,
                rgba(13, 46, 153, 0.8) 0%,
                rgba(13, 46, 153, 0) 100%
              );
              .title {
                margin-top: 125rem;
                line-height: 47.74rem;
                color: #ffffff;
                font-size: 36rem;
                font-weight: 700;
                text-align: center;
                vertical-align: top;
              }
              .fTitle {
                opacity: 1;
                font-size: 14rem;
                font-weight: 300;
                letter-spacing: 2rem;
                line-height: 19rem;
                color: #ffffff;
                text-align: center;
                vertical-align: top;
              }
              .Hbar {
                width: 80rem;
                height: 8rem;
                background: #ffffff;
                margin: 16rem auto 0;
              }
              .con {
                width: 300rem;
                height: 90rem;
                font-size: 18rem;
                font-weight: 300;
                /* line-height: 18rem; */
                color: #ffffff;
                text-align: left;
                vertical-align: top;
                margin: 8rem auto 0;
                -webkit-line-clamp: 5;
                text-overflow: ellipsis;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
              }
              .Hbottom {
                width: 104rem;
                height: 54rem;
                border-radius: 60rem;
                border: 1rem solid #ffffff;
                margin: 20rem auto 0;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
            img {
              height: 100%;
              width: 100%;
            }
          }
          &:nth-child(n + 1) {
            margin-right: 34rem;
          }
        }

        .home_box_2_card:hover {
          cursor: pointer;
          img {
            transform: scale(1.5); /* 放大图片两倍 */
          }
        }

        .ownSwiper {
          display: flex;
          flex-direction: row;
          justify-content: center;
          width: 100%;
          margin-top: -3rem;
          .swiperBtn {
            border: 0;
            box-shadow: 0 0.25rem 0.75rem rgba(30, 34, 40, 0.02);
            width: 2.2rem;
            height: 2.2rem;
            line-height: inherit;
            border-radius: 100%;
            text-shadow: none;
            transition: all 0.2s ease-in-out;
            background: rgba(var(--bs-primary-rgb), 0.9) !important;
            margin: 0 0.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .btn-disabled {
            background: rgba(var(--bs-primary-rgb), 0.7) !important;
            opacity: 0.35;
            cursor: auto;
            pointer-events: none;
          }
          .button-prev:after {
            content: "\e949";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
          .button-next:after {
            content: "\e94c";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
        }
      }
    }
    .home_box_3 {
      position: relative;
      background-color: rgba(245, 245, 245, 1);
      display: flex;
      flex-direction: column;
      /* height: 110vh; */
      height: 100%;
      // align-items: center;
      .carousel {
        // height: 60%;
        width: 100%;
        flex: 1;
        display: flex;
        align-items: center;
        // z-index: 15;
      }
      .home_box_3_header {
        display: flex;
        flex-direction: column;
        /* margin-left: 160rem; */
        /* margin-top: 50rem; */
        position: relative;
        width: 1600rem;
        /* margin: calc(50 / 929 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .home_box_3_list {
        margin: 36rem auto 0;
        display: flex;
        overflow: hidden;
        justify-content: space-between;
        width: 1600rem;
        .list_item {
          width: 506rem;
          height: 600rem;
          margin-right: 41rem;
          .list_img {
            width: 506rem;
            height: 310rem;
            border-radius: 10rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .list_title {
            width: 100%;
            height: 32rem;
            margin-top: 34rem;
            font-size: 24rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .list_content {
            font-size: 18rem;
            font-weight: 400;
            line-height: 25rem;
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            -webkit-line-clamp: 3;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden;
            margin-top: 12rem;
            text-align: justify;
            &:after {
              content: "";
              display: inline-block;
              width: 100%;
            }
          }
          .list_learn {
            width: 151rem;
            height: 30rem;
            font-size: 18rem;
            font-weight: 500;
            letter-spacing: 0rem;
            line-height: 30rem;
            color: rgba(26, 26, 26, 1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 48rem;
            img {
              width: 16rem;
              height: 14rem;
            }
          }
        }
        .list_item:hover {
          cursor: pointer;
          .list_content {
            color: #005ced;
          }
        }
      }
    }
    .home_box_3 :deep(.n-carousel__dots) {
      bottom: -50rem;
    }
    .home_box_3 :deep(.n-carousel.n-carousel--card) {
      .n-carousel__slide.n-carousel__slide--next {
        opacity: 1;
      }
      .n-carousel__slide.n-carousel__slide--prev {
        opacity: 1;
      }
    }
    .home_box_4 {
      /* height: 116vh; */
      height: 100%;
      background-color: #ffffff;
      position: relative;
      display: flex;
      flex-direction: column;
      .home_box_4_content {
        top: 0;
        left: 0;
        height: 65%;
        width: 1600rem;
        margin: 48rem auto 40rem;
        overflow: hidden;
        display: flex;
        .home_box_4_content_left {
          height: 100%;
          min-width: 60%;
          margin-right: 48rem;
          display: flex;
          flex-direction: column;
          .left_img {
            width: 1138rem;
            height: 613rem;
            border-radius: 20rem 0rem 20rem 0rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left_title {
            font-size: 24rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            margin-top: 28rem;
            margin-bottom: 8rem;
          }
          .left_text {
            width: 1138rem;
            font-size: 18rem;
            font-weight: 400;
            line-height: 24rem;
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
        .home_box_4_content_left:hover {
          cursor: pointer;
          .left_text {
            color: #005ced;
          }
        }
        .home_box_4_content_right {
          display: flex;
          flex-direction: column;
          .rightItem {
            display: flex;
            flex-direction: column;
            .right_img {
              width: 413rem;
              height: 222rem;
              margin-bottom: rem;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .right_title {
              font-size: 24rem;
              font-weight: 700;
              line-height: 32rem;
              color: rgba(56, 56, 56, 1);
              width: 402rem;
              height: 32rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin: 28rem 0 8rem;
            }
            .right_text {
              font-size: 18rem;
              font-weight: 400;
              line-height: 24rem;
              color: rgba(91, 91, 91, 1);
              width: 402rem;
              overflow: hidden;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:first-child {
              margin-bottom: 54rem;
            }
          }
          .rightItem:hover {
            cursor: pointer;
            .right_text {
              color: #005ced;
            }
          }
        }
      }
    }
    .home_box_6 {
      display: flex;
      position: relative;
      flex-direction: column;
      height: 100%;
      background: rgba(250, 250, 250, 1);
      .home_box_6_content {
        width: 1600rem;
        margin: 75rem auto 40rem;
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: #005ced;
        }
        .home_box_6_carousel {
          width: 1600rem;
          display: flex;
          margin: 0 auto;
          flex-wrap: wrap;
          position: relative;
          .cLine {
            width: 100%;
            justify-content: center;
            display: flex;
            &:nth-child(2n) {
              transform: translateX(-100rem);
              :nth-child(n + 4) {
                transform: translateX(200rem);
              }
            }
            &:nth-child(3) {
              &:nth-child(-n + 3) {
                transform: translateX(-200rem);
              }
              :nth-child(n + 4) {
                transform: translateX(400rem);
              }
            }
            &:not(:first-child) {
              margin-top: -100rem;
            }
            .cItem {
              width: 200rem;
              height: 200rem;
              clip-path: polygon(50% 5%, 95% 50%, 50% 95%, 5% 50%);
              background: rgba(242, 242, 242, 1);
              display: flex;
              justify-content: center;
              align-items: center;
              img {
                max-width: 45%;
                height: auto;
              }
            }
          }
          .dItem {
            width: 380rem;
            height: 380rem;
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            background: rgba(13, 46, 153, 1);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
  }

  #tsparticles {
    canvas {
      position: absolute !important;
      top: 0;
      left: 0;
    }
  }
  .underline {
    background-color: #005ced;
    height: 2rem;
    width: 17rem;
  }
}
@media screen and (max-width: 768px) {
  #home {
    font-family: MiSans;
    :deep(.swiper) {
      display: flex;
      overflow: hidden;
      width: 100%;
      .swiper-wrapper {
        display: flex;
      }
    }
    :deep(.swiper-button-prev1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      left: -80rem;
      transform: rotate(180deg);
      background: rgba(237, 237, 237, 0.6);
    }
    :deep(.swiper-button-next1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      right: -80rem;
      left: auto;
      transform: rotate(180deg);
      background: #f6f6f6;
    }
    .swiper-button-prev:after,
    .swiper-button-next:after {
      font-family: "";
    }
    .swiper-button-next:after {
      content: "";
    }
    .swiper-button-prev:after {
      content: "";
    }
    .home_box {
      height: 50vh;
      width: 100%;
      position: relative;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      position: relative;
      .home_title {
        line-height: 96rem;
        color: rgba(56, 56, 56, 1);
        font-size: 50rem !important;
        font-weight: 900;
        letter-spacing: 0rem;
        margin-bottom: 4rem;
      }
      .home_title_1 {
        font-size: 32rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47.74rem;
        color: rgba(128, 128, 128, 1);
      }
      .home_detail {
        font-size: 24rem;
        font-weight: 300;
        letter-spacing: 1rem;
        /* line-height: 24rem; */
        color: rgba(128, 128, 128, 1);
        /* width: 1133rem; */
        margin-top: 22rem;
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: wrap;
      }
      .home_more {
        position: absolute;
        right: 0;
        top: 0;
        /* width: 130rem; */
        height: 28rem;
        font-size: 16rem;
        font-weight: 500;
        line-height: 28rem;
        color: rgba(40, 41, 56, 1);
      }
      .home_box_header {
        display: flex;
        flex-direction: column;
        position: relative;
        /* margin: calc(57 / 1080 * 100%) auto 0; */
        margin: 60rem auto 0;
        width: 720rem;
      }
      .carousel-img {
        width: 100%;
        height: 240rem;
        object-fit: cover;
      }
    }
    img {
      // width: 100%;
    }

    .home_box_1 {
      /* background: url("../../assets/home1.gif"); */
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      .glass {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
      .banner-content {
        /* position: absolute;
          top: calc(108 / 1080 * 100vh); */
        /* width: calc(708 / 1080 * 100vh); */
        font-size: 20rem;
        letter-spacing: 1rem;
        color: rgba(255, 255, 255, 1);
        width: 720rem;
        margin: 150rem auto 0;
        & > .title {
          font-size: 36rem;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .subTitle {
          font-size: 36rem;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .context {
          margin-top: 6rem;
          line-height: 28rem;
          font-size: 18rem;
          font-weight: 300;
          :deep(p) {
            font-size: 22rem !important;
            span {
              font-size: 22rem !important;
            }
          }
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        position: absolute;
        bottom: 7%;
        left: 50%;
        transform: translateX(-50%);
        animation: blink 2s infinite;
      }
      .home_box_1_carousel {
        height: 100%;
        position: relative;
        .home_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
    }
    .home_box_2 {
      position: relative;
      /* height: 110vh; */
      height: 100%;
      .home_boex_2_bg {
        width: 139rem;
        height: 95rem;
        left: calc(59 / 1903 * 100%);
        top: calc(813 / 1080 * 116vh);
        position: absolute;
        background: url(../../assets//home/<USER>
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
      }
      .home_box_2_top {
        display: flex;
        flex-direction: column;
        /* margin-left: 160rem; */
        /* margin-top: 100rem; */
        width: 720rem;
        /* margin: calc(100 / 1080 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .content {
        z-index: 1;
        display: flex;
        margin: 30rem auto 40rem;
        width: 720rem;
        position: relative;
        .home_box_2_card {
          width: calc(360 / 780 * 100%) !important;
          height: 100%;
          /* margin-right: 40rem; */
          .home_box_2_item {
            height: 100%;
            width: 100%;
            position: relative;
            overflow: hidden;
            transition: transform 1s;
            img {
              width: 100%;
              height: 100%;
              object-fit: fill;
              transition: transform 1s;
            }
            .home_box_2_text {
              position: absolute;
              bottom: 0;
              width: 100%;
              height: 464rem;
              /* opacity: 0.8; */
              background: linear-gradient(
                0deg,
                rgba(13, 46, 153, 0.8) 0%,
                rgba(13, 46, 153, 0) 100%
              );
              .title {
                margin-top: 125rem;
                line-height: 47.74rem;
                color: #ffffff;
                font-size: 36rem;
                font-weight: 700;
                text-align: center;
                vertical-align: top;
              }
              .fTitle {
                opacity: 1;
                font-size: 14rem;
                font-weight: 300;
                letter-spacing: 2rem;
                line-height: 19rem;
                color: #ffffff;
                text-align: center;
                vertical-align: top;
              }
              .Hbar {
                width: 80rem;
                height: 8rem;
                background: #ffffff;
                margin: 16rem auto 0;
              }
              .con {
                width: 300rem;
                height: 90rem;
                font-size: 18rem;
                font-weight: 300;
                /* line-height: 18rem; */
                color: #ffffff;
                text-align: left;
                vertical-align: top;
                margin: 8rem auto 0;
                -webkit-line-clamp: 5;
                text-overflow: ellipsis;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
              }
              .Hbottom {
                width: 104rem;
                height: 54rem;
                border-radius: 60rem;
                border: 1rem solid #ffffff;
                margin: 20rem auto 0;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
            img {
              height: 100%;
              width: 100%;
            }
          }
          &:nth-child(n + 1) {
            margin-right: 30rem;
          }
        }

        .home_box_2_card:hover {
          cursor: pointer;
          img {
            transform: scale(1.5); /* 放大图片两倍 */
          }
        }

        .ownSwiper {
          display: flex;
          flex-direction: row;
          justify-content: center;
          width: 100%;
          margin-top: -3rem;
          .swiperBtn {
            border: 0;
            box-shadow: 0 0.25rem 0.75rem rgba(30, 34, 40, 0.02);
            width: 2.2rem;
            height: 2.2rem;
            line-height: inherit;
            border-radius: 100%;
            text-shadow: none;
            transition: all 0.2s ease-in-out;
            background: rgba(var(--bs-primary-rgb), 0.9) !important;
            margin: 0 0.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .btn-disabled {
            background: rgba(var(--bs-primary-rgb), 0.7) !important;
            opacity: 0.35;
            cursor: auto;
            pointer-events: none;
          }
          .button-prev:after {
            content: "\e949";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
          .button-next:after {
            content: "\e94c";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
        }
      }
    }
    .home_box_3 {
      position: relative;
      background-color: rgba(245, 245, 245, 1);
      display: flex;
      flex-direction: column;
      /* height: 110vh; */
      height: 100%;
      // align-items: center;
      .carousel {
        // height: 60%;
        width: 100%;
        flex: 1;
        display: flex;
        align-items: center;
        // z-index: 15;
      }
      .home_box_3_header {
        display: flex;
        flex-direction: column;
        /* margin-left: 160rem; */
        /* margin-top: 50rem; */
        position: relative;
        width: 720rem;
        /* margin: calc(50 / 929 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .home_box_3_list {
        margin: 36rem auto 40rem;
        display: flex;
        overflow: hidden;
        flex-direction: column;
        width: 720rem;
        align-items: center;
        .list_item {
          width: 700rem;
          margin-top: 20rem;
          /* height: 600rem; */
          .list_img {
            width: 700rem;
            height: 429rem;
            border-radius: 10rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .list_title {
            width: 100%;
            height: 32rem;
            margin-top: 34rem;
            font-size: 28rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .list_content {
            font-size: 24rem;
            font-weight: 400;
            /* line-height: 25rem; */
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            -webkit-line-clamp: 3;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden;
            margin-top: 12rem;
            text-align: justify;
            &:after {
              content: "";
              display: inline-block;
              width: 100%;
            }
          }
          .list_learn {
            width: 151rem;
            height: 30rem;
            font-size: 18rem;
            font-weight: 500;
            letter-spacing: 0rem;
            line-height: 30rem;
            color: rgba(26, 26, 26, 1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 48rem;
            img {
              width: 16rem;
              height: 14rem;
            }
          }
        }
        .list_item:hover {
          cursor: pointer;
          .list_content {
            color: #005ced;
          }
        }
      }
    }
    .home_box_3 :deep(.n-carousel__dots) {
      bottom: -50rem;
    }
    .home_box_3 :deep(.n-carousel.n-carousel--card) {
      .n-carousel__slide.n-carousel__slide--next {
        opacity: 1;
      }
      .n-carousel__slide.n-carousel__slide--prev {
        opacity: 1;
      }
    }
    .home_box_4 {
      /* height: 116vh; */
      height: 100%;
      background-color: #ffffff;
      position: relative;
      display: flex;
      flex-direction: column;
      .home_box_4_content {
        height: 65%;
        width: 720rem;
        margin: 48rem auto 40rem;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .home_box_4_content_left {
          height: 100%;
          width: 100%;
          margin-right: 48rem;
          display: flex;
          flex-direction: column;
          .left_img {
            width: 100%;
            height: 307rem;
            border-radius: 20rem 0rem 20rem 0rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left_title {
            font-size: 28rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            margin-top: 28rem;
            margin-bottom: 8rem;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
          }
          .left_text {
            /* width: 1138rem; */
            font-size: 24rem;
            font-weight: 400;
            /* line-height: 24rem; */
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
        .home_box_4_content_left:hover {
          cursor: pointer;
          .left_text {
            color: #005ced;
          }
        }
        .home_box_4_content_right {
          display: flex;
          flex-direction: column;
          .rightItem {
            display: flex;
            flex-direction: column;
            margin-top: 20rem;
            .right_img {
              width: 100%;
              height: 387rem;
              margin-bottom: 16rem;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .right_title {
              font-size: 28rem;
              font-weight: 700;
              line-height: 32rem;
              color: rgba(56, 56, 56, 1);
              /* width: 402rem; */
              height: 32rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 10rem;
            }
            .right_text {
              font-size: 24rem;
              font-weight: 400;
              color: rgba(91, 91, 91, 1);
              overflow: hidden;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:first-child {
              margin-bottom: 14rem;
            }
          }
          .rightItem:hover {
            cursor: pointer;
            .right_text {
              color: #005ced;
            }
          }
        }
      }
    }
    .home_box_6 {
      display: flex;
      position: relative;
      flex-direction: column;
      height: 100%;
      background: rgba(250, 250, 250, 1);
      .home_box_6_content {
        width: 720rem;
        margin: 75rem auto 40rem;
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: #005ced;
        }
        .home_box_6_carousel {
          width: 720rem;
          display: flex;
          margin: 0 auto;
          flex-wrap: wrap;
          position: relative;
          .cLine {
            width: 100%;
            justify-content: center;
            display: flex;
            &:nth-child(2n) {
              transform: translateX(-45rem);
              :nth-child(n + 4) {
                transform: translateX(90rem);
              }
            }
            &:nth-child(3) {
              &:nth-child(-n + 3) {
                transform: translateX(-90rem);
              }
              :nth-child(n + 4) {
                transform: translateX(180rem);
              }
            }
            &:not(:first-child) {
              margin-top: -45rem;
            }
            .cItem {
              width: 90rem;
              height: 90rem;
              clip-path: polygon(50% 5%, 95% 50%, 50% 95%, 5% 50%);
              background: rgba(242, 242, 242, 1);
              display: flex;
              justify-content: center;
              align-items: center;
              img {
                max-width: 45%;
                height: auto;
              }
            }
          }
          .dItem {
            width: 171rem;
            height: 171rem;
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            background: rgba(13, 46, 153, 1);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
  }

  #tsparticles {
    canvas {
      position: absolute !important;
      top: 0;
      left: 0;
    }
  }
  .underline {
    background-color: #005ced;
    height: 2rem;
    width: 17rem;
  }
}
.customer-service {
  width: 80rem;
  height: 80rem;
  background: #ffffff;
  border-radius: 50%;
  position: fixed;
  right: 100rem;
  bottom: 100rem;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  img{
    width: 80%;
  }
}
</style>
