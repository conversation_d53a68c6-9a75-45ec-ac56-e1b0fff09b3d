<template>
  <div id="legal">
    <div class="legal_box">
      <div style="margin-bottom: 30px">
        <n-breadcrumb separator=">">
          <n-breadcrumb-item> 首页</n-breadcrumb-item>
          <n-breadcrumb-item> 政策指引</n-breadcrumb-item>
          <n-breadcrumb-item> 国家法律规范</n-breadcrumb-item>
        </n-breadcrumb>
      </div>
      <div class="legal_box_top">
        <div class="top_left">
          <img src="../../assets/home/<USER>" alt="" />
        </div>
        <div class="top_right">
          <div class="title">企业信用评价</div>
          <div class="content">
            通过输入企业名称，查询企业当下的信用评分。do met sent. RELIT
            official consequent door ENIM RELIT Mollie. Excitation venial
            consequent sent nostrum met.
          </div>
          <div class="line"></div>
          <div class="btnList" style="margin-top: 24px">
            <n-button
              strong
              secondary
              round
              type="tertiary"
              style="margin-right: 14px"
            >
              信用评价
            </n-button>
            <n-button strong secondary round type="tertiary">
              高新企业
            </n-button>
          </div>
          <n-button
            round
            size="large"
            color="#169BD5"
            style="width: 200px; margin: 178px 0 0 240px"
            >立即申请</n-button
          >
        </div>
      </div>
      <div class="content">
        <div class="ctitle">基本信息</div>
        <div class="cText">
          第一条
          为了规范数据出境活动，保护个人信息权益，维护国家安全和社会公共利益，促进数据跨境安全、自由流动，根据《中华人民共和国网络安全法》、《中华人民共和国数据安全法》、《中华人民共和国个人信息保护法》等法律法规，制定本办法。第二条
          数据处理者向境外提供在中华人民共和国境内运营中收集和产生的重要数据和个人信息的安全评估，适用本办法。法律、行政法规另有规定的，依照其规定。第三条数据出境安全评估坚持事前评估和持续监督相结合、风险自评估与安全评估相结合，防范数据出境安全风险，保障数据依法有序自由流动。第四条
          数据处理者向境外提供数据，有下列情形之一的，应当通过所在地省级网信部门向国家网信部门申报数据出境安全评估:(一)数据处理者向境外提供重要数据:(二)关键信息基础设施运营者和处理100万人以上个人信息的数据处理者向境外提供个人信息;(三)自上年1月1日起累计向境外提供10万人个人信息或者1万人敏感个人信息的数据处理者向境外提供个人信息;(四)国家网信部门规定的其他需要申报数据出境安全评估的情形。第五条
          数据处理者在申报数据出境安全评估前，应当开展数据出境风险自评估，重点评估以下事项:(一)数据出境和境外接收方处理数据的目的、范围、方式等的合法性、正当性、必要性;(二)出境教据的规模、范围、种类、敏感程度，数据出境可能对国家安全、公共利益、个人或者组织合法权益带来的风险:(三)境外接收方承诺承担的责任义务，以及履行责任义务的管理和技术措施、能力等能否保障出境数据的安全;(四)数据出境中和出境后遭到築改、破坏、泄露、丢失、转移或者被非法获取、非法利用等的风险，个人信息权益维护的渠道是否通畅等;(五)与境外接收方拟订立的数据出境相关合同或者其他具有法律效力的文件等(以下统称法律文件)是否充分约定了数据安全保护责任义务;(六)其他可能影响数据出境安全的事项。第六条
          申报数据出境安全评估，应当提交以下材料:(一)申报书;(二)数据出境风险自评估报告;(三)数据处理者与境外接收方拟订立的法律交件;(四)安全评估工作需要的其他材料。第七条
          省级网信部门应当自收到申报材料之日起5个工作日内完成完备性查验。申报材料齐全的，将申报材料报送国家网信部门;申报材料不齐全的，应当退回数据处理者并一次性告知需要补充的材料。国家网信部门应当自收到申报材料之日起7个工作日内，确定是否受理并书面通知数据处理者。第八条数据出境安全评估重点评估数据出境活动可能对国家安全、公共利益、个人或者组织合法权益带来的风险，主要包括以下事项:(一)数据出境的目的、范围、方式等的合法性、正当性、必要性;(二)境外接收方所在国家或者地区的数据安全保护政策法规和网络安全环境对出境数据安全的影响;境外接收方的数据保护水平是否达到中华人民共和国法律、行政法规的规定和强制性国家标准的要求;(三)出境数据的规模、范围、种类、敏感程度，出境中和出境后遭到改、破坏、泄露、丢失、转移或者被非法获取、非法利用等的风险;
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts"></script>
<style lang="less" scoped>
#legal {
  min-width: 1000px;
  .legal_box {
    height: 200vh;
    width: 100%;
    position: relative;
    padding: 128px 360px 0;
    .legal_box_top {
      display: flex;
      flex-direction: row;
      .top_left {
        width: 657px;
        height: 450px;
        img {
          width: 100%;
        }
      }
      .top_right {
        margin-left: 36px;
        .title {
          font-size: 36px;
          font-weight: 700;
          line-height: 30px;
          color: rgba(37, 43, 66, 1);
          margin-top: 24px;
        }
        .content {
          width: 419px;
          height: 60px;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          color: rgba(85, 85, 85, 1);
          letter-spacing: 0.2px;
          margin-top: 30px;
        }
        .line {
          width: 445px;
          height: 1px;
          background: rgba(189, 189, 189, 0.6);
          margin-top: 28px;
        }
      }
    }
    .content {
      margin-top: 14px;
      width: 1200px;
      .ctitle {
        height: 80px;
        font-size: 20px;
        font-weight: 700;
        color: rgba(115, 115, 115, 1);
        border-top: 1px solid rgba(189, 189, 189, 0.6);
        border-bottom: 1px solid rgba(189, 189, 189, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .cText {
        font-size: 18px;
        font-weight: 400;
        line-height: 30px;
        color: rgba(85, 85, 85, 0.8);
      }
    }
  }
}
</style>
