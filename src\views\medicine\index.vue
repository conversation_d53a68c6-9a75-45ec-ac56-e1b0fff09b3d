<template>
  <div id="home">
    <div
      class="home-wrapper"
      style="padding: 0; height: 100vh"
      v-if="!isMobile"
    >
      <n-carousel
        dot-type="line"
        autoplay
        :draggable="bannerList.list && bannerList.list.length > 1"
      >
        <template v-for="(item, index) in bannerList.list">
          <template v-if="item.type == '2'">
            <video
              :src="item.url"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%"
              :poster="apiUrl + coverImg"
            ></video>
          </template>
          <template v-else-if="item.type == '1'">
            <img class="carousel-img" :src="item.url" />
          </template>
        </template>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              @click="to(index - 1)"
            />
          </ul>
        </template>
      </n-carousel>
      <div class="glass">
        <div class="banner-content">
          <div class="title">{{ titleContext.title }}</div>
          <div class="subTitle" v-if="false">{{ titleContext.subTitle }}</div>
          <div class="context" v-html="titleContext.context"></div>
        </div>
        <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div>
      </div>
    </div>
    <div class="home-wrapper" style="padding: 0; height: 50vh" v-else>
      <n-carousel
        dot-type="line"
        autoplay
        :draggable="bannerList.list && bannerList.list.length > 1"
      >
        <template v-for="(item, index) in bannerList.list">
          <template v-if="item.type == '2'">
            <video
              :src="item.url"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%"
            ></video>
          </template>
          <template v-else-if="item.type == '1'">
            <img class="carousel-img" :src="item.url" />
          </template>
        </template>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              @click="to(index - 1)"
            />
          </ul>
        </template>
      </n-carousel>
      <div class="glass">
        <div class="banner-content">
          <div class="title">{{ titleContext.title }}</div>
          <div class="subTitle" v-if="false">{{ titleContext.subTitle }}</div>
          <div class="context" v-html="titleContext.context"></div>
        </div>
        <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div>
      </div>
    </div>

    <div class="tablePart bg-gray">
      <div class="title">
        {{ title[0] ? title[0].title : "" }}
      </div>
      <div class="rightBody">
        <n-data-table
          :data="tableData"
          :columns="columns"
          size="large"
          ref="tableRef"
          max-height="465rem"
          style="margin-top: 28rem; height: 575rem"
        />
      </div>
    </div>
    <div class="home-wrapper">
      <div class="case-box">
        <div class="dotMatrix"></div>
        <div class="dotMatrix"></div>
        <div class="title">{{ title[1].title }}</div>
        <div class="subTitle" v-if="false">
          {{ title[1].subTitle }}
        </div>
        <div class="subTitle" v-else></div>
        <div class="main">
          <div class="side-menu">
            <div
              class="side-menu-item"
              :class="item.active == true ? 'active' : ''"
              v-for="(item, index) in caseList"
              @click="handleCase(item)"
            >
              <img :src="item.logo" />
              <span
                style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
                >{{ item.title }}</span
              >
            </div>
          </div>
          <div class="board">
            <img :src="selectedCase.image" alt="" style="height: 70%" />
            <div class="imgBox">
              <div class="swiper-button-prev swiper-button-next1">
                <img :src="toLeft" />
              </div>
              <div class="swiper-button-next swiper-button-prev1">
                <img :src="toRight" />
              </div>
              <!-- <swiper
                @swiper="setSwiper1"
                :slides-per-view="3"
                :autoplay="{
                  delay: 1000,
                  disableOnInteraction: true,
                }"
                :navigation="{
                  nextEl: '.swiper-button-next1',
                  prevEl: '.swiper-button-prev1',
                }"
                :loop="true"
                :slidesPerGroup="1"
                :loopedSlides="3"
                :spaceBetween="5"
                class="swiperRef"
                :modules="modules"
              > -->
              <swiper
                :slides-per-view="3"
                :autoplay="{
                  delay: 3000,
                  disableOnInteraction: true,
                }"
                :navigation="{
                  nextEl: '.swiper-button-next1',
                  prevEl: '.swiper-button-prev1',
                }"
                :loop="true"
                :slidesPerGroup="1"
                :loopedSlides="3"
                :spaceBetween="5"
                class="swiperRef"
                :modules="modules"
              >
                <swiper-slide
                  class="imgItem"
                  :class="imgIndex == index ? 'activeImg' : ''"
                  v-for="(item, index) in appList"
                  @click="changImage(item, index)"
                >
                  <!-- <div
                class="imgItem"
                :class="imgIndex == index ? 'activeImg' : ''"
                v-for="(item, index) in prodList.prod.slice(0, 3)"
                @click="changImage(item, index)"
              > -->
                  <img :src="apiUrl + item.imageUrl" alt="" />
                  <div class="img-glass">
                    <div class="img-glass-content">
                      <div class="title">{{ item.title }}</div>
                      <div class="sub-context">
                        <!-- <div class="sub-context-context">
                          {{ item.intro }}
                        </div> -->
                        <div class="prod-tag">
                          <div class="prod-tag-item" v-for="i in item.labels">
                            {{ i }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- </div> -->
                </swiper-slide>
              </swiper>
            </div>
            <div class="case-glass">
              <div
                class="case-glass-content"
                style="padding: 0; justify-content: center"
              >
                <!-- <div class="title">{{ selectedCase.subTitle }}</div> -->
                <div class="title">{{ selectedCase.title }}</div>
                <div class="sub-context" style="display: flex; flex: none">
                  <!-- <div class="sub-context-title">
                      {{ selectedCase.title }}
                    </div> -->
                  <!-- <div class="sub-context-middle">
                      {{ selectedCase.subTitle }}
                    </div> -->
                  <div
                    class="sub-context-context"
                    style="display: flex"
                    v-html="selectedCase.detail"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home-wrapper-free">
      <div class="prod-box">
        <div class="title" id="prod-title-title">
          <div class="prod-title-context">{{ title[2].title }}</div>
          <div style="max-width: 50%; width: 40%" v-if="prodMenuList?.length">
            <n-tabs
              type="line"
              animated
              size="large"
              justify-content="space-evenly"
              @update:value="changeProd"
              ref="tabsInstRef"
              pane-wrapper-style="border:0;"
              tab-style="color: rgba(115, 115, 115, 1);font-size: 20rem;font-weight: 400;"
            >
              <n-tab v-for="item in prodMenuList" :name="item.id">
                {{ item.name }}
              </n-tab>
            </n-tabs>
          </div>
        </div>
        <div class="subTitle" v-if="false">
          {{ title[2].subTitle }}
        </div>
        <div class="subTitle" v-else></div>
        <div class="prod-main">
          <template v-if="prodList.prod && prodList.prod.length > 0">
            <div class="prod-item-box">
              <div
                class="prod-item"
                v-for="(item, index) in prodList.prod"
                @click="goPage(`/product?id=${item.contentId}`)"
              >
                <div class="prod-info">
                  <div class="title">
                    <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                      <template #trigger>
                        {{ item.title }}
                      </template>
                      {{ item.title }}
                    </n-tooltip>
                  </div>
                  <div class="context">
                    <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                      <template #trigger>
                        {{ item.intro }}
                      </template>
                      {{ item.intro }}
                    </n-tooltip>
                  </div>
                  <div class="prod-tag">
                    <div class="prod-tag-item" v-for="i in item.labels">
                      {{ i }}
                    </div>
                  </div>
                  <div class="prod-view">
                    <img :src="item.icon" class="prod-view-eye" />
                    <div class="prod-view-text">
                      {{ item.clickNum + "点击" }}
                    </div>
                  </div>
                </div>
                <div class="prod-img">
                  <div class="prod-arrow-box">
                    <img :src="prodArrow" alt="" />
                  </div>
                  <img :src="item.imageUrl" />
                </div>
                <div class="prod-show">
                  <div class="title">
                    <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                      <template #trigger>
                        {{ item.title }}
                      </template>
                      {{ item.title }}
                    </n-tooltip>
                  </div>
                  <div class="context">
                    <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                      <template #trigger>
                        {{ item.intro }}
                      </template>
                      {{ item.intro }}
                    </n-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="empty">
              <n-empty description="当前类目暂无产品"></n-empty>
            </div>
          </template>
        </div>

        <div class="prod-pag">
          <n-pagination
            :page-count="prodList.totalPage"
            v-model:page="prodList.pageNum"
            @update:page="changeProdPage"
          >
            <template #prev>
              <img :src="arrowL" alt="" style="width: 23px; height: 20px" />
            </template>
            <template #next>
              <img :src="arrowR" alt="" style="width: 23px; height: 20px" />
            </template>
          </n-pagination>
        </div>
      </div>
    </div>
    <div class="home-wrapper">
      <div class="advantage-box">
        <div class="dotMatrix"></div>
        <div class="title">
          {{ title[3].title }}
        </div>
        <div class="subTitle" v-if="false">
          {{ title[3].subTitle }}
        </div>
        <div class="subTitle" v-else></div>
        <div class="main">
          <div class="advantage-swiper">
            <swiper
              @swiper="setSwiper"
              @slideChange="onSlideChange"
              :direction="'vertical'"
              :spaceBetween="10"
              :autoplay="{
                delay: 3000,
                disableOnInteraction: false,
              }"
              :modules="modules"
              class="mySwiper"
            >
              <swiper-slide v-for="(item, index) in adList">
                <img :src="item.url" class="ad-swiper-img" />
                <div class="ad-glass">
                  <img
                    :src="item.icon"
                    class="ad-glass-icon"
                    v-if="!isMobile"
                  />
                  <div class="title">{{ item.title }}</div>
                  <!-- <div class="sub-title">
                      {{ item.subTitle }}
                    </div> -->
                  <div class="sub-context" v-html="item.text"></div>
                </div>
              </swiper-slide>
            </swiper>
          </div>
          <div class="advantage-select">
            <div
              class="advantage-select-item"
              :class="item.active == true ? 'active' : ''"
              v-for="(item, index) in adList"
              @click="changeAdSwiper(index)"
            >
              <img :src="item.url" />
              <div class="ad-select-glass">
                <div>
                  <div class="ad-select-icon">
                    <img :src="item.icon" />
                  </div>
                  <div class="ad-select-text">{{ item.title }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home_box_4" style="background-color: rgba(249, 250, 251, 1)">
      <div class="home_box_header">
        <div class="home_title">{{ title[4].title }}</div>
        <div class="home_title_1">{{ home_4_msg.subTitle }}</div>
        <div class="home_detail">{{ home_4_msg.detail }}</div>
        <!-- <div class="home_more dynamicMore" @click="goPage('/news')">
          More ——
        </div> -->
      </div>

      <div class="home_box_4_content">
        <div class="home_box_4_content_left" @click="goNews(sideMsg)">
          <div class="left_img">
            <img v-if="sideMsg.image" :src="apiUrl + sideMsg.image" alt="" />
            <img v-else src="../../assets/home/<USER>" alt="" />
          </div>
          <div class="left_title">{{ sideMsg.title }}</div>
          <div class="left_text">{{ sideMsg.detail }}</div>
        </div>
        <div class="home_box_4_content_right">
          <div
            class="rightItem"
            v-for="item in home_4_list"
            @click="goNews(item)"
          >
            <div class="right_img">
              <img v-if="item.image" :src="apiUrl + item.image" alt="" />
              <img v-else src="../../assets/home/<USER>" alt="" />
            </div>
            <div class="right_title">
              {{ item.title }}
            </div>
            <div class="right_text">
              {{ item.detail }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home_box_4">
      <div class="home_box_header">
        <div class="home_title">{{ activList.title }}</div>
      </div>
    </div>
    <div
      class="home-wrapper"
      style="height: 60vh; display: flex"
      v-if="!isMobile"
    >
      <div class="activity-left-box" style="width: 49%; margin-right: 1%">
        <n-tooltip
          trigger="hover"
          placement="top"
          :style="{ maxWidth: '800rem' }"
        >
          <template #trigger>
            <div
              class="act-context"
              style="margin-top: 20rem; line-height: 40rem"
              v-html="activList.context"
            ></div>
          </template>
          <div v-html="activList.context"></div>
        </n-tooltip>
      </div>
      <div class="activity-box">
        <!-- <img :src="activList.img[0]" alt="" /> -->

        <div class="act-contain">
          <!-- <div class="act-title">{{ activList.title }}</div> -->
          <div class="act-sub-title" v-if="false">
            {{ activList.subTitle }}
          </div>

          <div
            class="act-button"
            @click="goPage(activList.buttonUrl, true)"
            v-show="activList.buttonUrl"
          >
            {{ activList.buttonName }}
          </div>
        </div>
        <img :src="activList.img[0]" alt="" />
      </div>
    </div>
    <div class="home-wrapper" style="height: 48vh" v-else>
      <div class="activity-box">
        <!-- <img :src="activList.img[0]" alt="" /> -->
        <div class="act-contain">
          <div class="act-title">{{ cooperationList.title }}</div>
          <div class="act-sub-title" v-if="false">
            {{ activList.subTitle }}
          </div>
          <div class="act-context" v-html="activList.context"></div>
          <a href=""></a>
          <div
            class="act-button"
            @click="goPage(activList.buttonUrl, true)"
            v-show="activList.buttonUrl"
          >
            {{ activList.buttonName }}
          </div>
        </div>
      </div>
    </div>
    <div class="advertisement">
      <img :src="cooperationList.url" alt="" />
      <div class="advertisement-contain">
        <div
          class="subTitle"
          v-if="false"
          v-html="cooperationList.subTitle"
        ></div>
        <div class="line">
          <div class="icon-box">
            <img :src="cooperationList.icon1" alt="" />
          </div>
          <div class="tel-text">
            {{ cooperationList.tel }}
          </div>
        </div>
        <div class="line">
          <div class="icon-box">
            <img :src="cooperationList.icon2" alt="" />
          </div>
          <div class="address-text">
            {{ cooperationList.address }}
          </div>
        </div>
      </div>
    </div>
    <n-modal v-model:show="showModal">
      <div class="modalBox">
        <div class="title">需求详情</div>
        <div class="tableBox">
          <!--   <div class="lableBox">需求编号</div>
          <div class="rowBox30">{{ ModalData?.DEMANDNUMBER }}</div> -->
          <div class="lableBox">需求单位</div>
          <div class="rowBox30">{{ ModalData?.DEMANDUNIT }}</div>

          <div class="lableBox">场景描述</div>
          <div class="rowBox30">{{ ModalData?.SCENETYPE }}</div>

          <!-- <div class="lableBox">需求类型</div>
          <div class="rowBox30">{{ ModalData?.DEMANDTYPE }}</div> -->

          <!-- <div class="lableBox">需求状态</div>
          <div class="rowBox30">{{ ModalData?.DEMANDSTATUS }}</div> -->
          <div class="lableBox">发布日期</div>
          <div class="rowBox30">{{ ModalData?.RELEASEDATE }}</div>

          <div class="lableBox">截止日期</div>
          <div class="rowBox30">{{ ModalData?.ENDDATE }}</div>
          <!-- <div class="lableBox">产品类型</div>
          <div class="rowBox30">{{ ModalData?.PRODUCTTYPE }}</div>
          <div class="liBox">
            <div class="lableBox">意向人数</div>
            <div class="rowBox80">{{ ModalData?.INTENTIONNUM }}</div>
          </div>
          <div class="liBox">
            <div class="lableBox">需求标签</div>
            <div class="rowBox80">{{ ModalData?.DEMANDLABEL }}</div>
          </div> -->
          <div class="liBox">
            <div class="lableBox">联系方式</div>
            <div class="rowBox80">{{ ModalData?.TEL }}</div>
          </div>
        </div>
        <div class="tableBox">
          <div class="liBox">
            <div class="lableBox" style="float: none">需求说明</div>
            <div class="rowBox80" style="float: none">
              {{ ModalData?.DESCRIBE }}
            </div>
          </div>
        </div>
      </div>
    </n-modal>
    <!-- <div class="placeholder"></div> -->
  </div>
</template>
<script lang="ts" setup>
import {
  reactive,
  computed,
  ref,
  onMounted,
  toRefs,
  nextTick,
  defineComponent,
  h,
} from "vue";
import {
  getZoneCarousel,
  getDataApplication,
  getProductTypeList,
  getBrandType,
  getActivityExchange,
  cooperationConsultation,
  getModuleConfig,
  tradingUpdates,
  getProductListByAppName,
  demandList,
} from "/@/api/special/traffic";
import arrowL from "/@/assets/special/arrow-l.png";
import arrowR from "/@/assets/special/arrow-r.png";
import toLeft from "/@/assets/special/toLeft.png";
import toRight from "/@/assets/special/toRight.png";
import prodArrow from "/@/assets/special/prod-arrow.png";
import { useRouter } from "vue-router";
const router = useRouter();
import { getFileType } from "/@/utils/file";
import dayjs from "dayjs";
import { Swiper, SwiperSlide } from "swiper/vue";
// Import Swiper styles
import "swiper/css";
// import required modules
import { Autoplay, Navigation } from "swiper/modules";
import "swiper/css/navigation";
import CountUp from "vue-countup-v3";
import type { ICountUp, CountUpOptions } from "vue-countup-v3";
import { NButton } from "naive-ui";
const modules = [Autoplay, Navigation];
const useSwiper = ref(null);
const setSwiper = (swiper) => {
  useSwiper.value = swiper;
};
const useSwiper1 = ref(null);
const setSwiper1 = (swiper) => {
  useSwiper1.value = swiper;
};
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
import { getAdvantage } from "/@/api/digtal/index";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const onSlideChange = (swiper) => {
  state.adList.forEach((item) => {
    item.active = false;
  });
  // console.log("onSlideChange", swiper, swiper.realIndex);
  state.adList[swiper.realIndex].active = true;
};
import { useGlobSetting } from "/@/hooks/setting";
import { getConfig } from "/@/api/home/<USER>";
import { getActivities } from "/@/api/digtal/index";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const tabsInstRef = ref(null);
const state = reactive({
  classId: {
    topBanner: "yydblbt", //顶部轮播
    menuProdList: "yytscp", //产品分类目录
    prodList: "yytscp", //产品
    activList: "yysjzqysjs", //活动
  },
  title: [
    {
      title: "",
      subTitle: "",
    },
    {
      title: "",
      subTitle: "",
    },
    {
      title: "",
      subTitle: "",
    },
    {
      title: "",
      subTitle: "",
    },
    {
      title: "",
      subTitle: "",
    },
  ],

  bannerList: {
    list: [],
    img: [],
    video: [],
  },
  titleContext: {
    title: "",
    subTitle: "",
    context: "",
  },
  caseList: [],
  selectedCase: {
    title: "数字货运(网络货运平台)",
    subTitle: "Manufacturing visualization",
    logo: new URL(`/@/assets/special/icon1.png`, import.meta.url).href,
    image: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
    detail:
      "本专区基于国家信用大数据中心、福建省大数据集团有限公司、交通部、工信部等部委的综合监管交通大数据，融合了全国车辆主要汽车制造厂商、高速公路通行等多维度国有大数据资源搭建而成的“交通数据专区”。专区为各企业在供应链过程可视化、运输、保险、金融等领域结合实际应用场景进行综合模型设计，发挥交通数据要素X的应用，精心打造针对不同细分领域的产品及解决方案。",
    active: true,
    id: 1,
  },
  adList: [
    {
      text: "交通部数据源，数据合规，数源稳定，全量数据，多场景设计应用。依托网络货运信用大数据联合实验室，已接入交通部运政类数据，主机厂数据。覆盖全国轻中重卡，新能源车辆数近900万辆。",
      icon: new URL(`/@/assets/special/adicon1.png`, import.meta.url).href,
      url: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
      active: true,
      title: "Data advantages",
      subTitle: "专业模型体系",
      id: 1,
    },
    {
      text: "交通部数据源，数据合规，数源稳定，全量数据，多场景设计应用。依托网络货运信用大数据联合实验室，已接入交通部运政类数据，主机厂数据。覆盖全国轻中重卡，新能源车辆数近900万辆。",
      icon: new URL(`/@/assets/special/adicon1.png`, import.meta.url).href,
      url: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
      active: false,
      title: "Data advantages",
      subTitle: "服务对象广泛",
      id: 2,
    },
  ],
  prodMenuList: [
    // {
    //   title: "Data advantages",
    //   name: "产品特色1",
    //   id: 1,
    // },
  ],
  prodList: {
    pageSize: 6,
    pageNum: 1,
    totalPage: 1,
    type: null,
    prod: [],
  },
  activList: {
    title: "活动交流",
    subTitle: "Event communication",
    context:
      "数据资产管理人才教育培训数据资产管理人才教育培训是由福建省大数据集团有限公司、国家信用大数据创新中心、福建省数据交易所、数字中国等共同发起的聚焦于大数据、人工智能、区块链等新型经济形态的交流平台，云集一流教授资源和行业专家，通过积极开展主题培训班、研讨会等活动，推动政、企、学、研间资源互补，为行业的转型创新发展和核心人才培养提供支持，从宏行业政策分析、科技创新、战略升级、精益运营等多维度打造契合企业需求的学习方案，帮助企业在新一轮的科技革命中抢占先机。",
    img: [
      new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
      new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
    ],
    buttonName: "查看详情",
    buttonUrl: "",
  },
  cooperationList: {
    url: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
    icon1: new URL(`/@/assets/special/tel.png`, import.meta.url).href,
    icon2: new URL(`/@/assets/special/address.png`, import.meta.url).href,
    title: "更多合作欢迎垂询",
    subTitle: "For more cooperation,<br/>please feel free to inquire",
    tel: "0591-83050001",
    address: "福建省福州市闽侯县 永丰社区杜坞村43号大数据科技园A1座8层",
  },
});
const {
  classId,
  title,
  bannerList,
  titleContext,
  caseList,
  selectedCase,
  adList,
  prodMenuList,
  prodList,
  activList,
  cooperationList,
} = toRefs(state);
const coverImg = ref("");
const dataTabList = ref([]);
const home_4_msg = ref({});
const sideMsg = ref([]);
const home_4_list = ref([]);
const tableData = ref([]);
const scrollTopNum = ref(0);
const columns = ref([
  {
    title: "需求名称",
    key: "DEMANDNAME",
  },
  {
    title: "需求说明",
    key: "DESCRIBE",
  },
  {
    title: "需求单位",
    key: "DEMANDUNIT",
  },
  {
    title: "发布日期",
    key: "RELEASEDATE",
  },
  {
    title: "查看",
    key: "actions",
    render(row, index) {
      return h(
        NButton,
        {
          strong: true,
          tertiary: true,
          size: "small",
          style:
            "color:#1A399E;background-color: #FFF0;margin: auto;width:100%;",
          onClick: () => playVeiw(row),
        },
        { default: () => "查看" }
      );
    },
  },
]);
const imgIndex = ref(null);
const tableRef = ref(null);
const interval = ref(null);
const appList = ref([]);
const requireList = ref([]);
const showModal = ref(false);
const ModalData = ref(null);

onMounted(() => {
  getBannerList();
  getMenuProdList();
  getCaseList();
  getCustomTitle();
  getActive();
  getDataAdvantageList();
  getCooperationList();
  getActivList();

  getTableData();
  //getDataTab();
  nextTick(() => {
    clearInterval(interval.value);
    interval.value = setInterval(() => {
      autoScrollTable("up", 1); // 向下滚动1px
    }, 50);
  });
  setTimeout(() => {
    getApp(state.caseList[0].title);
  }, 500);
});
function getCustomTitle() {
  getModuleConfig({
    classId: [
      "yyxqlbmkbt",
      "yyyycjmkbt",
      "yytscpmkbt",
      "yytsalmkbt",
      "yyzqdtmkbt",
    ],
  }).then((res: any) => {
    if (res?.length > 0) {
      state.title = res;
      res.forEach((item) => {
        if (item.classId == 11223) {
          home_4_msg.value = item;
        }
      });
    }
  });
}
function getBannerList() {
  getZoneCarousel({ classId: classId.value.topBanner }).then((res: any) => {
    state.titleContext.title = res.title;
    state.titleContext.subTitle = res.subTitle;
    state.titleContext.context = res.detail;
    coverImg.value = res.cover;
    let tempArray = [...res.videoUrl, ...res.imgUrls];
    state.bannerList.list = tempArray.map((fileName: string) => {
      return {
        url: apiUrl.value + fileName,
        type: getFileType(fileName),
      };
    });

    if (res.videoUrl && res.videoUrl.length > 0) {
      state.bannerList.videoUrl = [];
      res.videoUrl.forEach((element: string) => {
        state.bannerList.videoUrl.push({
          url: apiUrl.value + element,
        });
      });
    }
    if (res.imgUrls && res.imgUrls.length > 0) {
      state.bannerList.img = [];
      res.imgUrls.forEach((element: string) => {
        state.bannerList.img.push({
          url: apiUrl.value + element,
        });
      });
    }
  });
}
function getCaseList() {
  getDataApplication({ classId: "yyyycj" }).then((res: any) => {
    if (res && res.length > 0) {
      state.caseList = res;
      state.caseList.forEach((element: any) => {
        element.image = apiUrl.value + element.image;
        element.logo = apiUrl.value + element.logo;
        element.active = false;
      });
      state.caseList[0].active = true;
      state.selectedCase = state.caseList[0];
    }
  });
}

function handleCase(data) {
  state.selectedCase = data;
  state.caseList.forEach((value) => {
    value.active = false;
  });
  state.caseList.find((obj) => obj.contentId === data.contentId).active = true;
  getApp(data.title);
}
function getDataAdvantageList() {
  getDataApplication({ classId: "yytsal" }).then((res: any) => {
    if (res && res.length > 0) {
      state.adList = res.map((i) => {
        return {
          text: i.detail,
          icon: apiUrl.value + i.logo,
          url: apiUrl.value + i.image,
          active: false,
          title: i.title,
          subTitle: i.subTitle,
          id: i.contentId,
        };
      });
      state.adList[0].active = true;
    }
  });
}

function changeAdSwiper(num) {
  useSwiper.value.slideTo(num);
}
function getMenuProdList() {
  getBrandType({ type: "zqcpfl", classId: classId.value.menuProdList }).then(
    (res: any) => {
      if (res && res.length > 0) {
        state.prodMenuList = res.map((i) => {
          return {
            name: i.dicWord,
            id: i.val,
          };
        });
        state.prodMenuList.unshift({
          name: "全部",
          id: "",
        });
        nextTick(() => tabsInstRef.value?.syncBarPosition());
        state.prodList.type = state.prodMenuList[0].id;
        getProdList(state.prodList.type);
      } else {
        state.prodList.type = "";
        getProdList(state.prodList.type);
      }
    }
  );
}
function getProdList(data, scroll = false) {
  getProductTypeList({
    classId: classId.value.prodList,
    type: data,
    pageNum: state.prodList.pageNum,
    pageSize: state.prodList.pageSize,
  }).then((res) => {
    state.prodList.totalPage = res[1].pageCount;
    state.prodList.prod = res[0].map((i) => {
      return {
        ...i,
        imageUrl: apiUrl.value + i.imageUrl,
        icon: new URL(`/@/assets/special/eye.png`, import.meta.url).href,
      };
    });
    if (scroll) {
      const targetElement = document.getElementById("prod-title-title");
      // targetElement.scrollIntoView({ behavior: "smooth" });
      targetElement.scrollIntoView();
    }
  });
}
function changeProd(value) {
  state.prodList.pageNum = 1;
  state.prodList.type = value;
  getProdList(state.prodList.type);
}
function changeProdPage(page) {
  console.log("changeProdPage", page, state.prodList);
  getProdList(state.prodList.type, true);
}
function getActivList() {
  getModuleConfig({ classId: classId.value.activList }).then((res: any) => {
    try {
      var data = res[0];
      if (data) {
        state.activList.context = data.detail;
        state.activList.title = data.title;
        state.activList.img = [apiUrl.value + data.image];
        state.activList.subTitle = data.subTitle;
        state.activList.buttonName = data.buttonName;
        state.activList.buttonUrl = data.buttonUrl;
      }
    } catch (error) {}
  });
}
function getCooperationList() {
  cooperationConsultation({ classId: 11219 }).then((res: any) => {
    state.cooperationList.address = res.ADDRESS;
    state.cooperationList.tel = res.PHONE;
    state.cooperationList.subTitle = res.SUBTITLE;
    state.cooperationList.title = res.TITLE;
    state.cooperationList.url = apiUrl.value + res.image;
  });
}
function goPage(path: string, outer: boolean = false) {
  if (outer && path) {
    window.open(unescapeHTML(path), "_blank");
    return;
  }
  router.push(unescapeHTML(path));
}
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
function getDataTab() {
  let params = {
    classId: 11221,
  };
  getAdvantage(params).then((res) => {
    dataTabList.value = res;
  });
  let query = {
    classId: 11226,
  };
  getAdvantage(query).then((res) => {
    requireList.value = res;
  });
}
function getActive() {
  let params = {
    classId: "yyzqdt",
  };
  getActivities(params).then((res) => {
    res.forEach((e) => {
      if (e.isRec == "1") {
        sideMsg.value = e;
      } else {
        home_4_list.value.push(e);
      }
    });
  });
}
function changImage(item, index) {
  console.log("here");
  imgIndex.value = index;
  goPage(`/product?id=${item.contentId}`);
}
function getTableData() {
  let params = {
    classId: "yyxqlb",
  };
  demandList(params).then((res) => {
    console.log(res);
    tableData.value = res.map((i) => {
      i.RELEASEDATE = dayjs(i.RELEASEDATE).format("YYYY-MM-DD");
      i.ENDDATE = dayjs(i.ENDDATE).format("YYYY-MM-DD");
      return i;
    });
  });
}
function autoScrollTable(direction, distance) {
  let maxHeight = 0;
  if (tableRef.value) {
    maxHeight = parseInt(tableRef.value.maxHeight.split("rem")[0]);
  }
  let scrollHeight = scrollTopNum.value - 320;
  if (maxHeight > scrollHeight) {
    scrollTopNum.value += distance;
  } else {
    scrollTopNum.value = 0;
  }
  if (tableRef.value) {
    tableRef.value.scrollTo({ top: scrollTopNum.value });
  }
}
function getApp(data) {
  let params = {
    appName: data,
    classId: 11214,
  };
  getProductListByAppName(params).then((res) => {
    console.log(res);
    appList.value = res;
  });
}
function goNews(e) {
  router.push({
    name: "newsDetail",
    query: { contentId: e.contentId, flag: true },
  });
}
function playVeiw(row) {
  ModalData.value = row;
  showModal.value = true;
}
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  ::-webkit-scrollbar {
    display: none;
  }
  #home {
    // min-width: 1024px;
    .home-wrapper {
      position: relative;
      /* padding-top: calc(99 / 1080 * 100vh); */
      padding: 60rem 0 40rem;
      width: 100%;
      // min-width: 1600px;
      /* height: 100vh; */
      overflow: hidden;
    }
    .home-wrapper-free {
      position: relative;
      padding-top: calc(99 / 1080 * 100vh);
      width: 100%;
      // min-width: 1600px;
      background: rgba(249, 250, 251, 1);
      overflow: hidden;
    }
    .bg-gray {
      background-color: rgba(249, 250, 251, 1);
    }
    .carousel-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: All 0.3s ease-in-out;
    }
    .carousel-img:hover {
      transform: scale(1.04);
    }
    .custom-dots {
      position: absolute;
      display: flex;
      flex-wrap: nowrap;
      position: absolute;
      bottom: 44rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .custom-dots li {
      display: inline-block;
      margin: 0 4rem;
      width: 101rem;
      height: 6rem;
      opacity: 0.5;
      background: rgba(229, 229, 229, 1);
      transition: width 0.3s, background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
    }
    .custom-dots li.is-active {
      opacity: 1;
      background: rgba(255, 255, 255, 1);
    }
    .glass {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      background: linear-gradient(
        to right,
        rgba(13, 46, 153, 0.8) 0%,
        rgba(70, 128, 255, 0) 100%
      );
    }
    .banner-content {
      position: absolute;
      top: calc(308 / 1080 * 100vh);
      left: 160rem;
      font-size: 20rem;
      letter-spacing: 1rem;
      width: 75%;
      color: rgba(255, 255, 255, 1);
      & > .title {
        font-size: 50rem;
        font-weight: 700;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .subTitle {
        font-size: 36rem;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .context {
        margin-top: 26rem;
        line-height: 28rem;
        font-size: 18rem;
        font-weight: 300;
        color: #fff;
      }
    }
    @keyframes blink {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    .toDown {
      position: absolute;
      bottom: 7%;
      left: 50%;
      transform: translateX(50%);
      animation: blink 2s infinite;
    }
    .dataTab {
      display: flex;
      flex-direction: row;
      /* width: 1600rem;
      margin: 100rem auto; */
      background: rgba(255, 255, 255, 1);
      .dataItem {
        position: relative;
        display: flex;
        align-items: center;
        flex: 1;
        margin: 0 28rem;
        .itemImg {
          width: 150rem;
          height: 150rem;
          overflow: hidden;
          border-radius: 50%;
          box-shadow: 0px 7px 12px rgba(14, 31, 53, 0.08);
          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
        .itemText {
          margin-left: 40rem;
          .itemTitle {
            font-size: 30rem;
            line-height: 1;
            color: #383838;
            padding-bottom: 40rem;
            transition: all 0.2s linear;
          }
          .itemSubtitle {
            font-size: 70rem;
            font-weight: bold;
            line-height: 1;
            color: #383838;
            transition: all 0.2s linear;
            animation: countup linear forwards;
            :deep(.countup-wrap) {
              span {
                font-weight: bold !important;
              }
            }
          }
        }

        /* &::after {
        position: absolute;
        display: block;
        content: "";
        top: 50%;
        right: -2.85rem;
        width: 1px;
        height: 80%;
        transform: translateY(-50%);
        background-color: #e7e7e7;
      } */
      }
    }
    .tablePart {
      margin: 60rem auto 40rem;
      width: calc(100% - 320rem);
      :deep(.n-data-table-th) {
        background: #2342a2;
        color: #ffffff;
        font-size: 24rem;
        font-weight: 500;
        padding: 28rem;
        text-align: center;
      }
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .rightBody {
        .seamless {
          width: 100%;
          height: 575rem;
          overflow: hidden;
        }
      }
    }
    .case-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      height: calc(100% - calc(99 / 1080 * 100vh));
      .dotMatrix:nth-of-type(1) {
        position: absolute;
        top: 48rem;
        right: 0;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      .dotMatrix:nth-of-type(2) {
        position: absolute;
        bottom: -20rem;
        left: -72rem;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        /* line-height: 96rem; */
      }
      & > .subTitle {
        margin-bottom: 26rem;
        /* height: 47rem; */
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .main {
        display: flex;
        justify-content: space-between;
        position: relative;
        width: 100%;
        height: calc(100vh - 169rem);
        .side-menu {
          position: relative;
          z-index: 10;
          margin-right: 30rem;
          // width: 419px;
          // width: calc(419 / 1920 * 100%);
          width: 419rem;
          max-height: 100%;
          overflow-y: auto;
          .side-menu-item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100rem;
            border: 1rem solid rgba(240, 240, 240, 1);
            font-size: 24rem;
            font-weight: 500;
            color: rgba(128, 128, 128, 1);
            cursor: pointer;
            img {
              margin: 0 27rem 0 39rem;
              width: 45rem;
              height: 41rem;
            }
            span {
              width: calc(100% - 111rem);
            }
          }
          .side-menu-item.active {
            color: rgba(255, 255, 255, 1);
            background: rgba(13, 46, 153, 1);
          }
        }
        .board {
          position: relative;
          width: 1151rem;
          height: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          .imgBox {
            width: 100%;
            height: 30%;
            display: flex;
            position: relative;
            // justify-content: space-between;
            .swiperRef {
              height: 100%;
              width: 100%;
            }
            img {
              width: 100%;
              height: 100%;
              transition: none;
            }
            img:hover {
              transform: none;
            }
            .activeImg {
              border: 2px solid #005ced;
            }
            // :deep(.swiper) {
            //   height: 100%;
            //   margin-left: auto;
            //   margin-right: auto;
            //   position: relative;
            //   overflow: auto;
            //   list-style: none;
            //   padding: 0;
            //   z-index: 1;
            //   display: block;
            // }
            :deep(.swiper-button-prev1) {
              width: 30rem;
              height: 34rem;
              border-radius: 34rem;
              left: 0;
              position: absolute;
              z-index: 10;
              bottom: 50rem;
              top: auto;
              transform: rotate(180deg);
              // background: rgba(237, 237, 237, 0.6);
            }
            :deep(.swiper-button-next1) {
              width: 30rem;
              height: 34rem;
              border-radius: 34rem;
              right: 0;
              bottom: 50rem;
              left: auto;
              top: auto;
              z-index: 10;
              position: absolute;
              transform: rotate(180deg);
              // background: #f6f6f6;
            }
            .swiper-button-prev:after,
            .swiper-button-next:after {
              font-family: "";
            }
            .swiper-button-next:after {
              content: "";
            }
            .swiper-button-prev:after {
              content: "";
            }
            .imgItem {
              width: 33%;
              height: 100%;
              display: flex;
              position: relative;
              margin-top: 5rem;
              .img-glass {
                position: absolute;
                top: 0;
                left: 30rem;
                width: 82%;
                height: 100%;
                pointer-events: none;
                overflow: hidden;
                .img-glass-content {
                  box-sizing: border-box;
                  display: flex;
                  width: 100%;
                  flex-direction: column;
                  position: absolute;
                  left: 0;
                  top: 50%;
                  transform: translateY(-50%);
                  font-size: 20rem;
                  letter-spacing: 1rem;
                  color: rgba(255, 255, 255, 1);
                  & > .title {
                    flex-shrink: 0;
                    font-size: 20rem;
                    font-weight: 700;
                    letter-spacing: 0rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                  }
                  .sub-context {
                    flex: 1;
                    .sub-context-context {
                      margin-top: 19rem;
                      width: 100%;
                      height: 80rem;
                      font-size: 16rem;
                      font-weight: 400;
                      letter-spacing: 0.2rem;
                      line-height: 26rem;
                      color: #ffffff;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-line-clamp: 3;
                      -webkit-box-orient: vertical;
                    }
                    .prod-tag {
                      display: flex;
                      justify-content: space-around;
                      margin: 50rem 0 10rem;
                      width: 100%;
                      height: 100%;
                      .prod-tag-item {
                        width: 105rem;
                        height: 35rem;
                        line-height: 45rem;
                        font-size: 16rem;
                        font-weight: 400;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 60rem;
                        background: rgba(255, 255, 255, 0.4);
                        border: 1rem solid rgba(235, 235, 235, 1);
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      }
                    }
                  }
                }
              }
            }
          }
          img {
            width: 100%;
            height: 100%;
            transition: All 0.3s ease-in-out;
          }
          img:hover {
            transform: scale(1.08);
          }
          .case-glass {
            position: absolute;
            top: 0;
            left: 0;
            width: 66%;
            height: 70%;
            pointer-events: none;
            overflow: hidden;
            /* background: linear-gradient(
                90deg,
                rgba(242, 76, 39, 1) 0.93%,
                rgba(242, 76, 39, 0) 100%
              ); */
            .case-glass-content {
              box-sizing: border-box;
              display: flex;
              flex-direction: column;
              // justify-content: space-between;
              position: absolute;
              left: 82rem;
              // margin-top: calc(86 / 1080 * 100vh);
              // padding: calc(200 / 1080 * 100vh) 0 calc(60 / 1080 * 100vh);
              padding: calc(60 / 1080 * 100vh) 0 calc(60 / 1080 * 100vh);
              width: 79%;
              height: 100%;
              font-size: 20rem;
              letter-spacing: 1rem;
              color: rgba(255, 255, 255, 1);
              & > .title {
                /* height: 127rem; */
                flex-shrink: 0;
                font-size: 48rem;
                font-weight: 700;
                letter-spacing: 0rem;
                line-height: 63rem;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
              .sub-context {
                flex: 1;
                // margin-top: calc(120 / 1080 * 100vh);
                .sub-context-title {
                  font-size: 48rem;
                  font-weight: 700;
                  letter-spacing: 0rem;
                  line-height: 63.65rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                }
                .sub-context-middle {
                  margin-top: 13rem;
                  width: 100%;
                  height: 23rem;
                  font-size: 18rem;
                  font-weight: 500;
                  letter-spacing: 1rem;
                  line-height: 23.87rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                .sub-context-context {
                  margin-top: 32rem;
                  /* font-size: 18rem; */
                  /* font-weight: 400; */
                  /* letter-spacing: 0rem; */
                  /* line-height: 24rem; */
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 9;
                  -webkit-box-orient: vertical;
                  overflow-wrap: break-word;
                  hyphens: auto;
                  text-align: justify;
                }
              }
            }
          }
        }
      }
    }
    .home_box_4 {
      /* height: 116vh; */
      height: 100%;
      background-color: #ffffff;
      position: relative;
      display: flex;
      flex-direction: column;
      .home_box_header {
        display: flex;
        flex-direction: column;
        position: relative;
        /* margin: calc(57 / 1080 * 100%) auto 0; */
        margin: 60rem auto 0;
        width: 1600rem;
        .home_title {
          line-height: 96rem;
          color: rgba(56, 56, 56, 1);
          font-size: 50rem !important;
          font-weight: 900;
          letter-spacing: 0rem;
          margin-bottom: 4rem;
        }
        .home_title_1 {
          font-size: 32rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 47.74rem;
          color: rgba(128, 128, 128, 1);
        }
        .home_detail {
          font-size: 24rem;
          font-weight: 300;
          letter-spacing: 1rem;
          /* line-height: 24rem; */
          color: rgba(128, 128, 128, 1);
          width: 1133rem;
          margin-top: 22rem;
          overflow: hidden;
          text-overflow: ellipsis;
          text-wrap: wrap;
        }
        .home_more {
          position: absolute;
          right: 0;
          top: 0;
          /* width: 130rem; */
          height: 28rem;
          font-size: 16rem;
          font-weight: 500;
          line-height: 28rem;
          color: rgba(40, 41, 56, 1);
        }
      }
      .home_box_4_content {
        top: 0;
        left: 0;
        height: 65%;
        width: 1600rem;
        margin: 48rem auto 40rem;
        overflow: hidden;
        display: flex;
        .home_box_4_content_left {
          height: 100%;
          min-width: 60%;
          margin-right: 48rem;
          display: flex;
          flex-direction: column;
          .left_img {
            width: 1138rem;
            height: 613rem;
            border-radius: 20rem 0rem 20rem 0rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left_title {
            font-size: 24rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            margin-top: 28rem;
            margin-bottom: 8rem;
          }
          .left_text {
            width: 1138rem;
            font-size: 18rem;
            font-weight: 400;
            line-height: 24rem;
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
        .home_box_4_content_left:hover {
          cursor: pointer;
          .left_text {
            color: #005ced;
          }
        }
        .home_box_4_content_right {
          display: flex;
          flex-direction: column;
          .rightItem {
            display: flex;
            flex-direction: column;
            .right_img {
              width: 413rem;
              height: 222rem;
              margin-bottom: rem;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .right_title {
              font-size: 24rem;
              font-weight: 700;
              line-height: 32rem;
              color: rgba(56, 56, 56, 1);
              width: 402rem;
              height: 32rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin: 28rem 0 8rem;
            }
            .right_text {
              font-size: 18rem;
              font-weight: 400;
              line-height: 24rem;
              color: rgba(91, 91, 91, 1);
              width: 402rem;
              overflow: hidden;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:first-child {
              margin-bottom: 54rem;
            }
          }
          .rightItem:hover {
            cursor: pointer;
            .right_text {
              color: #005ced;
            }
          }
        }
      }
    }
    .advantage-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      // width: 84%;
      // min-width: 1600px;
      height: calc(100% - calc(89 / 1080 * 100vh));
      .dotMatrix {
        position: absolute;
        top: 48rem;
        right: 0;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        /* line-height: 96rem; */
      }
      & > .subTitle {
        margin-bottom: 26rem;
        /* height: 47rem; */
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .main {
        display: flex;
        justify-content: space-between;
        position: relative;
        width: calc(100% + 5rem);
        height: 622rem;
        // height: 686rem;
        .advantage-swiper {
          width: calc(100% - 240rem);
          // height: 686px;
          height: 100%;
          .mySwiper {
            width: 100%;
            height: 100%;
          }
          .ad-swiper-img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 60rem 0rem 60rem 0rem;
            transition: All 0.3s ease-in-out;
          }

          .ad-glass {
            position: absolute;
            top: 0;
            left: 0;
            padding: calc(108 / 1080 * 100vh) 96rem 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            border-radius: 60rem 0rem 60rem 0rem;
            background: linear-gradient(
              90deg,
              rgba(13, 46, 153, 1) 0%,
              rgba(13, 46, 153, 0) 100%
            );
            .ad-glass-icon {
              width: 128.57rem;
              height: 128.57rem;
              border-radius: 50%;
            }
            .title {
              margin-top: calc(45 / 1080 * 100vh);
              font-size: 72rem;
              font-weight: 500;
              letter-spacing: 1rem;
              line-height: 95.47rem;
              color: rgba(255, 255, 255, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-title {
              margin-top: calc(45 / 1080 * 100vh);
              font-size: 48rem;
              font-weight: 700;
              letter-spacing: 0rem;
              line-height: 63.65rem;
              color: rgba(255, 255, 255, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-context {
              margin-top: calc(19 / 1080 * 100vh);
              width: 90%;
              font-size: 20rem;
              font-weight: 400;
              letter-spacing: 1rem;
              line-height: 28rem;
              color: rgba(255, 255, 255, 1);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 4;
              -webkit-box-orient: vertical;
            }
          }
        }

        .advantage-select {
          width: 183rem;
          max-height: 100%;
          overflow-y: auto;
          .advantage-select-item {
            position: relative;
            margin: 0rem auto 0;
            width: 100%;
            height: 135rem;
            & > img {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto auto;
              width: 164rem;
              height: 112rem;
            }
            .ad-select-glass {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto auto;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 164rem;
              height: 112rem;
              pointer-events: none;
              background: rgba(13, 46, 153, 0.6);
              .ad-select-icon {
                margin: calc(20 / 1080 * 100vh) auto 0;
                width: 100%;
                height: 51rem;
                text-align: center;
                & > img {
                  width: 51rem;
                  height: 51rem;
                }
              }
              .ad-select-text {
                margin-top: calc(4 / 1080 * 100vh);
                width: 100%;
                font-size: 14rem;
                font-weight: 500;
                letter-spacing: 0rem;
                line-height: 23.87rem;
                color: rgba(255, 255, 255, 1);
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
              }
            }
          }
          .advantage-select-item.active {
            border-radius: 6rem;
            border: 4rem solid rgba(204, 207, 219, 1);
          }
        }
      }
    }
    .prod-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      & > .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 50rem;
        & > .prod-title-context {
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          /* line-height: 96rem; */
        }
        :deep(.n-tabs-nav-scroll-content) {
          border: 0;
        }
        :deep(.n-tabs-tab) {
          font-size: 22rem;
          font-weight: 400;
          letter-spacing: 0rem;
          line-height: 29.17rem;
          color: rgba(115, 115, 115, 1);
        }
        :deep(.n-tabs-tab.n-tabs-tab--active) {
          color: rgba(13, 46, 153, 1) !important;
        }
        :deep(.n-tabs-bar) {
          height: 4rem;
          border-radius: 3rem;
          --n-bar-color: rgba(13, 46, 153, 1);
        }
      }
      & > .subTitle {
        margin-bottom: 26rem;
        /* height: 47rem; */
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }

      .prod-main {
        padding-bottom: 32rem;
        width: 100%;
        // min-height: 1000px;

        .empty {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 400rem;
        }
        .prod-item-box {
          display: grid;
          grid-template-columns: repeat(2, minmax(0rem, 1fr));
          gap: 20rem;
          justify-content: center;
          width: 100%;
          height: 100%;
          .prod-item:hover {
            transform: translate(0, -10rem);
            box-shadow: 0 0.25rem 1.25rem #d4d4d4;
            .prod-show {
              display: flex;
            }
          }
          .prod-item {
            display: flex;
            justify-content: space-between;
            height: 344rem;
            background: rgba(255, 255, 255, 1);
            cursor: pointer;
            transition: All 0.3s ease-in-out;
            position: relative;
            .prod-info {
              padding: 52rem 0 0 53rem;
              width: calc(347 / 790 * 100%);
              height: 100%;
              overflow: hidden;
              .title {
                width: 100%;
                font-size: 36rem;
                font-weight: 700;
                letter-spacing: 0.2rem;
                line-height: 40rem;
                color: rgba(37, 43, 66, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                :deep(span) {
                  font-weight: 700 !important;
                }
              }
              .context {
                margin-top: 19rem;
                width: 100%;
                height: 80rem;
                font-size: 18rem;
                font-weight: 400;
                letter-spacing: 0.2rem;
                line-height: 26rem;
                color: rgb(255, 255, 255);
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
              }
              .prod-tag {
                display: flex;
                justify-content: space-between;
                margin-top: 20rem;
                width: 100%;
                height: 45rem;
                .prod-tag-item {
                  width: 140rem;
                  height: 45rem;
                  line-height: 45rem;
                  font-size: 16rem;
                  font-weight: 400;
                  text-align: center;
                  border-radius: 60rem;
                  background: rgba(245, 245, 245, 1);
                  border: 1rem solid rgba(235, 235, 235, 1);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
              .prod-view {
                display: flex;
                align-items: center;
                margin-top: 40rem;
                width: 100%;
                .prod-view-eye {
                  margin-right: 17rem;
                  width: 32.02rem;
                  height: 28.08rem;
                }
                .prod-view-text {
                  font-size: 14rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
            .prod-img {
              position: relative;
              width: 337rem;
              height: 100%;
              .prod-arrow-box {
                box-sizing: border-box;
                position: absolute;
                bottom: 24rem;
                // padding: 0 24;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 115.8rem;
                height: 46.13rem;
                opacity: 1;
                border-radius: 60rem;
                background: rgba(13, 46, 153, 1);
                transform: translateX(-50%);
                text-align: center;
                img {
                  width: 67.07rem;
                  height: 8.02rem;
                  object-fit: cover;
                }
              }
              & > img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            .prod-show {
              position: absolute;
              width: 100%;
              height: 100%;
              background-color: #fff;
              top: 0px;
              left: 0px;
              display: none;
              background-image: url("/src/assets/backgroundImg.png");
              background-size: 100% 100%;
              background-repeat: no-repeat;

              justify-content: center;
              flex-direction: column;
              padding: 20px;
              .title {
                width: 100%;
                font-size: 36rem;
                font-weight: 700;
                letter-spacing: 0.2rem;
                line-height: 40rem;
                color: rgb(255, 255, 255);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                :deep(span) {
                  font-weight: 700 !important;
                }
              }
              .context {
                margin-top: 19rem;
                width: 100%;
                height: 80rem;
                font-size: 18rem;
                font-weight: 400;
                letter-spacing: 0.2rem;
                line-height: 26rem;
                color: rgb(255, 255, 255);
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
      .prod-pag {
        display: flex;
        justify-content: center;
        margin-top: 36px;
        padding-bottom: 32px;
        text-align: center;
        :deep(.n-pagination-item) {
          color: rgba(128, 128, 128, 1);
          --n-item-border-active: 1px solid rgba(128, 128, 128, 1);
        }
      }
    }
    .activity-left-box {
      position: relative;
      padding-left: calc(151.5 / 1920 * 100%);
      width: 50%;
      // width: 84%;
      // min-width: 1600px;
      height: calc(100% - calc(153 / 1080 * 100vh));
      .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 104.26rem;
        color: rgba(56, 56, 56, 1);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .left-content {
        margin-top: 40rem;
        display: grid;
        grid-template-columns: repeat(2, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        width: 90%;
        height: 76%;
        .left-card {
          width: 343rem;
          height: 250rem;
          background: linear-gradient(180deg, #f0f4ff 0%, #ffffff 100%);
          box-shadow: 0px 0px 22rem 0px rgba(162, 162, 162, 0.25);
          border-radius: 10rem;
          .left-icon {
            margin: 40rem auto 30rem;
            width: 110rem;
            height: 93rem;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left-text {
            font-weight: 400;
            font-size: 24rem;
            color: #000000;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
    .activity-box {
      position: relative;
      // margin: calc(153 / 1080 * 100vh) auto 0;
      display: flex;
      flex-direction: column;
      width: 50%;
      height: 100%;
      // width: 84%;
      // min-width: 1600px;
      // height: calc(100% - calc(153 / 1080 * 100vh));
      .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 104.26rem;
        color: rgba(56, 56, 56, 1);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      // & > img:nth-of-type(1) {
      //   position: absolute;
      //   left: -200rem;
      //   width: 750rem;
      //   height: 581rem;
      //   border-radius: 20rem;
      //   transform: rotate(10.54deg);
      //   z-index: 10;
      // }
      & > img:nth-of-type(1) {
        // position: absolute;
        // right: -230px;
        // width: 750px;
        // height: 580px;
        //right: -200rem;
        // width: 750rem;
        // height: 581rem;
        // width: 500rem;
        width: 90%;
        height: 387rem;
        border-radius: 20rem;
        margin-top: 20rem;
        // transform: rotate(10.54deg);
        z-index: 10;
      }
      .act-contain {
        // position: absolute;
        // top: 50%;
        // left: 0;
        z-index: 20;
        // transform: translate(0, -50%);
        // width: 605px;
        // height: 600px;
        // width: calc(605 / 1920 * 100vw);
        width: 90%;
        // height: calc(600 / 1080 * 100vh);
        // background-color: rgba(255, 255, 255, 0.3);
        .act-title {
          width: 100%;
          text-align: center;
          font-size: 72rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .act-sub-title {
          width: 100%;
          font-size: 20rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 26.52rem;
          color: rgba(128, 128, 128, 1);
          // text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .act-context {
          margin-top: 40rem;
          width: 100%;
          font-size: 18rem;
          font-weight: 400;
          letter-spacing: 0rem;
          line-height: 30rem;
          color: rgba(85, 85, 85, 0.8);
          text-align: start;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 11;
          -webkit-box-orient: vertical;
        }
        .act-button {
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          // margin: 117px auto 0;
          width: 262rem;
          height: calc(66 / 1080 * 100vh);
          border-radius: 100rem;
          background: rgba(13, 46, 153, 1);
          font-size: 24rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: calc(66 / 1080 * 100vh);
          color: rgba(255, 255, 255, 1);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
    }
    .advertisement {
      position: relative;
      width: 100%;
      height: 705rem;
      background-color: rgba(249, 250, 251, 1);
      overflow: hidden;
      // background-repeat: no-repeat;
      // background-size: cover;
      & > img {
        position: absolute;
        right: 0;
        top: 0;
        width: 968rem;
        height: 100%;
      }
      .advertisement-contain {
        position: relative;
        z-index: 10;
        margin: 0 0 0 259rem;
        padding-top: 129rem;
        width: 620rem;

        .title {
          width: 100%;
          font-size: 72rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .subTitle {
          margin-bottom: 18rem;
          width: 470rem;
          font-size: 36rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 47.74rem;
          color: rgba(128, 128, 128, 1);
        }
        .line {
          display: flex;
          align-items: center;
          margin-top: 26rem;
          width: 100%;
          .icon-box {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 25rem;
            width: 53rem;
            height: 53rem;
            background: rgba(232, 232, 232, 1);
            border-radius: 50%;
            img {
              width: 28rem;
              height: 28rem;
            }
          }
          .tel-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: rgba(128, 128, 128, 1);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .address-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: rgba(128, 128, 128, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
    .placeholder {
      width: 100%;
      height: 101rem;
    }
  }
  .modalBox {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: 30px;
    width: 60%;
    .title {
      border-left: 4px solid #2342a2;
      font-size: 20px;
      margin-bottom: 20px;
      padding-left: 20px;
    }
    .tableBox {
      text-align: center;
      line-height: 45px;
      display: table;
      .liBox {
        display: table-row;
      }
      .lableBox {
        background-color: #f8f8f8;
        width: 20%;
        border: 1px solid #efeff5;
        float: left;
        display: table-cell;
        vertical-align: middle;
        text-align: center;
      }
      .rowBox30 {
        padding-left: 10px;
        width: 30%;
        border: 1px solid #efeff5;
        background-color: #fff;
        float: left;
        text-align: left;
        display: table-cell;
      }
      .rowBox80 {
        display: table-cell;
        line-height: 16px;
        padding: 15px;
        width: 80%;
        border: 1px solid #efeff5;
        background-color: #fff;
        float: left;
        text-align: left;
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #home {
    .home-wrapper {
      position: relative;
      padding-top: 60rem;
      padding-bottom: 40rem;
      width: 100%;
      /* height: 100vh; */
      overflow: hidden;
    }
    .home-wrapper-free {
      position: relative;
      padding-top: 60rem;
      width: 100%;
      background: rgba(249, 250, 251, 1);
      overflow: hidden;
    }
    .bg-gray {
      background-color: rgba(249, 250, 251, 1);
      height: 100%;
    }
    .carousel-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: All 0.3s ease-in-out;
    }
    .carousel-img:hover {
      transform: scale(1.04);
    }
    .custom-dots {
      position: absolute;
      display: flex;
      flex-wrap: nowrap;
      position: absolute;
      bottom: 44rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .custom-dots li {
      display: inline-block;
      margin: 0 4rem;
      width: 101rem;
      height: 6rem;
      opacity: 0.5;
      background: rgba(229, 229, 229, 1);
      transition: width 0.3s, background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
    }
    .custom-dots li.is-active {
      opacity: 1;
      background: rgba(255, 255, 255, 1);
    }
    .glass {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      background: linear-gradient(
        to right,
        rgba(13, 46, 153, 0.8) 0%,
        rgba(70, 128, 255, 0) 100%
      );
    }
    .banner-content {
      font-size: 20rem;
      letter-spacing: 1rem;
      color: rgba(255, 255, 255, 1);
      width: 720rem;
      margin: 150rem auto 0;
      & > .title {
        font-size: 36rem;
        font-weight: 700;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .subTitle {
        font-size: 36rem;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .context {
        margin-top: 6rem;
        line-height: 28rem;
        font-size: 18rem;
        font-weight: 300;
        color: #fff;
        :deep(p) {
          font-size: 22rem !important;
          span {
            font-size: 22rem !important;
          }
        }
      }
    }
    @keyframes blink {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    .toDown {
      position: absolute;
      bottom: 7%;
      left: 50%;
      transform: translateX(-50%);
      animation: blink 2s infinite;
    }
    .case-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: 720rem;
      /* height: calc(100% - calc(99 / 1080 * 100vh)); */
      .dotMatrix:nth-of-type(1) {
        position: absolute;
        top: 48rem;
        right: 0;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      .dotMatrix:nth-of-type(2) {
        position: absolute;
        bottom: -20rem;
        left: -72rem;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      & > .title {
        font-size: 72rem;
        font-weight: 900;
        letter-spacing: 0rem;
        /* line-height: 96rem; */
      }
      & > .subTitle {
        margin-bottom: 26rem;
        /* height: 47rem; */
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .main {
        display: flex;
        flex-direction: column;
        position: relative;
        width: 100%;
        height: calc(100% - 169rem);
        .side-menu {
          position: relative;
          z-index: 10;
          margin-right: 30rem;
          width: 100%;
          max-height: 400rem;
          overflow-y: auto;
          .side-menu-item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100rem;
            border: 1rem solid rgba(240, 240, 240, 1);
            font-size: 24rem;
            font-weight: 500;
            color: rgba(128, 128, 128, 1);
            cursor: pointer;
            img {
              margin: 0 27rem 0 39rem;
              width: 45rem;
              height: 41rem;
            }
            span {
              width: calc(100% - 111rem);
            }
          }
          .side-menu-item.active {
            color: rgba(255, 255, 255, 1);
            background: rgba(13, 46, 153, 1);
          }
        }
        .board {
          position: relative;
          width: 100%;
          height: 100%;
          overflow: hidden;
          margin-top: 24rem;
          img {
            width: 100%;
            height: 100%;
            transition: All 0.3s ease-in-out;
          }
          img:hover {
            transform: scale(1.08);
          }
          .case-glass {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            /* background: linear-gradient(
                90deg,
                rgba(242, 76, 39, 1) 0.93%,
                rgba(242, 76, 39, 0) 100%
              ); */
            .case-glass-content {
              width: 90%;
              box-sizing: border-box;
              display: flex;
              flex-direction: column;
              // justify-content: space-between;
              /* position: absolute; */
              /* left: 82rem; */
              // margin-top: calc(86 / 1080 * 100vh);
              padding: calc(60 / 1080 * 100vh) 10rem calc(60 / 1080 * 100vh);
              height: 100%;
              font-size: 20rem;
              letter-spacing: 1rem;
              color: rgba(255, 255, 255, 1);
              & > .title {
                /* height: 127rem; */
                font-size: 48rem;
                font-weight: 700;
                letter-spacing: 0rem;
                line-height: 63rem;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
              .sub-context {
                flex: 1;
                // margin-top: calc(120 / 1080 * 100vh);
                .sub-context-title {
                  font-size: 48rem;
                  font-weight: 700;
                  letter-spacing: 0rem;
                  line-height: 63.65rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                }
                .sub-context-middle {
                  margin-top: 13rem;
                  width: 100%;
                  height: 23rem;
                  font-size: 18rem;
                  font-weight: 500;
                  letter-spacing: 1rem;
                  line-height: 23.87rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                .sub-context-context {
                  margin-top: 18rem;
                  font-size: 18rem;
                  font-weight: 400;
                  letter-spacing: 0rem;
                  line-height: 24rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 9;
                  -webkit-box-orient: vertical;
                  :deep(p) {
                    font-size: 18rem !important;
                    span {
                      font-size: 18rem !important;
                      /* line-height: 1 !important; */
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .home_box_4 {
      /* height: 116vh; */
      height: 100%;
      background-color: #ffffff;
      position: relative;
      display: flex;
      flex-direction: column;
      .home_box_header {
        display: flex;
        flex-direction: column;
        position: relative;
        /* margin: calc(57 / 1080 * 100%) auto 0; */
        margin: 60rem auto 0;
        width: 720rem;
        .home_title {
          line-height: 96rem;
          color: rgba(56, 56, 56, 1);
          font-size: 50rem !important;
          font-weight: 900;
          letter-spacing: 0rem;
          margin-bottom: 4rem;
        }
        .home_title_1 {
          font-size: 32rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 47.74rem;
          color: rgba(128, 128, 128, 1);
        }
        .home_detail {
          font-size: 24rem;
          font-weight: 300;
          letter-spacing: 1rem;
          /* line-height: 24rem; */
          color: rgba(128, 128, 128, 1);
          /* width: 1133rem; */
          margin-top: 22rem;
          overflow: hidden;
          text-overflow: ellipsis;
          text-wrap: wrap;
        }
        .home_more {
          position: absolute;
          right: 0;
          top: 0;
          /* width: 130rem; */
          height: 28rem;
          font-size: 16rem;
          font-weight: 500;
          line-height: 28rem;
          color: rgba(40, 41, 56, 1);
        }
      }
      .home_box_4_content {
        height: 65%;
        width: 720rem;
        margin: 48rem auto 40rem;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .home_box_4_content_left {
          height: 100%;
          width: 100%;
          margin-right: 48rem;
          display: flex;
          flex-direction: column;
          .left_img {
            width: 100%;
            height: 307rem;
            border-radius: 20rem 0rem 20rem 0rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left_title {
            font-size: 28rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            margin-top: 28rem;
            margin-bottom: 8rem;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
          }
          .left_text {
            /* width: 1138rem; */
            font-size: 24rem;
            font-weight: 400;
            /* line-height: 24rem; */
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
        .home_box_4_content_left:hover {
          cursor: pointer;
          .left_text {
            color: #005ced;
          }
        }
        .home_box_4_content_right {
          display: flex;
          flex-direction: column;
          .rightItem {
            display: flex;
            flex-direction: column;
            margin-top: 20rem;
            .right_img {
              width: 100%;
              height: 387rem;
              margin-bottom: 16rem;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .right_title {
              font-size: 28rem;
              font-weight: 700;
              line-height: 32rem;
              color: rgba(56, 56, 56, 1);
              /* width: 402rem; */
              height: 32rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 10rem;
            }
            .right_text {
              font-size: 24rem;
              font-weight: 400;
              color: rgba(91, 91, 91, 1);
              overflow: hidden;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:first-child {
              margin-bottom: 14rem;
            }
          }
          .rightItem:hover {
            cursor: pointer;
            .right_text {
              color: #005ced;
            }
          }
        }
      }
    }
    .advantage-box {
      position: relative;
      margin: 0 auto;
      width: 720rem;
      // width: 84%;
      // min-width: 1600px;
      .dotMatrix {
        position: absolute;
        top: 48rem;
        right: 0;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      & > .title {
        font-size: 72rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 96rem;
      }
      & > .subTitle {
        margin-bottom: 26rem;
        height: 47rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .main {
        display: flex;
        /* justify-content: space-between; */
        flex-direction: column;
        position: relative;
        width: 720rem;
        /* height: 686rem; */
        .advantage-swiper {
          width: 720rem;
          // height: 686px;
          height: 392rem;
          .mySwiper {
            width: 100%;
            height: 100%;
          }
          .ad-swiper-img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 60rem 0rem 60rem 0rem;
            transition: All 0.3s ease-in-out;
          }

          .ad-glass {
            position: absolute;
            top: 0;
            left: 0;
            padding: 68rem 60rem 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            border-radius: 60rem 0rem 60rem 0rem;
            /* background: linear-gradient(
                90deg,
                rgba(13, 46, 153, 1) 0%,
                rgba(13, 46, 153, 0) 100%
              ); */
            .ad-glass-icon {
              width: 128.57rem;
              height: 128.57rem;
              border-radius: 50%;
            }
            .title {
              /* margin-top: calc(45 / 1080 * 100vh); */
              font-size: 50rem;
              font-weight: 500;
              letter-spacing: 1rem;
              /* line-height: 95.47rem; */
              color: rgba(255, 255, 255, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-title {
              font-size: 48rem;
              font-weight: 700;
              letter-spacing: 0rem;
              line-height: 63.65rem;
              color: rgba(255, 255, 255, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-context {
              margin-top: calc(19 / 1080 * 100vh);
              width: 90%;
              font-size: 20rem;
              font-weight: 400;
              letter-spacing: 1rem;
              /* line-height: 28rem; */
              color: rgba(255, 255, 255, 1);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 4;
              -webkit-box-orient: vertical;
              :deep(p) {
                font-size: 20rem !important;
                span {
                  font-size: 20rem !important;
                }
              }
            }
          }
        }

        .advantage-select {
          width: 100%;
          max-height: 100%;
          overflow-y: auto;
          display: flex;
          justify-content: space-between;
          .advantage-select-item {
            position: relative;
            margin: 0rem auto 0;
            width: 100%;
            height: 135rem;
            & > img {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto auto;
              width: 164rem;
              height: 112rem;
            }
            .ad-select-glass {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto auto;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 164rem;
              height: 112rem;
              pointer-events: none;
              background: rgba(13, 46, 153, 0.6);
              .ad-select-icon {
                margin: calc(20 / 1080 * 100vh) auto 0;
                width: 100%;
                height: 51rem;
                text-align: center;
                & > img {
                  width: 51rem;
                  height: 51rem;
                }
              }
              .ad-select-text {
                margin-top: calc(4 / 1080 * 100vh);
                width: 100%;
                font-size: 18rem;
                font-weight: 500;
                letter-spacing: 0rem;
                line-height: 23.87rem;
                color: rgba(255, 255, 255, 1);
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
              }
            }
          }
          .advantage-select-item.active {
            border-radius: 6rem;
            border: 4rem solid rgba(204, 207, 219, 1);
          }
        }
      }
    }
    .prod-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: 720rem;
      & > .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        & > .prod-title-context {
          font-size: 72rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 96rem;
        }
        :deep(.n-tabs-nav-scroll-content) {
          border: 0;
        }
        :deep(.n-tabs-tab) {
          font-size: 22rem;
          font-weight: 400;
          letter-spacing: 0rem;
          line-height: 29.17rem;
          color: rgba(115, 115, 115, 1);
        }
        :deep(.n-tabs-tab.n-tabs-tab--active) {
          color: rgba(13, 46, 153, 1) !important;
        }
        :deep(.n-tabs-bar) {
          height: 4rem;
          border-radius: 3rem;
          --n-bar-color: rgba(13, 46, 153, 1);
        }
      }
      & > .subTitle {
        margin-bottom: 26rem;
        height: 47rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }

      .prod-main {
        padding-bottom: 32rem;
        width: 100%;
        // min-height: 1000px;

        .empty {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 400rem;
        }
        .prod-item-box {
          display: grid;
          grid-template-columns: repeat(1, minmax(0rem, 1fr));
          gap: 20rem;
          justify-content: center;
          width: 100%;
          height: 100%;
          .prod-item:hover {
            transform: translate(0, -10rem);
            box-shadow: 0 0.25rem 1.25rem #d4d4d4;
            .prod-show {
              display: flex;
            }
          }
          .prod-item {
            position: relative;
            display: flex;
            justify-content: space-between;
            height: 344rem;
            background: rgba(255, 255, 255, 1);
            cursor: pointer;
            transition: All 0.3s ease-in-out;
            .prod-info {
              padding: 52rem 0 0 53rem;
              width: calc(347 / 790 * 100%);
              height: 100%;
              overflow: hidden;
              .title {
                width: 100%;
                font-size: 36rem;
                font-weight: 700;
                letter-spacing: 0.2rem;
                line-height: 40rem;
                color: rgba(37, 43, 66, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .context {
                margin-top: 19rem;
                width: 100%;
                height: 80rem;
                font-size: 24rem;
                font-weight: 400;
                letter-spacing: 0.2rem;
                /* line-height: 20rem; */
                color: rgba(85, 85, 85, 1);
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
              .prod-tag {
                display: flex;
                justify-content: space-evenly;
                margin-top: 20rem;
                width: 100%;
                height: 45rem;
                .prod-tag-item {
                  width: 120rem;
                  height: 45rem;
                  line-height: 45rem;
                  font-size: 16rem;
                  font-weight: 400;
                  text-align: center;
                  border-radius: 60rem;
                  background: rgba(245, 245, 245, 1);
                  border: 1rem solid rgba(235, 235, 235, 1);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
              .prod-view {
                display: flex;
                align-items: center;
                margin-top: 40rem;
                width: 100%;
                .prod-view-eye {
                  margin-right: 17rem;
                  width: 32.02rem;
                  height: 28.08rem;
                }
                .prod-view-text {
                  font-size: 14rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
            .prod-img {
              position: relative;
              width: 337rem;
              height: 100%;
              .prod-arrow-box {
                box-sizing: border-box;
                position: absolute;
                bottom: 24rem;
                // padding: 0 24;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 115.8rem;
                height: 46.13rem;
                opacity: 1;
                border-radius: 60rem;
                background: rgba(13, 46, 153, 1);
                transform: translateX(-50%);
                text-align: center;
                img {
                  width: 67.07rem;
                  height: 8.02rem;
                  object-fit: cover;
                }
              }
              & > img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            .prod-show {
              position: absolute;
              width: 100%;
              height: 100%;
              background-color: #fff;
              top: 0px;
              left: 0px;
              display: none;
              background-image: url("/src/assets/backgroundImg.png");
              background-size: 100% 100%;
              background-repeat: no-repeat;
              justify-content: center;
              flex-direction: column;
              padding: 10px;
              .title {
                width: 100%;
                font-size: 36rem;
                font-weight: 700;
                letter-spacing: 0.2rem;
                line-height: 40rem;
                color: rgba(37, 43, 66, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .context {
                margin-top: 19rem;
                width: 100%;
                height: 80rem;
                font-size: 24rem;
                font-weight: 400;
                letter-spacing: 0.2rem;
                /* line-height: 20rem; */
                color: rgba(85, 85, 85, 1);
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
      .prod-pag {
        display: flex;
        justify-content: center;
        margin-top: 36rem;
        padding-bottom: 40rem;
        text-align: center;
        :deep(.n-pagination-item) {
          color: rgba(128, 128, 128, 1);
          --n-item-border-active: 1px solid rgba(128, 128, 128, 1);
        }
      }
    }
    .activity-box {
      position: relative;
      /* margin: calc(153 / 1080 * 100vh) auto 0; */
      width: 100%;
      // width: 84%;
      // min-width: 1600px;
      height: 100%;
      & > img:nth-of-type(1) {
        position: absolute;
        left: 0;
        width: 100%;
        height: 100%;
      }
      .act-contain {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 20;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        .act-title {
          width: 100%;
          text-align: center;
          font-size: 36rem;
          font-weight: 900;
          letter-spacing: 0rem;
          /* line-height: 104.26rem; */
          color: rgba(255, 255, 255, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-top: 16rem;
        }
        .act-sub-title {
          width: 100%;
          font-size: 20rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 26.52rem;
          color: rgba(255, 255, 255, 1);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .act-context {
          margin: 40rem auto 0;
          width: 95%;
          font-size: 18rem;
          font-weight: 400;
          letter-spacing: 0rem;
          line-height: 30rem;
          color: rgba(255, 255, 255, 1);
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 11;
          -webkit-box-orient: vertical;
          :deep(p) {
            font-size: 22rem !important;
            span {
              font-size: 22rem !important;
            }
          }
        }
        .act-button {
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          // margin: 117px auto 0;
          width: 262rem;
          height: calc(66 / 1080 * 100vh);
          border-radius: 100rem;
          background: rgba(13, 46, 153, 1);
          font-size: 24rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: calc(66 / 1080 * 100vh);
          color: rgba(255, 255, 255, 1);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
    }
    .advertisement {
      position: relative;
      width: 100%;
      height: 547rem;
      background-color: rgba(249, 250, 251, 1);
      overflow: hidden;
      // background-repeat: no-repeat;
      // background-size: cover;
      & > img {
        position: absolute;
        right: 0;
        top: 0;
        width: 100%;
      }
      .advertisement-contain {
        position: relative;
        z-index: 10;
        margin: 0 auto;
        padding-top: 129rem;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        .title {
          margin: 0 auto;
          width: 720rem;
          font-size: 72rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: #ffffff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .subTitle {
          margin-bottom: 18rem;
          width: 470rem;
          font-size: 36rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 47.74rem;
          color: rgba(128, 128, 128, 1);
        }
        .line {
          display: flex;
          align-items: center;
          margin: 26rem auto;
          width: 720rem;
          .icon-box {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 25rem;
            width: 53rem;
            height: 53rem;
            background: #ffffff;
            border-radius: 50%;
            img {
              width: 28rem;
              height: 28rem;
            }
          }
          .tel-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .address-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: #ffffff;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
    .placeholder {
      width: 100%;
      height: 101rem;
    }
  }
  .modalBox {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    padding: 30px;
    width: 60%;
    .title {
      border-left: 4px solid #273eea;
      font-size: 20px;
      margin-bottom: 20px;
      padding-left: 20px;
    }
    .tableBox {
      text-align: center;
      line-height: 45px;
      display: table;
      .liBox {
        display: table-row;
      }
      .lableBox {
        background-color: #f8f8f8;
        width: 20%;
        border: 1px solid #efeff5;
        float: left;
        display: table-cell;
        vertical-align: middle;
        text-align: center;
      }
      .rowBox30 {
        padding-left: 10px;
        width: 80%;
        border: 1px solid #efeff5;
        background-color: #fff;
        float: left;
        text-align: left;
        display: table-cell;
      }
      .rowBox80 {
        display: table-cell;
        line-height: 16px;
        padding: 15px;
        width: 80%;
        border: 1px solid #efeff5;
        background-color: #fff;
        float: left;
        text-align: left;
      }
    }
  }
}
</style>
