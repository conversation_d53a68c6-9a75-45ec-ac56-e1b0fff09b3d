<template>
  <div id="news">
    <template v-if="!isMobile">
      <div class="news_box news_box_1">
        <div class="news_box_1_title">新闻资讯</div>
        <div class="news_box_1_text">
          在这里您可以阅读到各类相关信息，了解重要信息
        </div>
        <div class="news_box_1_search">
          <n-input
            size="large"
            v-model="search"
            round
            placeholder="请输入关键字搜索"
            @input="handleChange"
          >
            <template #suffix>
              <img
                src="../../assets/news/news_search.png"
                alt="搜索"
                style="margin-right: 38rem; cursor: pointer"
                @click="handleSearch"
              />
            </template>
          </n-input>
        </div>
      </div>
      <div class="news_box news_box_2">
        <div class="tabLan" v-if="tabList && tabList.length > 0 && !isMobile">
          <n-tabs
            :defaultValue="checkTab"
            size="large"
            justify-content="space-evenly"
            @update:value="changeValue"
          >
            <n-tab v-for="item in tabList" v-model="key" :name="item.VAL">{{
              item.DICWORD
            }}</n-tab>
          </n-tabs>
        </div>
        <div class="news_box_2_card" v-if="!flag && !isMobile">
          <div class="news_box_2_card_left" @click="goDetail(sideMsg)">
            <div style="position: relative; width: 954rem; height: 539rem">
              <div class="recommend" v-if="sideMsg.isRec === '1'">推荐</div>
              <img
                v-if="sideMsg.newsPicture"
                :src="apiUrl + sideMsg.newsPicture"
                alt=""
                style="position: absolute; top: 0; width: 100%; height: 100%"
              />
              <img
                v-else
                :src="newsImg"
                alt=""
                style="position: absolute; top: 0; width: 100%; height: 100%"
              />
            </div>
            <div class="footer">
              <div class="name">{{ sideMsg.newsName }}</div>
              <div class="arrow">
                <img src="../../assets/build/arrow.png" alt="" />
              </div>
            </div>
          </div>
          <vue3ScrollSeamless
            class="scroll-wrap border text-color news_box_2_card_right"
            :classOptions="classOptions"
            :dataList="newsList"
          >
            <div
              class="detail"
              v-for="item in newsList"
              @click="goDetail(item)"
            >
              <div class="dot">
                <img
                  v-if="item.newsPicture"
                  :src="apiUrl + item.newsPicture"
                  alt=""
                />
                <img v-else :src="newsImg" alt="" />
              </div>
              <div class="content">
                <div class="inTitle">{{ item.newsName }}</div>
                <div class="inText dynamicText">{{ item.detail }}</div>
                <div class="date">{{ item.addTime }}</div>
              </div>
            </div>
          </vue3ScrollSeamless>
        </div>

        <div class="mesExpress">
          <div
            class="mesCard dynamicUp"
            v-for="item in mesList"
            @click="goDetail(item)"
          >
            <div style="height: 214rem">
              <img
                v-if="item.newsPicture"
                :src="apiUrl + item.newsPicture"
                style="height: 214rem; width: 378rem"
              />
              <img
                v-else
                :src="newsImg"
                style="height: 214rem; width: 378rem"
              />
            </div>
            <div style="padding: 16rem 16rem 0 22rem; margin: 0">
              <div class="mesTitle">{{ item.newsName }}</div>
              <div class="mesText">{{ item.detail }}</div>
              <div class="mesLine"></div>
              <div class="mesDate">{{ item.addTime }}</div>
            </div>
          </div>
        </div>
        <div class="pages">
          <n-pagination
            :page-count="pageCount"
            v-model:page="page"
            @update:page="handleSearch()"
          >
            <template #prev> 上一页 </template>
            <template #next> 下一页 </template>
          </n-pagination>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="wrapper">
        <div class="newsItem" v-for="item in mesList" @click="goDetail(item)">
          <img
            class="newsImg"
            :src="item.newsPicture ? apiUrl + item.newsPicture : newsImg"
          />
          <div class="newsTitle">
            {{ item.newsName }}
          </div>
          <div class="newsContext">
            {{ item.detail }}
          </div>
          <div class="line"></div>
          <div class="newsTime">{{ item.addTime }}</div>
        </div>
        <div class="loadMore" v-if="hasNextPage" @click="getMoreDataOnPhone">
          加载更多
        </div>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { defineComponent, ref, reactive, computed } from "vue";
import { onBeforeMount, onBeforeUnmount } from "vue";
import { useRouter } from "vue-router";
import newsImg from "/@/assets/news/news_img.png";
import { getNewslist, getTablist, getSearch } from "/@/api/news/index";
import { useGlobSetting } from "/@/hooks/setting";
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
import { vue3ScrollSeamless } from "vue3-scroll-seamless";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const page = ref(1);
const pageSize = ref(8);
const hasNextPage = ref(true);
const checkTab = ref(null);
const newsList = ref([]);
const pageCount = ref(1);
const mesList = ref([]);
const sideMsg = ref({});
const router = useRouter();
const tabList = ref([]);
const key = ref(0);
const search = ref("");
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const flag = ref(false);

const classOptions = reactive({
  step: 0.9, //滚动速度值越大越快，但是值太小会卡顿
  limitMoveNum: newsList.value.length, //无缝滚动列表元素的长度，一般设置为列表的长度
  direction: 1, //方向: 0 往下 1 往上 2 向左 3 向右。
});
onBeforeMount(() => {
  getTabs();
  getData();
});

function getData(e) {
  if (e) {
    checkTab.value = null;
  }
  let params = {
    page: page.value,
    pageSize: pageSize.value,
    catid: checkTab.value,
    search: search.value,
  };
  getNewslist(params).then((res) => {
    mesList.value = res.bottomList;
    pageCount.value = res.pageInfo.pageCount;
    hasNextPage.value = res.pageInfo.hasNext;
    mesList.value.forEach((item) => {
      item.detail = item.detail.split(" ").join("");
    });
  });
}
function getMoreDataOnPhone() {
  page.value += 1;
  getNewslist({
    page: page.value,
    pageSize: pageSize.value,
    catid: "",
  }).then((res) => {
    mesList.value.push(...res.bottomList);
    hasNextPage.value = res.pageInfo.hasNext;
    pageCount.value = res.pageInfo.pageCount;
    mesList.value.forEach((item) => {
      item.detail = item.detail.split(" ").join("");
    });
  });
}
function getTabs() {
  let params = {
    catid: checkTab.value,
  };
  getTablist(params).then((res) => {
    tabList.value = res.ManageNewsCla;
    newsList.value = [];
    sideMsg.value = {};
    res.manageNewsHeaderList.forEach((e) => {
      if (e.isSlide === "1") {
        newsList.value.push(e);
      } else {
        sideMsg.value = e;
      }
    });
    if (!checkTab.value) {
      checkTab.value = tabList.value[0].VAL;
    }
  });
}
function changeValue(val) {
  page.value = 1;
  checkTab.value = val;
  flag.value = false;
  getTabs();
  getData();
}
function handleChange(e) {
  search.value = e;
}
function handleSearch() {
  let params = {
    page: page.value,
    pageSize: pageSize.value,
    catid: checkTab.value,
    search: search.value,
  };
  getSearch(params).then((res) => {
    flag.value = true;
    mesList.value = res.searchList;
    pageCount.value = res.pageInfo.pageCount;
  });
}
function goDetail(e) {
  router.push({ name: "newsDetail", query: { contentId: e.contentId } });
}

onBeforeUnmount(() => {});
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  #news {
    .news_box {
      height: 100vh;
      width: 100%;
      position: relative;

      :deep(.n-pagination-item) {
        background: #ffffff;
        border-radius: 2rem 2rem 2rem 2rem;
        border: 1rem solid #e6e6e6;
        padding: 15rem;
      }

      :deep(.n-pagination-item):hover {
        color: #005ced;
      }

      :deep(.n-pagination-item--active) {
        background: #005ced;
        border-radius: 2rem 2rem 2rem 2rem;
        color: #ffffff;
      }

      :deep(.n-input) {
        font-size: 20rem;
        height: 80rem;
        border-radius: 40rem;
        background: rgba(255, 255, 255, 0.8);
        align-items: center;
        --n-border-hover: 1rem solid #ffffff !important;
        --n-border-focus: 1rem solid #ffffff !important;
        --n-caret-color: #000 !important;
      }

      :deep(.n-tabs) {
        height: 100%;

        .n-tabs-nav {
          height: 100%;
        }

        .n-tabs-pane-wrapper {
          height: 100%;
        }
      }

      :deep(.n-tabs-tab) {
        font-weight: 500;
        font-size: 24rem;
        color: #333333;
        line-height: 24rem;
      }

      :deep(.n-tabs-bar) {
        height: 5rem;
        border-radius: 3rem;
        --n-bar-color: #0d2e99;
      }

      :deep(.v-x-scroll) {
        height: 100%;
      }
    }

    .news_box_1 {
      background: url(../../assets/news/news_bg.png);
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      height: 63vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .news_box_1_title {
        /* width: 240rem; */
        height: 60rem;
        font-weight: 400;
        font-size: 60rem;
        color: #ffffff;
        text-shadow: 0rem 6rem 4rem rgba(0, 0, 0, 0.75);
        display: flex;
        align-items: center;
      }

      .news_box_1_text {
        height: 24rem;
        font-weight: 400;
        font-size: 24rem;
        color: rgba(255, 255, 255, 0.87);
        display: flex;
        align-items: center;
        margin-top: 45rem;
      }

      .news_box_1_search {
        width: 768rem;
        height: 80rem;
        margin-top: 60rem;
      }
    }

    .news_box_2 {
      /* height: 180vh; */
      height: 100%;

      .tabLan {
        height: 80rem;
        background: #f8fafb;
      }

      .news_box_2_card {
        width: 1600rem;
        height: 539rem;
        margin: 45rem auto 0;
        display: flex;
        background: rgba(0, 0, 0, 0);

        .news_box_2_card_left {
          width: 60%;
          position: relative;
          background: rgba(245, 244, 244, 0);
          box-shadow: 0rem 0rem 22rem 0rem rgba(13, 46, 153, 0.33);
          border: 1rem solid #f9f9f9;
          transition: All 1s ease;

          .recommend {
            width: 90rem;
            height: 45rem;
            background: #005ced;
            font-weight: 500;
            font-size: 20rem;
            color: #ffffff;
            position: absolute;
            z-index: 999;
            top: 0;
            left: 0;
            border-radius: 0 0 20rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .footer {
            width: 100%;
            height: 104rem;
            background: linear-gradient(
              0deg,
              rgba(0, 0, 0, 0.52),
              rgba(0, 0, 0, 0)
            );
            position: absolute;
            bottom: 0;
            display: flex;
            justify-content: space-between;
            padding: 0 40rem;
            align-items: center;

            .name {
              font-weight: bold;
              font-size: 20rem;
              color: #ffffff;
              line-height: 32rem;
              display: flex;
              align-items: center;
            }
          }
        }

        news_box_2_card_left:hover {
          img {
            transform: scale(1.1);
          }

          cursor: pointer;
        }

        .news_box_2_card_right {
          width: 40%;
          height: 100%;
          margin-left: 10rem;
          overflow: auto;
          background: rgba(0, 0, 0, 0);
          box-shadow: 0rem 0rem 22rem 0rem rgba(13, 46, 153, 0.33);

          .detail {
            width: 100%;
            height: calc(33.33% - 10rem);
            display: flex;
            align-items: center;
            background: #ffffff;
            box-shadow: 0rem 0rem 22rem 0rem rgba(13, 46, 153, 0.33);
            border: 1rem solid #f9f9f9;
            margin-bottom: 10rem;

            .dot {
              flex-shrink: 0;
              width: 290rem;
              height: 172rem;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .content {
              flex: 1;
              display: flex;
              flex-direction: column;
              padding: 10rem;
              height: 100%;
              font-weight: bold;
              font-size: 20rem;
              color: #444444;
              // line-height: 23rem;

              .inTitle {
                width: 100%;
                height: auto;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                white-space: normal;
                cursor: pointer;
              }

              .inText {
                margin-top: 10rem;
                width: 100%;
                font-weight: 400;
                font-size: 18rem;
                color: #888888;
                line-height: 26rem;
                -webkit-line-clamp: 2;
                text-overflow: ellipsis;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
              }

              .date {
                margin-top: auto;
                font-weight: 400;
                font-size: 12rem;
                color: #888888;
                line-height: 12rem;
              }
            }
          }
          &::-webkit-scrollbar {
            display: none;
          }

          &::-webkit-scrollbar-thumb {
            display: none;
          }

          &::-webkit-scrollbar-thumb:hover {
            display: none;
          }
        }
      }

      .mesExpress {
        width: 1600rem;
        /* height: 900rem; */
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        margin: 85rem auto 0;

        :nth-child(4n + 2) {
          margin: 0 25rem;
        }

        :nth-child(4n + 3) {
          margin-right: 25rem;
        }

        .mesCard {
          width: 380rem;
          /* height: 390rem; */
          background: rgba(255, 255, 255, 0.82);
          box-shadow: 0rem 0rem 22rem 0rem rgba(13, 46, 153, 0.33);
          border: 1rem solid #f9f9f9;
          margin-top: 25rem;

          .mesTitle {
            width: 95%;
            font-weight: bold;
            font-size: 18rem;
            color: #323232;

            /* line-height: 18rem; */
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            // text-wrap: nowrap;
            overflow: hidden;
            display: -webkit-box;
          }

          .mesText {
            font-weight: 400;
            width: 95%;
            font-size: 18rem;
            color: #969696;
            line-height: 26rem;
            height: 78rem;
            overflow: hidden;
            margin: 18rem 0 0;
            -webkit-line-clamp: 3;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden;
          }

          .mesLine {
            width: 349rem;
            height: 1rem;
            background: #e6e6e6;
            margin: 17rem 0 0 0;
          }

          .mesDate {
            font-weight: 400;
            font-size: 14rem;
            color: #969696;
            line-height: 14rem;
            margin: 16rem 0;
          }
        }
      }

      .pages {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 30rem 0;
      }
    }
  }
}

// 移动端
@media screen and (max-width: 768px) {
  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 75px 0 20px;

    .newsItem {
      margin-top: 16px;
      padding-bottom: 28rem;
      width: 690rem;
      background: #ffffff;
      box-shadow: 0px 0px 9px 0px rgba(13, 46, 153, 0.33);
      border: 1px solid #f9f9f9;

      .newsImg {
        display: block;
        width: 100%;
        height: 389rem;
      }

      .newsTitle {
        padding: 30rem 16rem 0;
        font-weight: bold;
        font-size: 16px;
        color: #323232;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .newsContext {
        margin-bottom: 20rem;
        padding: 22rem 30rem 0rem 16rem;
        font-weight: 400;
        font-size: 14px;
        color: #969696;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }

      .line {
        margin: 0 auto 26rem;
        width: 667rem;
        height: 1px;
        background: #e6e6e6;
      }

      .newsTime {
        padding: 0 16rem;
        font-size: 12px;
        color: #969696;
      }
    }

    .loadMore {
      padding-top: 20px;
      width: 100%;
      text-align: center;
    }
  }
}
</style>
