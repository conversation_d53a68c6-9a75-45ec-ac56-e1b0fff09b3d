<template>
  <div id="build_detail">
    <div class="detail_1">
      <div class="detail_1_top">
        <div class="detail_1_top_title" ref="scrollContainer">新闻资讯</div>
        <div class="detail_1_top_text">
          在这里您可以阅读到各类相关信息，了解重要信息
        </div>
        <div class="detail_1_top_bread">
          <n-breadcrumb separator=">">
            <n-breadcrumb-item @click="goPage('/')">首页</n-breadcrumb-item>
            <n-breadcrumb-item @click="goPage('/news')">
              新闻资讯</n-breadcrumb-item
            >
            <n-breadcrumb-item> 新闻详情</n-breadcrumb-item>
          </n-breadcrumb>
        </div>
      </div>
      <div class="detail_1_content">
        <div class="content_title">
          {{ detail.newsName || detail.title }}
        </div>
        <div class="content_author">
          <span>发布时间： {{ detail.addTime }}</span>
          <span v-if="!route.query.flag"
            >来源单位：{{ detail.newsSource }}</span
          >
          <span>浏览量：{{ detail.views }}次</span>
        </div>
        <div
          class="content_text"
          v-html="detail.newsContent || detail.detail"
        ></div>
      </div>
      <div class="detail_1_footer">
        <div
          class="footer_left"
          @click="getActives(up)"
          v-if="route.query.flag"
        >
          <span style="text-align: left"><上一篇</span>
          <span style="color: #999999"> {{ up.newsName || up.title }}</span>
        </div>
        <div class="footer_left" @click="getData(up)" v-else>
          <span style="text-align: left"><上一篇</span>
          <span style="color: #999999"> {{ up.newsName || up.title }}</span>
        </div>
        <div
          class="footer_left"
          @click="getActives(down)"
          v-if="route.query.flag"
        >
          <span style="text-align: right">下一篇></span>
          <span style="color: #999999"> {{ down.newsName || down.title }}</span>
        </div>
        <div class="footer_left" @click="getData(down)" v-else>
          <span style="text-align: right">下一篇></span>
          <span style="color: #999999"> {{ down.newsName || down.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { getDetail, getActive } from "/@/api/news/index";
import { useRoute, useRouter } from "vue-router";
import { useGlobSetting } from "/@/hooks/setting";
import { collectUserVisitInfoAndSendToServer } from "/@/utils/visit.js";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const route = useRoute();
const router = useRouter();
const detail = ref({});
const up = ref({});
const down = ref({});
const scrollContainer = ref();
const target = () => scrollContainerRef.value;
function getData(e) {
  let contentID = 0;
  if (e) {
    contentID = e.contentId;
  } else if (route.query) {
    contentID = route.query.contentId;
  }
  let params = {
    contentId: contentID,
  };
  getDetail(params).then((res) => {
    detail.value = res.news;
    up.value = res.up;
    down.value = res.down;
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
    imgUrl();
  });
}
function getActives(e) {
  let contentID = 0;
  if (e) {
    contentID = e.contentId;
  } else if (route.query) {
    contentID = route.query.contentId;
  }
  let params = {
    contentId: contentID,
  };
  getActive(params).then((res) => {
    detail.value = res.news;
    up.value = res.up;
    down.value = res.down;
    imgUrl();
  });
}
function imgUrl() {
  const regex = /src="\/local\//;
  if (detail.value.newsContent) {
    if (regex.test(detail.value.newsContent)) {
      detail.value.newsContent = detail.value.newsContent.replace(
        /src="\/local\//g,
        `src="${apiUrl.value}/local/`
      );
    }
  } else if (detail.value.detail) {
    if (regex.test(detail.value.detail)) {
      detail.value.detail = detail.value.detail.replace(
        /src="\/local\//g,
        `src="${apiUrl.value}/local/`
      );
    }
  }
}
function goPage(path) {
  router.push(path);
}
onMounted(() => {
  if (route.query.flag) {
    getActives();
  } else {
    getData();
  }
  let params = {
    contentId: route.query.contentId,
  };
  collectUserVisitInfoAndSendToServer(params);
});
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  #build_detail {
    position: relative;
    min-width: 1600rem;
    :deep(.n-button__content) {
      font-size: 14rem;
    }
    .detail_1 {
      padding-bottom: 40rem;
      position: relative;
      background: #e5eefd;
      :deep(.n-breadcrumb-item__link) {
        color: #ffffff;
      }
      :deep(.n-breadcrumb-item__separator) {
        color: #ffffff;
      }
      .detail_1_top {
        background: url(../../assets/news/news_bg.png);
        height: 60vh;
        background-size: cover;
        display: flex;
        align-items: center;
        flex-direction: column;
        .detail_1_top_title {
          /* width: 240rem; */
          height: 60rem;
          font-weight: 400;
          font-size: 60rem;
          color: #ffffff;
          text-shadow: 0rem 6rem 4rem rgba(0, 0, 0, 0.75);
          display: flex;
          margin-top: 160rem;
          align-items: center;
        }
        .detail_1_top_text {
          height: 24rem;
          font-weight: 400;
          font-size: 24rem;
          color: rgba(255, 255, 255, 0.87);
          margin-top: 48rem;
          display: flex;
          align-items: center;
        }
        .detail_1_top_bread {
          width: 1600rem;
          margin: 48rem auto 0;
        }
      }
      .detail_1_content {
        margin: -170rem auto 0;
        width: 1600rem;
        /* height: 1694rem; */
        border: 1rem solid #f0f0f0;
        background: #ffffff;
        .content_title {
          font-weight: 500;
          font-size: 30rem;
          color: #000000;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 68rem 52rem 0;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow: hidden;
          text-align: center;
        }
        .content_author {
          width: 1000rem;
          height: 16rem;
          font-weight: 400;
          font-size: 16rem;
          color: #999999;
          line-height: 16rem;
          margin: 42rem auto 0;
          display: flex;
          justify-content: space-between;
        }
        ::v-deep .content_text {
          margin: 108rem 52rem 0;
          font-weight: 400;
          font-size: 16rem;
          color: #000000;
          /* height: 83%; */
          overflow: auto;
          line-height: 30rem;
          p {
            max-width: calc(100% - 105rem);
            word-break: break-all;
            span {
              max-width: calc(100% - 105rem);
              word-break: break-all;
              text-wrap: balance !important;
            }
          }
        }
        /* .content_footer {
        width: 1290rem;
        height: 105rem;
        background: #ffffff;
        border: 1rem solid #f0f0f0;
      } */
      }
      .detail_1_footer {
        width: 1600rem;
        height: 105rem;
        border: 1rem solid #f0f0f0;
        margin: 20rem auto 0;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        padding: 0 18rem;
        justify-content: space-between;
        .footer_left {
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #build_detail {
    position: relative;
    :deep(.n-button__content) {
      font-size: 14rem;
    }
    .detail_1 {
      padding-bottom: 40rem;
      position: relative;
      background: #e5eefd;
      :deep(.n-breadcrumb-item__link) {
        color: #ffffff;
      }
      :deep(.n-breadcrumb-item__separator) {
        color: #ffffff;
      }
      .detail_1_top {
        background: url(../../assets/news/news_bg.png);
        height: 50vh;
        background-size: cover;
        display: flex;
        align-items: center;
        flex-direction: column;
        .detail_1_top_title {
          /* width: 240rem; */
          height: 60rem;
          font-weight: 400;
          font-size: 60rem;
          color: #ffffff;
          text-shadow: 0rem 6rem 4rem rgba(0, 0, 0, 0.75);
          display: flex;
          margin-top: 265rem;
          align-items: center;
        }
        .detail_1_top_text {
          height: 24rem;
          font-weight: 400;
          font-size: 24rem;
          color: rgba(255, 255, 255, 0.87);
          margin-top: 48rem;
          display: flex;
          align-items: center;
        }
        .detail_1_top_bread {
          width: 720rem;
          margin: 48rem auto 0;
        }
      }
      .detail_1_content {
        margin: -170rem auto 0;
        width: 720rem;
        /* height: 1694rem; */
        border: 1rem solid #f0f0f0;
        background: #ffffff;
        .content_title {
          font-weight: 500;
          font-size: 30rem;
          color: #000000;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 68rem 52rem 0;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          overflow: hidden;
          text-align: center;
        }
        .content_author {
          width: 635rem;
          height: 16rem;
          font-weight: 400;
          font-size: 16rem;
          color: #999999;
          line-height: 16rem;
          margin: 42rem auto 0;
          display: flex;
          justify-content: space-between;
        }
        ::v-deep .content_text {
          margin: 108rem 52rem 0;
          font-weight: 400;
          font-size: 16rem;
          color: #000000;
          /* height: 83%; */
          overflow: auto;
          line-height: 30rem;
          :deep(img) {
            width: 100% !important;
          }
          p {
            max-width: calc(100% - 105rem);
            word-break: pre-line;
            span {
              max-width: calc(100% - 105rem);
              word-break: pre-line;
              text-wrap: none !important;
            }
          }
        }
        /* .content_footer {
        width: 1290rem;
        height: 105rem;
        background: #ffffff;
        border: 1rem solid #f0f0f0;
      } */
      }
      .detail_1_footer {
        width: 720rem;
        height: 105rem;
        border: 1rem solid #f0f0f0;
        margin: 20rem auto 0;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        padding: 0 18rem;
        justify-content: space-between;
        .footer_left {
          display: flex;
          flex-direction: column;
          width: 48%;
          height: 100%;
          span {
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
      }
    }
  }
}
</style>
