<template>
  <div id="legal">
    <div class="home-wrapper">
      <div class="main">
        <div style="margin-bottom: 45rem">
          <n-breadcrumb separator=">">
            <n-breadcrumb-item @click="goPage(`/`)">首页</n-breadcrumb-item>
            <n-breadcrumb-item @click="goPage(`/special/satellite`)"
              >卫星专区</n-breadcrumb-item
            >
            <n-breadcrumb-item>产品详情</n-breadcrumb-item>
            <!-- <n-breadcrumb-item> 国家法律规范</n-breadcrumb-item> -->
          </n-breadcrumb>
        </div>
        <div class="introduce">
          <div class="introduce-left">
            <img :src="product.imageUrl" />
          </div>
          <div class="introduce-right">
            <div class="introduce-right-title">{{ product.title }}</div>
            <div class="introduce-right-content" :title="product.intro">
              {{ product.intro }}
            </div>
            <div class="line"></div>
            <div class="introduce-right-tagList">
              <div class="tag" v-for="item in product.labels">{{ item }}</div>
            </div>
            <div
              class="apply-btn"
              @click="goPage(product.skipUrl, true)"
              v-show="product.skipUrl"
            >
              立即申请
            </div>
          </div>
        </div>
        <div class="content">
          <div class="c-title">基本信息</div>
          <div
            class="c-text"
            v-if="product.content"
            v-html="product.content"
          ></div>
          <div class="empty" v-else>
            <n-empty description="暂无信息"></n-empty>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, computed, ref, onMounted, toRefs, nextTick } from "vue";
import { getProductDetail } from "/@/api/product/detail";
import { collectUserVisitInfoAndSendToServer } from "/@/utils/visit.js";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
import { useGlobSetting } from "/@/hooks/setting";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);

const state = reactive({
  product: {},
});
const { product } = toRefs(state);

function getDetail(data) {
  getProductDetail({ contentId: data }).then((res) => {
    state.product = { ...res, imageUrl: apiUrl.value + res.imageUrl };
    const regex = /src="\/local\//;
    if (regex.test(state.product.content)) {
      state.product.content = state.product.content.replace(
        /src="\/local\//g,
        `src="${apiUrl.value}/local/`
      );
    }
    console.log(state.product);
  });
}
function goPage(path: string, outer: boolean = false) {
  if (outer && path) {
    window.open(unescapeHTML(path), "_blank");
    return;
  }
  router.push(unescapeHTML(path));
}
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
onMounted(() => {
  if (route.query.id) {
    getDetail(route.query.id);
  }
  let params = {
    contentId: route.query.id,
  };
  collectUserVisitInfoAndSendToServer(params);
});
</script>
<style lang="less" scoped>
@media screen and (min-width: 769px) {
  #legal {
    min-width: 1024px;
    .home-wrapper {
      position: relative;
      padding-top: 149rem;
      width: 100%;
      min-height: 100vh;
    }
    .main {
      position: relative;
      margin: 0 auto;
      width: calc(100% - 320rem);
      // max-width: 1600px;
      .introduce {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 440rem;
        .introduce-left {
          margin-right: 83rem;
          width: 640rem;
          // min-width: 340px;
          height: 100%;
          & > img {
            width: 100%;
            height: 100%;
          }
        }
        .introduce-right {
          position: relative;
          flex-grow: 1;
          height: 100%;
          .introduce-right-title {
            margin-bottom: 31rem;
            font-size: 36rem;
            font-weight: 700;
            letter-spacing: 0.2rem;
            line-height: 30rem;
            color: rgba(37, 43, 66, 1);
          }
          .introduce-right-content {
            margin-bottom: 27rem;
            width: 670rem;
            max-width: 100%;
            font-size: 18rem;
            font-weight: 400;
            letter-spacing: 0.2rem;
            // line-height: 20rem;
            color: rgba(85, 85, 85, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }
          .line {
            margin-bottom: 24rem;
            width: 680rem;
            max-width: 100%;
            height: 0rem;
            opacity: 1;
            border: 1rem solid rgba(189, 189, 189, 0.6);
          }
          .introduce-right-tagList {
            margin-top: 24rem;
            width: 658rem;
            max-width: 100%;
            max-height: 135rem;
            overflow: hidden;
            .tag {
              display: inline-block;
              margin-right: 14rem;
              width: 130rem;
              height: 45rem;
              line-height: 45rem;
              text-align: center;
              font-size: 16rem;
              font-weight: 400;
              color: rgba(136, 136, 136, 1);
              border-radius: 60rem;
              background: rgba(245, 245, 245, 1);
              border: 1rem solid rgba(235, 235, 235, 1);
              cursor: default;
            }
          }
          .apply-btn {
            position: absolute;
            left: 0;
            bottom: 10rem;
            width: 238rem;
            height: 60rem;
            line-height: 60rem;
            text-align: center;
            opacity: 1;
            border-radius: 600rem;
            background: rgba(13, 46, 153, 1);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            color: #fff;
            cursor: pointer;
          }
        }
      }
      .content {
        margin-top: 77rem;
        width: 100%;
        .c-title {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 80rem;
          border-top: 1rem solid rgba(189, 189, 189, 0.6);
          border-bottom: 1rem solid rgba(189, 189, 189, 0.6);
          font-size: 20rem;
          font-weight: 700;
          letter-spacing: 0.2rem;
          color: rgba(115, 115, 115, 1);
        }
        .c-text {
          padding: 52rem 0;
          width: 100%;
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #legal {
    /* min-width: 1024px; */
    .home-wrapper {
      position: relative;
      padding-top: 149rem;
      width: 100%;
      min-height: 100vh;
    }
    .main {
      position: relative;
      margin: 0 auto;
      width: 720rem;
      // max-width: 1600px;
      .introduce {
        display: flex;
        flex-direction: column;
        width: 100%;
        /* height: 440rem; */
        .introduce-left {
          margin: 0 auto 20rem;
          width: 640rem;
          // min-width: 340px;
          height: 100%;
          & > img {
            width: 100%;
            height: 100%;
          }
        }
        .introduce-right {
          position: relative;
          flex-grow: 1;
          height: 100%;
          .introduce-right-title {
            margin-bottom: 24rem;
            font-size: 36rem;
            font-weight: 700;
            letter-spacing: 0.2rem;
            /* line-height: 30rem; */
            color: rgba(37, 43, 66, 1);
          }
          .introduce-right-content {
            margin-bottom: 27rem;
            width: 670rem;
            max-width: 100%;
            font-size: 18rem;
            font-weight: 400;
            letter-spacing: 0.2rem;
            // line-height: 20rem;
            color: rgba(85, 85, 85, 1);
            // overflow: hidden;
            // text-overflow: ellipsis;
            // display: -webkit-box;
            // -webkit-line-clamp: 3;
            // -webkit-box-orient: vertical;
          }
          .line {
            margin-bottom: 24rem;
            width: 680rem;
            max-width: 100%;
            height: 0rem;
            opacity: 1;
            border: 1rem solid rgba(189, 189, 189, 0.6);
          }
          .introduce-right-tagList {
            margin-top: 24rem;
            width: 658rem;
            max-width: 100%;
            max-height: 135rem;
            overflow: hidden;
            .tag {
              display: inline-block;
              margin-right: 14rem;
              width: 130rem;
              height: 45rem;
              line-height: 45rem;
              text-align: center;
              font-size: 16rem;
              font-weight: 400;
              color: rgba(136, 136, 136, 1);
              border-radius: 60rem;
              background: rgba(245, 245, 245, 1);
              border: 1rem solid rgba(235, 235, 235, 1);
              cursor: default;
            }
          }
          .apply-btn {
            /* position: absolute;
            left: 0;
            bottom: 10rem; */
            width: 238rem;
            height: 60rem;
            line-height: 60rem;
            text-align: center;
            opacity: 1;
            border-radius: 600rem;
            background: rgba(13, 46, 153, 1);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            color: #fff;
            cursor: pointer;
            margin-top: 24rem;
          }
        }
      }
      .content {
        margin-top: 36rem;
        width: 100%;
        .c-title {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 80rem;
          border-top: 1rem solid rgba(189, 189, 189, 0.6);
          border-bottom: 1rem solid rgba(189, 189, 189, 0.6);
          font-size: 20rem;
          font-weight: 700;
          letter-spacing: 0.2rem;
          color: rgba(115, 115, 115, 1);
        }
        .c-text {
          padding: 52rem 0;
          width: 100%;
          :deep(p) {
            width: 100% !important;
            img {
              width: 100% !important;
            }
          }
        }
      }
    }
  }
}
</style>
