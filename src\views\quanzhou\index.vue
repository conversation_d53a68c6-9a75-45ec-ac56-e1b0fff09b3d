<template>
  <div id="home">
    <div class="home_box home_box_1">
      <n-carousel
        ref="carousel"
        draggable
        autoplay
        dot-type="line"
        class="home_box_1_carousel"
      >
        <n-carousel-item
          class="n-carousel-item"
          v-if="home_1_videoUrl.length != 0"
          v-for="(item, index) in home_1_videoUrl"
          style="width: 100%; height: 100%"
        >
          <div class="home_box_1_item">
            <video
              :src="apiUrl + item"
              autoplay
              loop
              muted
              style="width: 100%; height: 100%; object-fit: fill"
              :poster="apiUrl + coverImg"
            >
              <!-- <source
                :src="apiUrl+`/local/video/2024-04-13/test/output.m3u8`"
                type="application/x-mpegURL"
            /> -->
            </video>
          </div>
        </n-carousel-item>
        <n-carousel-item
          class="n-carousel-item"
          v-else
          v-for="(item, index) in home_1_imgUrls"
          style="width: 100%; height: 100%"
        >
          <div class="home_box_1_item">
            <img :src="apiUrl + item" alt="" />
          </div>
        </n-carousel-item>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              @click="to(index - 1)"
            />
          </ul>
        </template>
      </n-carousel>
      <div class="glass">
        <div class="banner-content">
          <div class="title">{{ titleContext.title }}</div>
          <div>
            <n-button
              color="#ffffff"
              ghost
              round
              @click="goPage(titleContext.buttonUrl)"
              v-if="titleContext.buttonName"
            >
              {{ titleContext.buttonName }}
            </n-button>
          </div>
          <div class="context" v-html="titleContext.detail"></div>
        </div>
        <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div>
      </div>
    </div>
    <div class="dataTab">
      <div
        style="width: 1600rem; margin: 60rem auto; display: flex"
        v-if="!isMobile"
      >
        <div class="dataItem" v-for="item in dataTabList">
          <div class="itemImg">
            <img :src="apiUrl + item.imageUrl" alt="" />
          </div>
          <div class="itemText">
            <div class="itemTitle">{{ item.TITLE }}</div>
            <div
              class="itemSubtitle"
              ref="itemSubtitle"
              :data-count="item.INTRO"
            >
              {{ item.INTRO }}
              <!-- <CountUp
                :end-val="item.INTRO"
                :duration="2.5"
                :decimal-places="0"
                :delay="2"
                @init="onInit"
                @finished="onFinished"
              ></CountUp> -->
            </div>
          </div>
        </div>
      </div>
      <div style="margin: 60rem auto; display: flex" v-else>
        <div class="dataItem" v-for="item in dataTabList">
          <div class="itemImg">
            <img :src="apiUrl + item.imageUrl" alt="" />
          </div>
          <div class="itemText">
            <div class="itemTitle">{{ item.TITLE }}</div>
            <div
              class="itemSubtitle"
              ref="itemSubtitle"
              :data-count="item.INTRO"
            >
              <CountUp
                :end-val="item.INTRO"
                :duration="2.5"
                :decimal-places="0"
                :delay="2"
                @init="onInit"
                @finished="onFinished"
              ></CountUp>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="home_box home_box_7">
      <div class="left">
        <div class="title">{{ home_3_msg }}</div>
        <div class="context" v-html="home_3_detail"></div>
        <!-- <div class="subTitle">
          <div style="float: left">了解更多</div>
          <div class="toRightIcon"></div>
        </div> -->
      </div>
      <div class="right">
        <img style="width: 100%" :src="apiUrl + home_3_imgUrls" alt="" />
      </div>
    </div>
    <div class="tablePart">
      <div class="title">
        {{ home_8_msg ? home_8_msg : "" }}
      </div>
      <div
        class="rightBody"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <n-data-table
          :data="tableData"
          :columns="columns"
          size="large"
          ref="tableRef"
          max-height="465rem"
          style="margin-top: 28rem; height: 575rem"
        />
      </div>
    </div>
    <div class="tablePart">
      <div class="title">
        {{ home_9_msg ? home_9_msg : "" }}
      </div>
      <div
        class="rightBody"
        @mouseenter="handleMouseEnter2"
        @mouseleave="handleMouseLeave2"
      >
        <n-data-table
          :data="tableData2"
          :columns="columns2"
          size="large"
          ref="tableRef2"
          max-height="465rem"
          style="margin-top: 28rem; height: 575rem"
        />
      </div>
    </div>
    
    <div class="home_box home_box_2">
      <div class="home_boex_2_bg" v-if="!isMobile"></div>
      <div
        class="home_boex_2_bg"
        style="left: calc(1624 / 1903 * 100%); top: calc(164 / 1080 * 116vh)"
        v-if="!isMobile"
      ></div>
      <div class="home_box_2_top">
        <div>
          <div class="home_title" style="color: #fff">
            {{ home_2_msg.title }}
          </div>
          <!-- <div class="home_title_1" style="color: #aaa">
            {{ home_2_msg.subTitle }}
          </div> -->
        </div>
        <!-- <div class="home_detail">{{ home_2_msg.detail }}</div> -->
      </div>
      <div class="content" v-if="!isMobile">
        <div class="swiper-button-prev swiper-button-next1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-prev1">
          <img :src="arrowR" />
        </div>
        <swiper
          :slides-per-view="4"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :slidesPerGroup="4"
          class="swiperRef"
          :modules="modules"
        >
          <!--@click="goCreateDetail(item.contentId)"-->
          <swiper-slide class="home_box_2_card" v-for="item in home_2_list">
            <div class="home_box_2_item">
              <img :src="apiUrl + item.logo" alt="" />
              <div class="home_box_2_text">
                <div class="title">{{ item.title }}</div>
                <div class="fTitle">{{ item.subTitle }}</div>
                <div class="Hbar"></div>
                <div class="con" v-html="item.detail"></div>
                <!-- <div class="Hbottom"> 
                  <img
                    :src="toRight"
                    alt=""
                    style="width: 60rem; height: 9rem"
                  />
                </div>-->
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="content" v-else>
        <!-- <div class="swiper-button-prev swiper-button-next1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-prev1">
          <img :src="arrowR" />
        </div> -->
        <swiper
          :slides-per-view="2"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :slidesPerGroup="2"
          class="swiperRef"
          :modules="modules"
        >
          <!--   @click="goCreateDetail(item.contentId)"-->
          <swiper-slide class="home_box_2_card" v-for="item in home_2_list">
            <div class="home_box_2_item">
              <img :src="apiUrl + item.logo" alt="" />
              <div class="home_box_2_text">
                <div class="title">{{ item.title }}</div>
                <div class="fTitle">{{ item.subTitle }}</div>
                <div class="Hbar"></div>
                <div class="con" v-html="item.detail"></div>
                <!-- <div class="Hbottom">
                  <img
                    :src="toRight"
                    alt=""
                    style="width: 60rem; height: 9rem"
                  />
                </div> -->
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <div class="home-wrapper-free">
      <div class="prod-box">
        <div class="title" id="prod-title-title">
          <div class="prod-title-context">{{ home_7_msg.title }}</div>
          <div
            style="max-width: 50%; width: 40%"
            v-if="state.prodMenuList?.length"
          >
            <n-tabs
              type="line"
              animated
              size="large"
              justify-content="space-evenly"
              @update:value="changeProd"
              ref="tabsInstRef"
              pane-wrapper-style="border:0;"
              tab-style="color: rgba(115, 115, 115, 1);font-size: 20rem;font-weight: 400;"
            >
              <n-tab v-for="item in state.prodMenuList" :name="item.id">
                {{ item.name }}
              </n-tab>
            </n-tabs>
          </div>
        </div>
        <div class="subTitle" v-if="false">
          {{ home_7_msg.title }}
        </div>
        <div class="subTitle" v-else></div>
        <div class="prod-main">
          <template
            v-if="state.prodList.prod && state.prodList.prod.length > 0"
          >
            <div class="prod-item-box">
              <div
                class="prod-item"
                v-for="(item, index) in state.prodList.prod"
                @click="goPage(`/zhangzhouProduct?id=${item.contentId}`)"
                :key="index"
              >
                <!-- -->
                <div class="prod-info">
                  <div class="title">
                    <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                      <template #trigger> {{ item.title }} </template>
                      {{ item.title }}
                    </n-tooltip>
                  </div>

                  <div class="context" style="color: #555">
                    <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                      <template #trigger>
                        {{ item.intro }}
                      </template>
                      {{ item.intro }}
                    </n-tooltip>
                  </div>
                  <div class="prod-tag">
                    <div class="prod-tag-item" v-for="i in item.labels">
                      {{ i }}
                    </div>
                  </div>
                  <div class="prod-view">
                    <img :src="item.icon" class="prod-view-eye" />
                    <div class="prod-view-text">
                      {{ item.clickNum + "点击" }}
                    </div>
                  </div>
                </div>
                <div class="prod-img">
                  <div class="prod-arrow-box">
                    <img :src="prodArrow" alt="" />
                  </div>
                  <img :src="item.imageUrl" />
                </div>
                <div class="prod-show">
                  <div class="title">
                    <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                      <template #trigger> {{ item.title }} </template>
                      {{ item.title }}
                    </n-tooltip>
                  </div>
                  <div class="context">
                    <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                      <template #trigger>
                        {{ item.intro }}
                      </template>
                      {{ item.intro }}
                    </n-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="empty">
              <n-empty description="当前类目暂无产品"></n-empty>
            </div>
          </template>
        </div>

        <div class="prod-pag">
          <n-pagination
            :page-count="state.prodList.totalPage"
            v-model:page="state.prodList.pageNum"
            @update:page="changeProdPage"
          >
            <template #prev>
              <img :src="arrowLm" alt="" style="width: 23px; height: 20px" />
            </template>
            <template #next>
              <img :src="arrowRm" alt="" style="width: 23px; height: 20px" />
            </template>
          </n-pagination>
        </div>
      </div>
    </div>

    <div class="home_box home_box_4">
      <div class="home_box_header">
        <div class="home_title">{{ home_4_msg.title }}</div>
        <!-- <div class="home_title_1">{{ home_4_msg.subTitle }}</div>
        <div class="home_detail">{{ home_4_msg.detail }}</div>-->
        <!-- <div class="home_more dynamicMore" @click="goPage('/news')">
          More ——
        </div> -->
      </div>

      <div class="home_box_4_content">
        <div class="home_box_4_content_left" @click="goNews(sideMsg)">
          <div class="left_img">
            <img v-if="sideMsg.image" :src="apiUrl + sideMsg.image" alt="" />
            <img v-else src="../../assets/home/<USER>" alt="" />
          </div>
          <div class="left_title">{{ sideMsg.title }}</div>
          <div class="left_text">{{ sideMsg.detail }}</div>
        </div>
        <div class="home_box_4_content_right">
          <div
            class="rightItem"
            v-for="item in home_4_list"
            @click="goNews(item)"
          >
            <div class="right_img">
              <img v-if="item.image" :src="apiUrl + item.image" alt="" />
              <img v-else src="../../assets/home/<USER>" alt="" />
            </div>
            <div class="right_title">
              {{ item.title }}
            </div>
            <div class="right_text">
              {{ item.detail }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home_box home_box_6">
      <div class="home_box_header">
        <div class="home_title">服务商生态</div>
        <!--  <div class="home_title_1">{{ home_5_msg.subTitle }}</div>
        <div class="home_detail">{{ home_5_msg.detail }}</div>-->
      </div>
      <div class="home_box_6_content">
        <n-carousel autoplay loop draggable>
          <n-carousel-item
            v-for="(im, index) in home_6_list[0]"
            style="margin-bottom: 20rem"
          >
            <div class="home_box_6_carousel">
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(0, 8)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(8, 14)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(14, 20)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(20, 26)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="cLine">
                <div class="cItem" v-for="i in im.slice(26, 34)">
                  <img v-if="i.logo" :src="apiUrl + i.logo" alt="" />
                </div>
              </div>
              <div class="dItem">
                <img
                  src="../../assets/shushang.png"
                  alt=""
                  style="width: 100%"
                />
              </div>
            </div>
          </n-carousel-item>
          <template #dots="{ total, currentIndex, to }">
            <ul class="custom-dots">
              <li
                v-for="index of total"
                :key="index"
                :class="{ ['is-active']: currentIndex === index - 1 }"
                @click="to(index - 1)"
              />
            </ul>
          </template>
        </n-carousel>
      </div>
    </div>
    <router-view></router-view>
    <!-- <div class=" customer-service" @click="showIm = !showIm">
      <img src="/@/assets/home/<USER>" alt="" />
    </div> -->
    <!-- <Im ref="imRef" v-if="showIm" @close="showIm = false"/> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, nextTick } from "vue";
import dayjs from "dayjs";
import cBg from "/@/assets/home/<USER>";
import hone_bg_2 from "/@/assets/home/<USER>";
import toRight from "/@/assets/home/<USER>";
import arrowL from "/@/assets/home/<USER>";
import arrowR from "/@/assets/home/<USER>";
import arrowLm from "/@/assets/special/arrow-l.png";
import arrowRm from "/@/assets/special/arrow-r.png";
import prodArrow from "/@/assets/special/prod-arrow.png";
import {
  getBg,
  getPolicy,
  getApplication,
  getConfig,
  getPartner,
  cooperativeZZNumberQuotient,
} from "/@/api/home/<USER>";
import {
  getZoneCarousel,
  getBrandType,
  getProdList,
  getProductTypeList,
  tradingUpdates,
  registerUpdates,
} from "/@/api/special/traffic";
import { getCooper } from "/@/api/digtal/index";
import { getActivities, getAdvantage } from "/@/api/digtal/index";
import { getTablist } from "/@/api/news/index";
import { useGlobSetting } from "/@/hooks/setting";
import { useRouter } from "vue-router";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay, Navigation } from "swiper/modules";
import CountUp from "vue-countup-v3";
import type { ICountUp, CountUpOptions } from "vue-countup-v3";
const modules = [Autoplay, Navigation];
import "swiper/css";
import "swiper/css/navigation";
const router = useRouter();
const globSetting = useGlobSetting();
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const apiUrl = ref(globSetting.apiUrl);
const carousel = ref(null);
const dataTabList = ref([]);
const home_2_list = ref([]);
const home_2_activ = ref(0);
const home_3_detail = ref("");
const home_3_imgUrls = ref("");
const home_4_list = ref([]);
const home_1_imgUrls = ref([]);
const home_1_videoUrl = ref([]);
const home_2_msg = ref({});
const home_3_msg = ref({});
const home_4_msg = ref({});
const home_5_msg = ref({});
const home_6_list = ref([]);
const home_7_msg = ref({});
const home_8_msg = ref("最新交易动态");
const home_9_msg = ref("最新登记动态");
const sideMsg = ref({});
const titleContext = ref({});
const coverImg = ref("");
const showIm = ref(false);
const tableData = ref([]);
const tableData2 = ref([]);
const scrollTopNum = ref(0);
const scrollTopNum2 = ref(0);
const isStopPlay = ref(false);
const isStopPlay2 = ref(false);
const tableRef = ref(null);
const tableRef2 = ref(null);
const interval = ref(0);
const interval2 = ref(0);
const columns = ref([
  {
    title: "名称",
    key: "NAME",
  },
  {
    title: "产品描述",
    key: "DESCRIBE",
  },
  {
    title: "买方",
    key: "BUYER",
  },
  {
    title: "卖方",
    key: "SELLER",
  },
]);
const columns2 = ref([
  {
    title: "企业名称",
    key: "NAME",
  },
  {
    title: "登记数据",
    key: "DJSJ",
  },
  {
    title: "数据资源简介",
    key: "SJZYJJ",
  },
  {
    title: "登记时间",
    key: "DJRQ",
  },
]);
const state = reactive({
  prodMenuList: [
    // {
    //   title: "Data advantages",
    //   name: "产品特色1",
    //   id: 1,
    // },
  ],
  prodList: {
    pageSize: 6,
    pageNum: 1,
    totalPage: 1,
    type: null,
    prod: [],
  },
});
function getbox1Bg() {
  let params = {
    classId: "zzzqdblbt",
  };
  getBg(params).then((res) => {
    home_1_imgUrls.value = res.imgUrls;
    home_1_videoUrl.value = res.videoUrl;
    titleContext.value = {
      title: res.title,
      subTitle: res.subTitle,
      detail: res.detail,
      buttonName: res.buttonName,
      buttonUrl: res.buttonUrl,
    };
    coverImg.value = res.cover;
  });
}
function getPolicyData() {
  let params = {
    classId: "zzzqfwpt",
  };
  getBg(params).then((res) => {
    home_3_detail.value = res.detail;
    home_3_msg.value = res.title;
    home_3_imgUrls.value = res.imgUrls[0];
  });
}
function goDetail(e) {
  router.push({
    name: "policyGuidce",
    query: { contentId: e, title: home_3_msg.value.title },
  });
}
function getBox2Data() {
  let params = {
    classId: "zzzqywbk",
  };
  getApplication(params).then((res) => {
    home_2_list.value = res;
  });
}
function getTitleConfig() {
  let params = {
    classId: [
      "ywbkmkbt",
      "ywdtmkbt",
      "cpzqmkbt",
      "zzzxjydtmkbt",
      "zzzxdjdtmkbt",
    ],
  };
  getConfig(params).then((res) => {
    res.forEach((e) => {
      if (e.classId == 11248) {
        home_2_msg.value = e;
      } else if (e.classId == 11249) {
        home_4_msg.value = e;
      } else if (e.classId == 11250) {
        home_7_msg.value = e;
      } else if (e.classId == 11284) {
        home_7_msg.value = e;
      } else if (e.classId == 11286) {
        home_7_msg.value = e;
      }
    });
  });
}
function getActive() {
  let params = {
    classId: "zzzqywdt",
  };
  getActivities(params).then((res) => {
    res.forEach((e) => {
      if (e.isRec == "1") {
        sideMsg.value = e;
      } else {
        home_4_list.value.push(e);
      }
    });
  });
}
function goNews(e) {
  router.push({
    name: "newsDetail",
    query: { contentId: e.contentId, flag: true },
  });
}

function goCreateDetail(e) {
  router.push({ name: "createDetail", query: { contentId: e } });
}
function getCP() {
  //  getCooper
  cooperativeZZNumberQuotient().then((res) => {
    home_6_list.value = res;
    home_6_list.value.forEach((res) => {
      res.forEach((e) => {
        if (e.length < 34) {
          let num = 34 - e.length;
          for (let i = 0; i < num; i++) {
            e.push({
              contentId: i,
              detail: "",
              image: "",
              logo: "",
              subTitle: "",
              title: "",
              type: "",
            });
          }
        }
      });
    });
  });
}
function goPage(path) {
  const regex = /(https?)/;
  if (regex.test(path)) {
    window.open(unescapeHTML(path));
  } else {
    router.push(path);
    localStorage.setItem("pagePath", path);
  }
}
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
function getMenuProdList() {
  getBrandType({ type: "zqcpfl", classId: "yytscp" }).then((res: any) => {
    if (res && res.length > 0) {
      state.prodMenuList = res.map((i) => {
        return {
          name: i.dicWord,
          id: i.val,
        };
      });
      state.prodMenuList.unshift({
        name: "全部",
        id: "",
      });
      nextTick(() => tabsInstRef.value?.syncBarPosition());
      state.prodList.type = state.prodMenuList[0].id;
      getProdList(state.prodList.type);
    } else {
      state.prodList.type = "";
      getProdList(state.prodList.type);
    }
  });
}
function getProdList(data, scroll = false) {
  getProductTypeList({
    classId: "zzzqcpzq",
    // type: data,
    pageNum: state.prodList.pageNum,
    pageSize: state.prodList.pageSize,
  }).then((res) => {
    state.prodList.totalPage = res[1].pageCount;
    state.prodList.prod = res[0].map((i) => {
      return {
        ...i,
        imageUrl: apiUrl.value + i.imageUrl,
        icon: new URL(`/@/assets/special/eye.png`, import.meta.url).href,
      };
    });
    if (scroll) {
      const targetElement = document.getElementById("prod-title-title");
      // targetElement.scrollIntoView({ behavior: "smooth" });
      targetElement.scrollIntoView();
    }
  });
}
function changeProd(value) {
  state.prodList.pageNum = 1;
  state.prodList.type = value;
  getProdList(state.prodList.type);
}
function changeProdPage(page) {
  console.log("changeProdPage", page, state.prodList);
  getProdList(state.prodList.type, true);
}
function getDataTab() {
  let params = {
    classId: 11282,
  };
  getAdvantage(params).then((res) => {
    dataTabList.value = res;
  });
}
let countUp: ICountUp | undefined;
const onInit = (ctx: ICountUp) => {
  console.log("init", ctx);
  countUp = ctx;
};
const onFinished = () => {
  console.log("finished");
};
// function getVisition(){
//   let spt = document.createElement('script')
//   let contentId = localStorage.getItem('pagePath')
//   spt.src = apiUrl.vue
// }
function autoScrollTable(direction, distance) {
  if (isStopPlay.value) {
    return;
  }
  let maxHeight = 0;
  if (tableRef.value) {
    maxHeight = parseInt(tableRef.value.maxHeight.split("rem")[0]);
  }
  let scrollHeight = scrollTopNum.value;
  if (maxHeight > scrollHeight) {
    scrollTopNum.value += distance;
  } else {
    scrollTopNum.value = 0;
  }
  if (tableRef.value) {
    tableRef.value.scrollTo({ top: scrollTopNum.value });
  }
}
function autoScrollTable2(direction, distance) {
  if (isStopPlay2.value) {
    return;
  }
  let maxHeight = 0;
  if (tableRef2.value) {
    maxHeight = parseInt(tableRef2.value.maxHeight.split("rem")[0]);
  }
  let scrollHeight = scrollTopNum2.value;
  if (maxHeight > scrollHeight) {
    scrollTopNum2.value += distance;
  } else {
    scrollTopNum2.value = 0;
  }
  if (tableRef.value) {
    tableRef2.value.scrollTo({ top: scrollTopNum2.value });
  }
}
function handleMouseEnter() {
  isStopPlay.value = true;
}
function handleMouseLeave() {
  if (tableRef.value) {
    // 获取横向和纵向滚动位置
    const scrollContainer = tableRef.value.$el.querySelector(
      ".n-scrollbar-container"
    );
    // return {
    //   left: scrollContainer.scrollLeft,
    //   top: scrollContainer.scrollTop
    // }
    scrollTopNum.value = Math.floor(scrollContainer.scrollTop);
  }
  isStopPlay.value = false;
}
function handleMouseEnter2() {
  isStopPlay2.value = true;
}
function handleMouseLeave2() {
  if (tableRef2.value) {
    // 获取横向和纵向滚动位置
    const scrollContainer = tableRef2.value.$el.querySelector(
      ".n-scrollbar-container"
    );
    // return {
    //   left: scrollContainer.scrollLeft,
    //   top: scrollContainer.scrollTop
    // }
    scrollTopNum2.value = Math.floor(scrollContainer.scrollTop);
  }
  isStopPlay2.value = false;
}
function getTableData() {
  let params = {
    classId: 11283,
  };
  tradingUpdates(params).then((res) => {
    console.log(res);
    tableData.value = res;
  });
  let params2 = {
    classId: 11285,
  };
  registerUpdates(params2).then((res) => {
    console.log(res);
    tableData2.value = res.map((i) => {
      i.DJRQ = dayjs(i.DJRQ).format("YYYY-MM-DD");
      return i;
    });
  });
}
onMounted(() => {
  getbox1Bg();
  getPolicyData();
  getBox2Data();
  getTitleConfig();
  getActive();
  getProdList("zzzqcpzq");
  getDataTab();
  getTableData();
  getCP();
  nextTick(() => {
    clearInterval(interval.value);
    clearInterval(interval2.value);
    interval.value = setInterval(() => {
      autoScrollTable("up", 1); // 向下滚动1px
    }, 50);
    interval2.value = setInterval(() => {
      autoScrollTable2("up", 1); // 向下滚动1px
    }, 50);
  });
});
</script>
<style scoped lang="less">
@media screen and (min-width: 769px) {
  #home {
    font-family: MiSans;
    :deep(.swiper) {
      display: flex;
      overflow: hidden;
      width: 100%;
      .swiper-wrapper {
        display: flex;
      }
    }
    :deep(.swiper-button-prev1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      left: -80rem;
      transform: rotate(180deg);
      background: rgba(237, 237, 237, 0.6);
    }
    :deep(.swiper-button-next1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      right: -80rem;
      left: auto;
      transform: rotate(180deg);
      background: #f6f6f6;
    }
    .swiper-button-prev:after,
    .swiper-button-next:after {
      font-family: "";
    }
    .swiper-button-next:after {
      content: "";
    }
    .swiper-button-prev:after {
      content: "";
    }
    .home_box {
      height: 100vh;
      width: 100%;
      position: relative;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      position: relative;
      .home_title {
        line-height: 96rem;
        color: rgba(56, 56, 56, 1);
        font-size: 50rem !important;
        font-weight: 900;
        letter-spacing: 0rem;
        margin-bottom: 4rem;
      }
      .home_title_1 {
        font-size: 32rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47.74rem;
        color: rgba(128, 128, 128, 1);
      }
      .home_detail {
        font-size: 24rem;
        font-weight: 300;
        letter-spacing: 1rem;
        /* line-height: 24rem; */
        color: rgba(128, 128, 128, 1);
        width: 1133rem;
        margin-top: 22rem;
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: wrap;
      }
      .home_more {
        position: absolute;
        right: 0;
        top: 0;
        /* width: 130rem; */
        height: 28rem;
        font-size: 16rem;
        font-weight: 500;
        line-height: 28rem;
        color: rgba(40, 41, 56, 1);
      }
      .home_box_header {
        display: flex;
        flex-direction: column;
        position: relative;
        /* margin: calc(57 / 1080 * 100%) auto 0; */
        margin: 60rem auto 0;
        width: 1600rem;
      }
      .carousel-img {
        width: 100%;
        height: 240rem;
        object-fit: cover;
      }
    }
    img {
      // width: 100%;
    }

    .home_box_1 {
      /* background: url("../../assets/home1.gif"); */
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      .glass {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        /* background: linear-gradient(
          to right,
          rgba(13, 46, 153, 0.8) 0%,
          rgba(70, 128, 255, 0) 100%
        ); */
      }
      .banner-content {
        position: absolute;
        top: calc(308 / 1080 * 100vh);
        left: 160rem;
        font-size: 20rem;
        letter-spacing: 1rem;
        width: 75%;
        color: rgba(255, 255, 255, 1);
        & > .title {
          font-size: 50rem;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .subTitle {
          font-size: 36rem;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .context {
          margin-top: 26rem;
          line-height: 28rem;
          font-size: 18rem;
          font-weight: 300;
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        position: absolute;
        bottom: 7%;
        left: 50%;
        transform: translateX(50%);
        animation: blink 2s infinite;
      }
      .home_box_1_carousel {
        height: 100%;
        position: relative;
        .home_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
    }
    .home_box_2 {
      position: relative;
      /* height: 110vh; */
      height: 95vh;
      .home_boex_2_bg {
        width: 139rem;
        height: 95rem;
        left: calc(59 / 1903 * 100%);
        top: calc(813 / 1080 * 116vh);
        position: absolute;
        background: url(../../assets//home/<USER>
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
      }
      .home_box_2_top {
        display: flex;
        flex-direction: column;
        /* margin-left: 160rem; */
        /* margin-top: 100rem; */
        width: 1600rem;
        /* margin: calc(100 / 1080 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .content {
        z-index: 1;
        display: flex;
        width: 1600rem;
        margin: 30rem auto 40rem;
        position: relative;
        .home_box_2_card {
          width: calc(370 / 1600 * 100%) !important;
          height: 100%;
          /* margin-right: 40rem; */
          .home_box_2_item {
            height: 60vh;
            width: 100%;
            position: relative;
            overflow: hidden;
            transition: transform 1s;
            img {
              width: 100%;
              height: 100%;
              object-fit: fill;
              transition: transform 1s;
            }
            .home_box_2_text {
              position: absolute;
              top: calc((100% - 464rem) / 2);
              width: 100%;
              height: 464rem;
              /* opacity: 0.8; */
              // background: linear-gradient(
              //   0deg,
              //   rgba(13, 46, 153, 0.8) 0%,
              //   rgba(13, 46, 153, 0) 100%
              // );
              .title {
                margin-top: 125rem;
                line-height: 47.74rem;
                color: #ffffff;
                font-size: 36rem;
                font-weight: 700;
                text-align: center;
                vertical-align: top;
              }
              .fTitle {
                opacity: 1;
                font-size: 14rem;
                font-weight: 300;
                letter-spacing: 2rem;
                line-height: 19rem;
                color: #ffffff;
                text-align: center;
                vertical-align: top;
              }
              .Hbar {
                width: 80rem;
                height: 8rem;
                background: #ffffff;
                margin: 16rem auto 0;
              }
              .con {
                width: 300rem;
                height: 190rem;
                font-size: 18rem;
                font-weight: 300;
                /* line-height: 18rem; */
                color: #ffffff;
                text-align: left;
                vertical-align: top;
                margin: 8rem auto 0;
                -webkit-line-clamp: 5;
                text-overflow: ellipsis;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
              }
              .Hbottom {
                width: 104rem;
                height: 54rem;
                border-radius: 60rem;
                border: 1rem solid #ffffff;
                margin: 20rem auto 0;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
            img {
              height: 100%;
              width: 100%;
            }
          }
          &:nth-child(n + 1) {
            margin-right: 34rem;
          }
        }

        .home_box_2_card:hover {
          cursor: pointer;
          border-radius: 14px;
          overflow: hidden;
          img {
            transform: scale(1.5); /* 放大图片两倍 */
          }
        }

        .ownSwiper {
          display: flex;
          flex-direction: row;
          justify-content: center;
          width: 100%;
          margin-top: -3rem;
          .swiperBtn {
            border: 0;
            box-shadow: 0 0.25rem 0.75rem rgba(30, 34, 40, 0.02);
            width: 2.2rem;
            height: 2.2rem;
            line-height: inherit;
            border-radius: 100%;
            text-shadow: none;
            transition: all 0.2s ease-in-out;
            background: rgba(var(--bs-primary-rgb), 0.9) !important;
            margin: 0 0.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .btn-disabled {
            background: rgba(var(--bs-primary-rgb), 0.7) !important;
            opacity: 0.35;
            cursor: auto;
            pointer-events: none;
          }
          .button-prev:after {
            content: "\e949";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
          .button-next:after {
            content: "\e94c";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
        }
      }
    }
    .home_box_3 {
      position: relative;
      background-color: rgba(245, 245, 245, 1);
      display: flex;
      flex-direction: column;
      /* height: 110vh; */
      height: 100%;
      // align-items: center;
      .carousel {
        // height: 60%;
        width: 100%;
        flex: 1;
        display: flex;
        align-items: center;
        // z-index: 15;
      }
      .home_box_3_header {
        display: flex;
        flex-direction: column;
        /* margin-left: 160rem; */
        /* margin-top: 50rem; */
        position: relative;
        width: 1600rem;
        /* margin: calc(50 / 929 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .home_box_3_list {
        margin: 36rem auto 0;
        display: flex;
        overflow: hidden;
        justify-content: space-between;
        width: 1600rem;
        .list_item {
          width: 506rem;
          height: 600rem;
          margin-right: 41rem;
          .list_img {
            width: 506rem;
            height: 310rem;
            border-radius: 10rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .list_title {
            width: 100%;
            height: 32rem;
            margin-top: 34rem;
            font-size: 24rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .list_content {
            font-size: 18rem;
            font-weight: 400;
            line-height: 25rem;
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            -webkit-line-clamp: 3;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden;
            margin-top: 12rem;
            text-align: justify;
            &:after {
              content: "";
              display: inline-block;
              width: 100%;
            }
          }
          .list_learn {
            width: 151rem;
            height: 30rem;
            font-size: 18rem;
            font-weight: 500;
            letter-spacing: 0rem;
            line-height: 30rem;
            color: rgba(26, 26, 26, 1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 48rem;
            img {
              width: 16rem;
              height: 14rem;
            }
          }
        }
        .list_item:hover {
          cursor: pointer;
          .list_content {
            color: #005ced;
          }
        }
      }
    }
    .home_box_3 :deep(.n-carousel__dots) {
      bottom: -50rem;
    }
    .home_box_3 :deep(.n-carousel.n-carousel--card) {
      .n-carousel__slide.n-carousel__slide--next {
        opacity: 1;
      }
      .n-carousel__slide.n-carousel__slide--prev {
        opacity: 1;
      }
    }
    .home_box_4 {
      /* height: 116vh; */
      height: 100%;
      background-color: #ffffff;
      position: relative;
      display: flex;
      flex-direction: column;
      .home_box_4_content {
        top: 0;
        left: 0;
        height: 65%;
        width: 1600rem;
        margin: 48rem auto 40rem;
        overflow: hidden;
        display: flex;
        .home_box_4_content_left {
          height: 100%;
          min-width: 60%;
          margin-right: 48rem;
          display: flex;
          flex-direction: column;
          .left_img {
            width: 1138rem;
            height: 613rem;
            border-radius: 20rem 0rem 20rem 0rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left_title {
            font-size: 24rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            margin-top: 28rem;
            margin-bottom: 8rem;
          }
          .left_text {
            width: 1138rem;
            font-size: 18rem;
            font-weight: 400;
            line-height: 24rem;
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
        .home_box_4_content_left:hover {
          cursor: pointer;
          .left_text {
            color: #005ced;
          }
        }
        .home_box_4_content_right {
          display: flex;
          flex-direction: column;
          .rightItem {
            display: flex;
            flex-direction: column;
            .right_img {
              width: 413rem;
              height: 222rem;
              margin-bottom: rem;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .right_title {
              font-size: 24rem;
              font-weight: 700;
              line-height: 32rem;
              color: rgba(56, 56, 56, 1);
              width: 402rem;
              height: 32rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin: 28rem 0 8rem;
            }
            .right_text {
              font-size: 18rem;
              font-weight: 400;
              line-height: 24rem;
              color: rgba(91, 91, 91, 1);
              width: 402rem;
              overflow: hidden;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:first-child {
              margin-bottom: 54rem;
            }
          }
          .rightItem:hover {
            cursor: pointer;
            .right_text {
              color: #005ced;
            }
          }
        }
      }
    }
    .home_box_6 {
      display: flex;
      position: relative;
      flex-direction: column;
      height: 100%;
      background: rgba(250, 250, 250, 1);
      .home_box_6_content {
        width: 1600rem;
        margin: 75rem auto 40rem;
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: #005ced;
        }
        .home_box_6_carousel {
          width: 1600rem;
          display: flex;
          margin: 0 auto;
          flex-wrap: wrap;
          position: relative;
          .cLine {
            width: 100%;
            justify-content: center;
            display: flex;
            &:nth-child(2n) {
              transform: translateX(-100rem);
              :nth-child(n + 4) {
                transform: translateX(200rem);
              }
            }
            &:nth-child(3) {
              &:nth-child(-n + 3) {
                transform: translateX(-200rem);
              }
              :nth-child(n + 4) {
                transform: translateX(400rem);
              }
            }
            &:not(:first-child) {
              margin-top: -100rem;
            }
            .cItem {
              width: 200rem;
              height: 200rem;
              clip-path: polygon(50% 5%, 95% 50%, 50% 95%, 5% 50%);
              background: rgba(242, 242, 242, 1);
              display: flex;
              justify-content: center;
              align-items: center;
              img {
                max-width: 45%;
                height: auto;
              }
            }
          }
          .dItem {
            width: 380rem;
            height: 380rem;
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            background: rgba(13, 46, 153, 1);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
    .home_box_7 {
      display: flex;
      position: relative;
      flex-direction: row;
      height: 100%;
      background: rgba(250, 250, 250, 1);
      padding: 6vh;
      justify-content: space-between;
      .left {
        padding-top: 4%;
        width: 48%;
        display: flex;
        flex-direction: column;
        & > .title {
          font-size: 36rem;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          background-color: #04a0ff;
          color: #fff;
          text-align: center;

          margin-left: auto;
          margin-right: auto;
          margin-bottom: 30rem;
          padding: 13rem;
          border-radius: 20rem;
        }
        & > .subTitle {
          padding-left: 25rem;
          text-align: center;
          font-size: 36rem;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          background-color: #04a0ff;
          color: #fff;
          text-align: center;
          width: 230rem;
          border-radius: 20rem;
          .toRightIcon {
            width: 30rem;
            height: 30rem;
            background-image: url("/@/assets/special/toRight.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            float: left;
            margin-top: 14rem;
            margin-left: 10rem;
          }
        }
        & > .context {
          margin-top: 6rem;
          line-height: 28rem;
          font-size: 18rem;
          font-weight: 300;
          :deep(p) {
            font-size: 22rem !important;
            span {
              font-size: 22rem !important;
              text-wrap-mode: wrap !important;
            }
          }
        }
      }
      .right {
        width: 48%;
      }
    }
    .dataTab {
      display: flex;
      flex-direction: row;
      /* width: 1600rem;
    margin: 100rem auto; */
      background: rgba(250, 250, 250, 1);
      width: 100%;
      overflow-x: auto;
      .dataItem {
        position: relative;
        display: flex;
        align-items: center;
        width: 465rem;
        margin: 0 28rem;
        .itemImg {
          width: 150rem;
          height: 150rem;
          overflow: hidden;
          border-radius: 50%;
          box-shadow: 0px 7px 12px rgba(14, 31, 53, 0.08);
          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
        .itemText {
          margin-left: 40rem;
          .itemTitle {
            font-size: 30rem;
            line-height: 1;
            color: #383838;
            padding-bottom: 40rem;
            transition: all 0.2s linear;
          }
          .itemSubtitle {
            font-size: 70rem;
            font-weight: bold;
            line-height: 1;
            color: #383838;
            transition: all 0.2s linear;
            animation: countup linear forwards;
            :deep(.countup-wrap) {
              span {
                font-weight: bold !important;
              }
            }
          }
        }

        /* &::after {
        position: absolute;
        display: block;
        content: "";
        top: 50%;
        right: -2.85rem;
        width: 1px;
        height: 80%;
        transform: translateY(-50%);
        background-color: #e7e7e7;
      } */
      }
    }
    .tablePart {
      margin: 60rem auto 40rem;
      width: calc(100% - 320rem);
      :deep(.n-data-table-th) {
        background: #2342a2;
        color: #ffffff;
        font-size: 24rem;
        font-weight: 500;
        padding: 28rem;
        text-align: center;
      }
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .rightBody {
        .seamless {
          width: 100%;
          height: 575rem;
          overflow: hidden;
        }
      }
    }
    @keyframes countup {
      from {
        content: "0";
      }
      to {
        content: attr(data-count);
      }
    }
  }

  #tsparticles {
    canvas {
      position: absolute !important;
      top: 0;
      left: 0;
    }
  }
  .underline {
    background-color: #005ced;
    height: 2rem;
    width: 17rem;
  }
  .home-wrapper-free {
    position: relative;
    padding-top: calc(99 / 1080 * 100vh);
    width: 100%;
    // min-width: 1600px;
    background: rgba(249, 250, 251, 1);
    overflow: hidden;
  }
  .prod-box {
    position: relative;
    // margin: calc(99 / 1080 * 100vh) auto 0;
    margin: 0 auto;
    width: calc(100% - 320rem);
    & > .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 50rem;
      & > .prod-title-context {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        /* line-height: 96rem; */
      }
      :deep(.n-tabs-nav-scroll-content) {
        border: 0;
      }
      :deep(.n-tabs-tab) {
        font-size: 22rem;
        font-weight: 400;
        letter-spacing: 0rem;
        line-height: 29.17rem;
        color: rgba(115, 115, 115, 1);
      }
      :deep(.n-tabs-tab.n-tabs-tab--active) {
        color: rgba(13, 46, 153, 1) !important;
      }
      :deep(.n-tabs-bar) {
        height: 4rem;
        border-radius: 3rem;
        --n-bar-color: rgba(13, 46, 153, 1);
      }
    }
    & > .subTitle {
      margin-bottom: 26rem;
      /* height: 47rem; */
      font-size: 36rem;
      font-weight: 500;
      letter-spacing: 0rem;
      line-height: 47rem;
      color: rgba(128, 128, 128, 1);
    }

    .prod-main {
      padding-bottom: 32rem;
      width: 100%;
      // min-height: 1000px;

      .empty {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 400rem;
      }
      .prod-item-box {
        display: grid;
        grid-template-columns: repeat(2, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        width: 100%;
        height: 100%;
        .prod-item:hover {
          transform: translate(0, -10rem);
          box-shadow: 0 0.25rem 1.25rem #d4d4d4;
          .prod-show {
            display: flex;
          }
        }
        .prod-item {
          display: flex;
          justify-content: space-between;
          height: 344rem;
          background: rgba(255, 255, 255, 1);
          cursor: pointer;
          transition: All 0.3s ease-in-out;
          position: relative;
          .prod-info {
            padding: 52rem 0 0 53rem;
            width: calc(347 / 790 * 100%);
            height: 100%;
            overflow: hidden;
            .title {
              width: 100%;
              font-size: 36rem;
              font-weight: 700;
              letter-spacing: 0.2rem;
              line-height: 40rem;
              color: rgba(37, 43, 66, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              :deep(span) {
                font-weight: 700 !important;
              }
            }
            .context {
              margin-top: 19rem;
              width: 100%;
              height: 80rem;
              font-size: 18rem;
              font-weight: 400;
              letter-spacing: 0.2rem;
              line-height: 26rem;
              color: rgb(255, 255, 255);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            }
            .prod-tag {
              display: flex;
              justify-content: space-between;
              margin-top: 20rem;
              width: 100%;
              height: 45rem;
              .prod-tag-item {
                width: 140rem;
                height: 45rem;
                line-height: 45rem;
                font-size: 16rem;
                font-weight: 400;
                text-align: center;
                border-radius: 60rem;
                background: rgba(245, 245, 245, 1);
                border: 1rem solid rgba(235, 235, 235, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            .prod-view {
              display: flex;
              align-items: center;
              margin-top: 40rem;
              width: 100%;
              .prod-view-eye {
                margin-right: 17rem;
                width: 32.02rem;
                height: 28.08rem;
              }
              .prod-view-text {
                font-size: 14rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
          .prod-img {
            position: relative;
            width: 337rem;
            height: 100%;
            .prod-arrow-box {
              box-sizing: border-box;
              position: absolute;
              bottom: 24rem;
              // padding: 0 24;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 115.8rem;
              height: 46.13rem;
              opacity: 1;
              border-radius: 60rem;
              background: rgba(13, 46, 153, 1);
              transform: translateX(-50%);
              text-align: center;
              img {
                width: 67.07rem;
                height: 8.02rem;
                object-fit: cover;
              }
            }
            & > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .prod-show {
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: #fff;
            top: 0px;
            left: 0px;
            display: none;
            background-image: url("/src/assets/defaultBagIMG.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;

            justify-content: center;
            flex-direction: column;
            padding: 20px;
            .title {
              width: 100%;
              font-size: 36rem;
              font-weight: 700;
              letter-spacing: 0.2rem;
              line-height: 40rem;
              color: rgb(255, 255, 255);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              :deep(span) {
                font-weight: 700 !important;
              }
            }
            .context {
              margin-top: 19rem;
              width: 100%;
              height: 80rem;
              font-size: 18rem;
              font-weight: 400;
              letter-spacing: 0.2rem;
              line-height: 26rem;
              color: #fff;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            }
          }
        }
      }
    }
    .prod-pag {
      display: flex;
      justify-content: center;
      margin-top: 36px;
      padding-bottom: 32px;
      text-align: center;
      :deep(.n-pagination-item) {
        color: rgba(128, 128, 128, 1);
        --n-item-border-active: 1px solid rgba(128, 128, 128, 1);
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #home {
    @keyframes countup {
      from {
        content: "0";
      }
      to {
        content: attr(data-count);
      }
    }
    font-family: MiSans;
    :deep(.swiper) {
      display: flex;
      overflow: hidden;
      width: 100%;
      .swiper-wrapper {
        display: flex;
      }
    }
    :deep(.swiper-button-prev1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      left: -80rem;
      transform: rotate(180deg);
      background: rgba(237, 237, 237, 0.6);
    }
    :deep(.swiper-button-next1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      right: -80rem;
      left: auto;
      transform: rotate(180deg);
      background: #f6f6f6;
    }
    .swiper-button-prev:after,
    .swiper-button-next:after {
      font-family: "";
    }
    .swiper-button-next:after {
      content: "";
    }
    .swiper-button-prev:after {
      content: "";
    }
    .home_box {
      height: 50vh;
      width: 100%;
      position: relative;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      position: relative;
      .home_title {
        line-height: 96rem;
        color: rgba(56, 56, 56, 1);
        font-size: 50rem !important;
        font-weight: 900;
        letter-spacing: 0rem;
        margin-bottom: 4rem;
      }
      .home_title_1 {
        font-size: 32rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47.74rem;
        color: rgba(128, 128, 128, 1);
      }
      .home_detail {
        font-size: 24rem;
        font-weight: 300;
        letter-spacing: 1rem;
        /* line-height: 24rem; */
        color: rgba(128, 128, 128, 1);
        /* width: 1133rem; */
        margin-top: 22rem;
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: wrap;
      }
      .home_more {
        position: absolute;
        right: 0;
        top: 0;
        /* width: 130rem; */
        height: 28rem;
        font-size: 16rem;
        font-weight: 500;
        line-height: 28rem;
        color: rgba(40, 41, 56, 1);
      }
      .home_box_header {
        display: flex;
        flex-direction: column;
        position: relative;
        /* margin: calc(57 / 1080 * 100%) auto 0; */
        margin: 60rem auto 0;
        width: 720rem;
      }
      .carousel-img {
        width: 100%;
        height: 240rem;
        object-fit: cover;
      }
    }
    img {
      // width: 100%;
    }

    .home_box_1 {
      /* background: url("../../assets/home1.gif"); */
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      .glass {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
      .banner-content {
        /* position: absolute;
          top: calc(108 / 1080 * 100vh); */
        /* width: calc(708 / 1080 * 100vh); */
        font-size: 20rem;
        letter-spacing: 1rem;
        color: rgba(255, 255, 255, 1);
        width: 720rem;
        margin: 150rem auto 0;
        & > .title {
          font-size: 36rem;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .subTitle {
          font-size: 36rem;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .context {
          margin-top: 6rem;
          line-height: 28rem;
          font-size: 18rem;
          font-weight: 300;
          :deep(p) {
            font-size: 22rem !important;
            span {
              font-size: 22rem !important;
            }
          }
        }
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }
      .toDown {
        position: absolute;
        bottom: 7%;
        left: 50%;
        transform: translateX(-50%);
        animation: blink 2s infinite;
      }
      .home_box_1_carousel {
        height: 100%;
        position: relative;
        .home_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
    }
    .home_box_2 {
      position: relative;
      /* height: 110vh; */
      height: 95vh;

      .home_boex_2_bg {
        width: 139rem;
        height: 95rem;
        left: calc(59 / 1903 * 100%);
        top: calc(813 / 1080 * 116vh);
        position: absolute;
        background: url(../../assets//home/<USER>
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
      }
      .home_box_2_top {
        display: flex;
        flex-direction: column;
        /* margin-left: 160rem; */
        /* margin-top: 100rem; */
        width: 720rem;
        /* margin: calc(100 / 1080 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .content {
        z-index: 1;
        display: flex;
        margin: 30rem auto 40rem;
        width: 720rem;
        position: relative;
        .home_box_2_card {
          width: calc(360 / 780 * 100%) !important;
          height: 100%;
          /* margin-right: 40rem; */
          .home_box_2_item {
            height: 60vh;
            width: 100%;
            position: relative;
            overflow: hidden;
            transition: transform 1s;
            img {
              width: 100%;
              height: 100%;
              object-fit: fill;
              transition: transform 1s;
            }
            .home_box_2_text {
              position: absolute;
              top: calc((100% - 464rem) / 2);
              width: 100%;
              height: 464rem;
              /* opacity: 0.8; */
              // background: linear-gradient(
              //   0deg,
              //   rgba(13, 46, 153, 0.8) 0%,
              //   rgba(13, 46, 153, 0) 100%
              // );
              .title {
                margin-top: 125rem;
                line-height: 47.74rem;
                color: #ffffff;
                font-size: 36rem;
                font-weight: 700;
                text-align: center;
                vertical-align: top;
              }
              .fTitle {
                opacity: 1;
                font-size: 14rem;
                font-weight: 300;
                letter-spacing: 2rem;
                line-height: 19rem;
                color: #ffffff;
                text-align: center;
                vertical-align: top;
              }
              .Hbar {
                width: 80rem;
                height: 8rem;
                background: #ffffff;
                margin: 16rem auto 0;
              }
              .con {
                width: 300rem;
                height: 190rem;
                font-size: 18rem;
                font-weight: 300;
                /* line-height: 18rem; */
                color: #ffffff;
                text-align: left;
                vertical-align: top;
                margin: 8rem auto 0;
                -webkit-line-clamp: 5;
                text-overflow: ellipsis;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
              }
              .Hbottom {
                width: 104rem;
                height: 54rem;
                border-radius: 60rem;
                border: 1rem solid #ffffff;
                margin: 20rem auto 0;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }
            img {
              height: 100%;
              width: 100%;
            }
          }
          &:nth-child(n + 1) {
            margin-right: 30rem;
          }
        }

        .home_box_2_card:hover {
          cursor: pointer;
          border-radius: 14px;
          overflow: hidden;
          img {
            transform: scale(1.5); /* 放大图片两倍 */
          }
        }

        .ownSwiper {
          display: flex;
          flex-direction: row;
          justify-content: center;
          width: 100%;
          margin-top: -3rem;
          .swiperBtn {
            border: 0;
            box-shadow: 0 0.25rem 0.75rem rgba(30, 34, 40, 0.02);
            width: 2.2rem;
            height: 2.2rem;
            line-height: inherit;
            border-radius: 100%;
            text-shadow: none;
            transition: all 0.2s ease-in-out;
            background: rgba(var(--bs-primary-rgb), 0.9) !important;
            margin: 0 0.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .btn-disabled {
            background: rgba(var(--bs-primary-rgb), 0.7) !important;
            opacity: 0.35;
            cursor: auto;
            pointer-events: none;
          }
          .button-prev:after {
            content: "\e949";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
          .button-next:after {
            content: "\e94c";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
        }
      }
    }
    .home_box_3 {
      position: relative;
      background-color: rgba(245, 245, 245, 1);
      display: flex;
      flex-direction: column;
      /* height: 110vh; */
      height: 100%;
      // align-items: center;
      .carousel {
        // height: 60%;
        width: 100%;
        flex: 1;
        display: flex;
        align-items: center;
        // z-index: 15;
      }
      .home_box_3_header {
        display: flex;
        flex-direction: column;
        /* margin-left: 160rem; */
        /* margin-top: 50rem; */
        position: relative;
        width: 720rem;
        /* margin: calc(50 / 929 * 116vh) auto 0; */
        margin: 60rem auto 0;
      }
      .home_box_3_list {
        margin: 36rem auto 40rem;
        display: flex;
        overflow: hidden;
        flex-direction: column;
        width: 720rem;
        align-items: center;
        .list_item {
          width: 700rem;
          margin-top: 20rem;
          /* height: 600rem; */
          .list_img {
            width: 700rem;
            height: 429rem;
            border-radius: 10rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .list_title {
            width: 100%;
            height: 32rem;
            margin-top: 34rem;
            font-size: 28rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .list_content {
            font-size: 24rem;
            font-weight: 400;
            /* line-height: 25rem; */
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            -webkit-line-clamp: 3;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            overflow: hidden;
            margin-top: 12rem;
            text-align: justify;
            &:after {
              content: "";
              display: inline-block;
              width: 100%;
            }
          }
          .list_learn {
            width: 151rem;
            height: 30rem;
            font-size: 18rem;
            font-weight: 500;
            letter-spacing: 0rem;
            line-height: 30rem;
            color: rgba(26, 26, 26, 1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 48rem;
            img {
              width: 16rem;
              height: 14rem;
            }
          }
        }
        .list_item:hover {
          cursor: pointer;
          .list_content {
            color: #005ced;
          }
        }
      }
    }
    .home_box_3 :deep(.n-carousel__dots) {
      bottom: -50rem;
    }
    .home_box_3 :deep(.n-carousel.n-carousel--card) {
      .n-carousel__slide.n-carousel__slide--next {
        opacity: 1;
      }
      .n-carousel__slide.n-carousel__slide--prev {
        opacity: 1;
      }
    }
    .home_box_4 {
      /* height: 116vh; */
      height: 100%;
      background-color: #ffffff;
      position: relative;
      display: flex;
      flex-direction: column;
      .home_box_4_content {
        height: 65%;
        width: 720rem;
        margin: 48rem auto 40rem;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .home_box_4_content_left {
          height: 100%;
          width: 100%;
          margin-right: 48rem;
          display: flex;
          flex-direction: column;
          .left_img {
            width: 100%;
            height: 307rem;
            border-radius: 20rem 0rem 20rem 0rem;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .left_title {
            font-size: 28rem;
            font-weight: 700;
            line-height: 32rem;
            color: rgba(56, 56, 56, 1);
            text-align: left;
            vertical-align: top;
            margin-top: 28rem;
            margin-bottom: 8rem;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
          }
          .left_text {
            /* width: 1138rem; */
            font-size: 24rem;
            font-weight: 400;
            /* line-height: 24rem; */
            color: rgba(91, 91, 91, 1);
            text-align: left;
            vertical-align: top;
            overflow: hidden;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            display: -webkit-box;
          }
        }
        .home_box_4_content_left:hover {
          cursor: pointer;
          .left_text {
            color: #005ced;
          }
        }
        .home_box_4_content_right {
          display: flex;
          flex-direction: column;
          .rightItem {
            display: flex;
            flex-direction: column;
            margin-top: 20rem;
            .right_img {
              width: 100%;
              height: 387rem;
              margin-bottom: 16rem;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .right_title {
              font-size: 28rem;
              font-weight: 700;
              line-height: 32rem;
              color: rgba(56, 56, 56, 1);
              /* width: 402rem; */
              height: 32rem;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 10rem;
            }
            .right_text {
              font-size: 24rem;
              font-weight: 400;
              color: rgba(91, 91, 91, 1);
              overflow: hidden;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
            }
            &:first-child {
              margin-bottom: 14rem;
            }
          }
          .rightItem:hover {
            cursor: pointer;
            .right_text {
              color: #005ced;
            }
          }
        }
      }
    }
    .home_box_6 {
      display: flex;
      position: relative;
      flex-direction: column;
      height: 100%;
      background: rgba(250, 250, 250, 1);
      .home_box_6_content {
        width: 720rem;
        margin: 75rem auto 40rem;
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: #005ced;
        }
        .home_box_6_carousel {
          width: 720rem;
          display: flex;
          margin: 0 auto;
          flex-wrap: wrap;
          position: relative;
          .cLine {
            width: 100%;
            justify-content: center;
            display: flex;
            &:nth-child(2n) {
              transform: translateX(-45rem);
              :nth-child(n + 4) {
                transform: translateX(90rem);
              }
            }
            &:nth-child(3) {
              &:nth-child(-n + 3) {
                transform: translateX(-90rem);
              }
              :nth-child(n + 4) {
                transform: translateX(180rem);
              }
            }
            &:not(:first-child) {
              margin-top: -45rem;
            }
            .cItem {
              width: 90rem;
              height: 90rem;
              clip-path: polygon(50% 5%, 95% 50%, 50% 95%, 5% 50%);
              background: rgba(242, 242, 242, 1);
              display: flex;
              justify-content: center;
              align-items: center;
              img {
                max-width: 45%;
                height: auto;
              }
            }
          }
          .dItem {
            width: 171rem;
            height: 171rem;
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            background: rgba(13, 46, 153, 1);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
    .home_box_7 {
      display: flex;
      position: relative;
      flex-direction: columns;
      height: 100%;
      background: rgba(250, 250, 250, 1);
      padding: 6vh;
      justify-content: space-between;
      .left {
        padding-top: 4%;
        width: 98%;
        display: flex;
        flex-direction: column;
        padding-bottom: 1%;
        & > .title {
          font-size: 36rem;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          background-color: #04a0ff;
          color: #fff;
          text-align: center;

          margin-left: auto;
          margin-right: auto;
          margin-bottom: 30rem;
          padding: 13rem;
          border-radius: 20rem;
        }
        & > .subTitle {
          padding-left: 25rem;
          text-align: center;
          font-size: 36rem;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          background-color: #04a0ff;
          color: #fff;
          text-align: center;
          width: 230rem;
          border-radius: 20rem;
          .toRightIcon {
            width: 30rem;
            height: 30rem;
            background-image: url("/@/assets/special/toRight.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            float: left;
            margin-top: 14rem;
            margin-left: 10rem;
          }
        }
        & > .context {
          margin-top: 6rem;
          line-height: 28rem;
          font-size: 18rem;
          font-weight: 300;
          :deep(p) {
            font-size: 22rem !important;
            span {
              font-size: 22rem !important;
              text-wrap-mode: wrap !important;
            }
          }
        }
      }
      .right {
        width: 98%;
      }
    }
    .dataTab {
      display: flex;
      flex-direction: row;
      /* width: 1600rem;
      margin: 100rem auto; */
      background: rgba(250, 250, 250, 1);
      .dataItem {
        position: relative;
        display: flex;
        align-items: center;
        flex: 1;
        margin: 0 28rem;
        .itemImg {
          width: 150rem;
          height: 150rem;
          overflow: hidden;
          border-radius: 50%;
          box-shadow: 0px 7px 12px rgba(14, 31, 53, 0.08);
          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
        .itemText {
          margin-left: 40rem;
          .itemTitle {
            font-size: 30rem;
            line-height: 1;
            color: #383838;
            padding-bottom: 40rem;
            transition: all 0.2s linear;
          }
          .itemSubtitle {
            font-size: 70rem;
            font-weight: bold;
            line-height: 1;
            color: #383838;
            transition: all 0.2s linear;
            animation: countup linear forwards;
            :deep(.countup-wrap) {
              span {
                font-weight: bold !important;
              }
            }
          }
        }

        
      }
    }
  }

  #tsparticles {
    canvas {
      position: absolute !important;
      top: 0;
      left: 0;
    }
  }
  .underline {
    background-color: #005ced;
    height: 2rem;
    width: 17rem;
  }
  .home-wrapper-free {
    position: relative;
    padding-top: 60rem;
    width: 100%;
    background: rgba(249, 250, 251, 1);
    overflow: hidden;
  }
  .prod-box {
    position: relative;
    // margin: calc(99 / 1080 * 100vh) auto 0;
    margin: 0 auto;
    width: 720rem;
    & > .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      & > .prod-title-context {
        font-size: 72rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 96rem;
      }
      :deep(.n-tabs-nav-scroll-content) {
        border: 0;
      }
      :deep(.n-tabs-tab) {
        font-size: 22rem;
        font-weight: 400;
        letter-spacing: 0rem;
        line-height: 29.17rem;
        color: rgba(115, 115, 115, 1);
      }
      :deep(.n-tabs-tab.n-tabs-tab--active) {
        color: rgba(13, 46, 153, 1) !important;
      }
      :deep(.n-tabs-bar) {
        height: 4rem;
        border-radius: 3rem;
        --n-bar-color: rgba(13, 46, 153, 1);
      }
    }
    & > .subTitle {
      margin-bottom: 26rem;
      height: 47rem;
      font-size: 36rem;
      font-weight: 500;
      letter-spacing: 0rem;
      line-height: 47rem;
      color: rgba(128, 128, 128, 1);
    }

    .prod-main {
      padding-bottom: 32rem;
      width: 100%;
      // min-height: 1000px;

      .empty {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 400rem;
      }
      .prod-item-box {
        display: grid;
        grid-template-columns: repeat(1, minmax(0rem, 1fr));
        gap: 20rem;
        justify-content: center;
        width: 100%;
        height: 100%;
        .prod-item:hover {
          transform: translate(0, -10rem);
          box-shadow: 0 0.25rem 1.25rem #d4d4d4;
          .prod-show {
            display: flex;
          }
        }
        .prod-item {
          position: relative;
          display: flex;
          justify-content: space-between;
          height: 344rem;
          background: rgba(255, 255, 255, 1);
          cursor: pointer;
          transition: All 0.3s ease-in-out;
          .prod-info {
            padding: 52rem 0 0 53rem;
            width: calc(347 / 790 * 100%);
            height: 100%;
            overflow: hidden;
            .title {
              width: 100%;
              font-size: 36rem;
              font-weight: 700;
              letter-spacing: 0.2rem;
              line-height: 40rem;
              color: rgba(37, 43, 66, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .context {
              margin-top: 19rem;
              width: 100%;
              height: 80rem;
              font-size: 24rem;
              font-weight: 400;
              letter-spacing: 0.2rem;
              /* line-height: 20rem; */
              color: rgba(85, 85, 85, 1);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            .prod-tag {
              display: flex;
              justify-content: space-evenly;
              margin-top: 20rem;
              width: 100%;
              height: 45rem;
              .prod-tag-item {
                width: 120rem;
                height: 45rem;
                line-height: 45rem;
                font-size: 16rem;
                font-weight: 400;
                text-align: center;
                border-radius: 60rem;
                background: rgba(245, 245, 245, 1);
                border: 1rem solid rgba(235, 235, 235, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            .prod-view {
              display: flex;
              align-items: center;
              margin-top: 40rem;
              width: 100%;
              .prod-view-eye {
                margin-right: 17rem;
                width: 32.02rem;
                height: 28.08rem;
              }
              .prod-view-text {
                font-size: 14rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
          .prod-img {
            position: relative;
            width: 337rem;
            height: 100%;
            .prod-arrow-box {
              box-sizing: border-box;
              position: absolute;
              bottom: 24rem;
              // padding: 0 24;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 115.8rem;
              height: 46.13rem;
              opacity: 1;
              border-radius: 60rem;
              background: rgba(13, 46, 153, 1);
              transform: translateX(-50%);
              text-align: center;
              img {
                width: 67.07rem;
                height: 8.02rem;
                object-fit: cover;
              }
            }
            & > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .prod-show {
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: #fff;
            top: 0px;
            left: 0px;
            display: none;
            background-image: url("/src/assets/defaultBagIMG.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            justify-content: center;
            flex-direction: column;
            padding: 10px;
            .title {
              width: 100%;
              font-size: 36rem;
              font-weight: 700;
              letter-spacing: 0.2rem;
              line-height: 40rem;
              color: rgba(37, 43, 66, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .context {
              margin-top: 19rem;
              width: 100%;
              height: 80rem;
              font-size: 24rem;
              font-weight: 400;
              letter-spacing: 0.2rem;
              /* line-height: 20rem; */
              color: #fff;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              span {
                text-wrap-mode: wrap !important;
              }
            }
          }
        }
      }
    }
    .prod-pag {
      display: flex;
      justify-content: center;
      margin-top: 36rem;
      padding-bottom: 40rem;
      text-align: center;
      :deep(.n-pagination-item) {
        color: rgba(128, 128, 128, 1);
        --n-item-border-active: 1px solid rgba(128, 128, 128, 1);
      }
    }
  }
}
.home_box_2 {
  background-image: linear-gradient(318deg, rgba(255, 0, 0, 0), #129eff);
}
.customer-service {
  width: 80rem;
  height: 80rem;
  background: #ffffff;
  border-radius: 50%;
  position: fixed;
  right: 100rem;
  bottom: 100rem;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 80%;
  }
}
</style>
