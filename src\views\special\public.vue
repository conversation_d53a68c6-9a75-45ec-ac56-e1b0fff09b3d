<template>
  <div id="home">
    <div class="home-wrapper" style="padding: 0">
      <n-carousel
        dot-type="line"
        autoplay
        :draggable="bannerList.list && bannerList.list.length > 1"
      >
        <template v-for="(item, index) in bannerList.list">
          <template v-if="item.type == '2'">
            <video
              :src="item.url"
              autoplay
              muted
              loop
              style="width: 100%; height: 100%"
              :poster="apiUrl + coverImg"
            ></video>
          </template>
          <template v-else-if="item.type == '1'">
            <img class="carousel-img" :src="item.url" />
          </template>
        </template>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              @click="to(index - 1)"
            />
          </ul>
        </template>
      </n-carousel>
      <div class="glass">
        <div class="banner-content">
          <div class="title">{{ titleContext.title }}</div>
          <div class="subTitle">{{ titleContext.subTitle }}</div>
          <div class="context" v-html="titleContext.context"></div>
        </div>
        <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div>
      </div>
    </div>
    <div class="dataTab">
      <div
        style="width: 1600rem; margin: 60rem auto; display: flex"
        v-if="!isMobile"
      >
        <div class="dataItem" v-for="item in dataTabList">
          <div class="itemImg">
            <img :src="apiUrl + item.imageUrl" alt="" />
          </div>
          <div class="itemText">
            <div class="itemTitle">{{ item.TITLE }}</div>
            <div
              class="itemSubtitle"
              ref="itemSubtitle"
              :data-count="item.INTRO"
            >
              <CountUp
                :end-val="item.INTRO"
                :duration="2.5"
                :decimal-places="0"
                :delay="2"
                @init="onInit"
                @finished="onFinished"
              ></CountUp>
            </div>
          </div>
        </div>
      </div>
      <div style="margin: 60rem auto; display: flex" v-else>
        <div class="dataItem" v-for="item in dataTabList">
          <div class="itemImg">
            <img :src="apiUrl + item.imageUrl" alt="" />
          </div>
          <div class="itemText">
            <div class="itemTitle">{{ item.TITLE }}</div>
            <div
              class="itemSubtitle"
              ref="itemSubtitle"
              :data-count="item.INTRO"
            >
              <CountUp
                :end-val="item.INTRO"
                :duration="2.5"
                :decimal-places="0"
                :delay="2"
                @init="onInit"
                @finished="onFinished"
              ></CountUp>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="publicCom">
      <div
        class="home_box_2_top"
        style="flex-direction: column; align-items: start"
      >
        <div class="home_title">应用成果展示</div>
        <div class="home_detail">
          依托公共数据专区，成功为省内多家银行打造“智慧快贷”、“榕易贷”、“信秒贷”等多个金融应用场景，<br />赋能银行授信近
          <span style="font-weight: 700">50亿元</span
          >。同时，专区还在社保就业、园区运营、职业背调等场景广泛应用。
        </div>
        <!-- <div
          class="home_more dynamicMore"
          @click="goPage('https://trade.fjbdex.com/ltywpt/data-module', true)"
        >
          More ——
        </div> -->
      </div>
      <div class="content" v-if="!isMobile">
        <div class="swiper-button-prev swiper-button-next1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-prev1">
          <img :src="arrowR" />
        </div>
        <swiper
          :slides-per-view="5"
          :autoplay="{
            delay: 1000,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :loop="true"
          :slidesPerGroup="1"
          :loopedSlides="5"
          :spaceBetween="36"
          class="swiperRef"
          :modules="modules"
        >
          <swiper-slide class="home_box_2_card" v-for="item in home_new_list">
            <div class="home_box_2_item">
              <div class="home_box_img" style="background: #fff">
                <img
                  :src="item.imageUrl"
                  alt=""
                  style="width: 100%; height: 100%"
                />
              </div>
              <div class="title">{{ item.TITLE }}</div>
              <div class="con">
                <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                  <template #trigger>
                    {{ unescapeHTML(item.INTRO) }}
                  </template>
                  {{ unescapeHTML(item.INTRO) }}
                </n-tooltip>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="content" v-else>
        <swiper
          :slides-per-view="2"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :slidesPerGroup="2"
          :spaceBetween="36"
          class="swiperRef"
          :modules="modules"
        >
          <swiper-slide class="home_box_2_card" v-for="item in home_new_list">
            <div class="home_box_2_item">
              <div class="home_box_img" style="background: #fff">
                <img
                  :src="item.imageUrl"
                  alt=""
                  style="width: 100%; height: 100%"
                />
              </div>
              <div class="title">{{ item.TITLE }}</div>
              <div class="con">
                <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                  <template #trigger>
                    {{ unescapeHTML(item.INTRO) }}
                  </template>
                  {{ unescapeHTML(item.INTRO) }}
                </n-tooltip>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <div class="twoPart">
      <div class="leftPart">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div class="title">
            {{ title[1].title }}
          </div>
          <div
            class="home_more dynamicMore"
            @click="
              goPage(
                'https://trade.fjbdex.com/ltywpt/standardized-products',
                true
              )
            "
          >
            More ——
          </div>
        </div>
        <div class="leftBody">
          <div
            class="leftItem"
            v-for="item in leftPartList"
            @click="goPage(item.KIPURL, true)"
          >
            <img
              :src="apiUrl + item.imageUrl"
              alt=""
              style="position: absolute"
            />
            <div class="itemName">
              <n-tooltip trigger="hover" :style="{ maxWidth: '350rem' }">
                <template #trigger>
                  {{ item.TITLE }}
                </template>
                {{ item.INTRO }}
              </n-tooltip>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="rightPart">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div class="title" style="width: auto">
            {{ title[2].title }}
          </div>
          <div
            class="home_more dynamicMore"
            @click="
              goPage('https://trade.fjbdex.com/ltywpt/data-catalog', true)
            "
          >
            More ——
          </div>
        </div>
        <div class="rightBody">
          <n-data-table
            :data="tableData"
            :columns="columns"
            size="large"
            ref="tableRef"
            max-height="465rem"
            style="margin-top: 28rem; height: 575rem"
          />
        </div>
      </div> -->
    </div>
    <div class="publicCom">
      <div class="home_box_2_top">
        <div class="home_title">
          {{ title[0].title }}
        </div>
        <div
          class="home_more dynamicMore"
          @click="goPage('https://trade.fjbdex.com/ltywpt/data-module', true)"
        >
          More ——
        </div>
      </div>
      <div class="content" v-if="!isMobile">
        <div class="swiper-button-prev swiper-button-next1">
          <img :src="arrowL" />
        </div>
        <div class="swiper-button-next swiper-button-prev1">
          <img :src="arrowR" />
        </div>
        <swiper
          :slides-per-view="5"
          :autoplay="{
            delay: 500,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :loop="true"
          :slidesPerGroup="1"
          :spaceBetween="36"
          :direction="'horizontal'"
          class="swiperRef"
          :modules="modules"
        >
          <swiper-slide class="home_box_2_card" v-for="item in home_2_list">
            <div class="home_box_2_item">
              <div class="home_box_img">
                <img :src="apiUrl + item.imageUrl" alt="" />
              </div>
              <div class="title">{{ item.TITLE }}</div>
              <div class="con">
                <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                  <template #trigger>
                    {{ unescapeHTML(item.INTRO) }}
                  </template>
                  {{ unescapeHTML(item.INTRO) }}
                </n-tooltip>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="content" v-else>
        <swiper
          :slides-per-view="2"
          :autoplay="{
            delay: 5000,
            disableOnInteraction: true,
          }"
          :navigation="{
            nextEl: '.swiper-button-next1',
            prevEl: '.swiper-button-prev1',
          }"
          :slidesPerGroup="2"
          :spaceBetween="36"
          class="swiperRef"
          :modules="modules"
        >
          <swiper-slide class="home_box_2_card" v-for="item in home_2_list">
            <div class="home_box_2_item">
              <div class="home_box_img">
                <img :src="apiUrl + item.imageUrl" alt="" />
              </div>
              <div class="title">{{ item.TITLE }}</div>
              <div class="con">
                <n-tooltip trigger="hover" :style="{ maxWidth: '300rem' }">
                  <template #trigger>
                    {{ unescapeHTML(item.INTRO) }}
                  </template>
                  {{ unescapeHTML(item.INTRO) }}
                </n-tooltip>
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <div class="home-wrapper" style="height: 100%; padding-bottom: 40rem">
      <div class="advantage-box">
        <div class="dotMatrix"></div>
        <div class="title">
          {{ title[3].title }}
        </div>
        <div class="subTitle">
          {{ title[3].subTitle }}
        </div>
        <div class="main">
          <div class="advantage-swiper">
            <swiper
              @swiper="setSwiper"
              @slideChange="onSlideChange"
              :direction="'vertical'"
              :spaceBetween="10"
              :autoplay="{
                delay: 3000,
                disableOnInteraction: false,
              }"
              :modules="modules"
              class="mySwiper"
            >
              <swiper-slide v-for="(item, index) in adList">
                <img :src="item.url" class="ad-swiper-img" />
                <div class="ad-glass">
                  <img
                    :src="item.icon"
                    class="ad-glass-icon"
                    v-if="!isMobile"
                  />
                  <div class="title">{{ item.title }}</div>
                  <div class="sub-title">
                    {{ item.subTitle }}
                  </div>
                  <div class="sub-context" v-html="item.text"></div>
                </div>
              </swiper-slide>
            </swiper>
          </div>
          <div class="advantage-select">
            <div
              class="advantage-select-item"
              :class="item.active == true ? 'active' : ''"
              v-for="(item, index) in adList"
              @click="changeAdSwiper(index)"
            >
              <img :src="item.url" />
              <div class="ad-select-glass">
                <div>
                  <div class="ad-select-icon">
                    <img :src="item.icon" />
                  </div>
                  <div class="ad-select-text">{{ item.title }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home-wrapper bg-gray">
      <div class="case-box">
        <div class="dotMatrix"></div>
        <div class="dotMatrix"></div>
        <div class="title">
          {{ title[4].title }}
        </div>
        <div class="subTitle">
          {{ title[4].subTitle }}
        </div>
        <div class="main">
          <div class="side-menu 22222">
            <div
              class="side-menu-item"
              :class="item.active == true ? 'active' : ''"
              v-for="(item, index) in caseList"
              @click="handleCase(item)"
            >
              <img :src="item.logo" />
              <span
                style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
                >{{ item.title }}</span
              >
            </div>
          </div>
          <!-- <swiper
            @swiper="setSwiper"
            @slideChange="onCaseSlideChange"
            :direction="'vertical'"
            :spaceBetween="10"
            :autoplay="{
              delay: 3000,
              disableOnInteraction: false,
            }"
            :modules="modules"
            class="mySwiper"
          >
            <swiper-slide v-for="(item, index) in caseList"> -->
          <div class="board">
            <img :src="selectedCase.image" alt="" />
            <div class="case-glass">
              <div class="case-glass-content">
                <!-- <div class="title">{{ item.subTitle }}</div> -->
                <div class="title">{{ selectedCase.title }}</div>
                <div class="sub-context">
                  <!-- <div class="sub-context-title">
                    {{ item.title }}
                  </div>
                  <div class="sub-context-middle">
                    {{ item.subTitle }}
                  </div> -->
                  <div
                    class="sub-context-context"
                    v-html="selectedCase.detail"
                  ></div>
                </div>
              </div>
            </div>
          </div>
          <!-- </swiper-slide>
          </swiper> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  reactive,
  computed,
  ref,
  onMounted,
  toRefs,
  nextTick,
  defineComponent,
  h,
  onBeforeUnmount,
} from "vue";
import {
  getZoneCarousel,
  getDataApplication,
  getProductTypeList,
  getBrandType,
  cooperationConsultation,
  getModuleConfig,
  getDataDirectory,
} from "/@/api/special/traffic";
import { getAdvantage } from "/@/api/digtal/index";
import arrowL from "/@/assets/special/arrow-l.png";
import arrowR from "/@/assets/special/arrow-r.png";
import prodArrow from "/@/assets/special/prod-arrow.png";
import { useRouter } from "vue-router";
const router = useRouter();
import { getFileType } from "/@/utils/file";
import CountUp from "vue-countup-v3";
import type { ICountUp, CountUpOptions } from "vue-countup-v3";
import { NTooltip } from "naive-ui";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay, Navigation } from "swiper/modules";
import "swiper/css/navigation";
// Import Swiper styles
import "swiper/css";
// import required modules
const modules = [Autoplay, Navigation];
const useSwiper = ref(null);
const setSwiper = (swiper) => {
  useSwiper.value = swiper;
};
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const onSlideChange = (swiper) => {
  state.adList.forEach((item) => {
    item.active = false;
  });
  // console.log("onSlideChange", swiper, swiper.realIndex);
  state.adList[swiper.realIndex].active = true;
};
const onCaseSlideChange = (swiper) => {
  caseList.value.forEach((item) => {
    item.active = false;
  });
  // console.log("onSlideChange", swiper, swiper.realIndex);
  caseList.value[swiper.realIndex].active = true;
};
import { useGlobSetting } from "/@/hooks/setting";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const tabsInstRef = ref(null);
const state = reactive({
  classId: {
    topBanner: 11157, //顶部轮播
    menuProdList: 11165, //产品分类目录
    prodList: 11165, //产品
  },
  title: [
    {
      title: "",
    },
    {
      title: "",
    },
    {
      title: "",
    },
    {
      title: "",
    },
    {
      title: "",
    },
  ],
  bannerList: {
    list: [],
    img: [],
    video: [],
  },
  titleContext: {
    title: "",
    subTitle: "",
    context: "",
  },
  caseList: [
    {
      title: "数字货运(网络货运平台)",
      subTitle: "Manufacturing visualization",
      logo: new URL(`/@/assets/special/icon1.png`, import.meta.url).href,
      image: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
      detail:
        "本专区基于国家信用大数据中心、福建省大数据集团有限公司、交通部、工信部等部委的综合监管交通大数据，融合了全国车辆主要汽车制造厂商、高速公路通行等多维度国有大数据资源搭建而成的“交通数据专区”。专区为各企业在供应链过程可视化、运输、保险、金融等领域结合实际应用场景进行综合模型设计，发挥交通数据要素X的应用，精心打造针对不同细分领域的产品及解决方案。",
      active: true,
      contentId: 1,
    },
  ],
  selectedCase: {
    title: "数字货运(网络货运平台)",
    subTitle: "Manufacturing visualization",
    logo: new URL(`/@/assets/special/icon1.png`, import.meta.url).href,
    image: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
    detail:
      "本专区基于国家信用大数据中心、福建省大数据集团有限公司、交通部、工信部等部委的综合监管交通大数据，融合了全国车辆主要汽车制造厂商、高速公路通行等多维度国有大数据资源搭建而成的“交通数据专区”。专区为各企业在供应链过程可视化、运输、保险、金融等领域结合实际应用场景进行综合模型设计，发挥交通数据要素X的应用，精心打造针对不同细分领域的产品及解决方案。",
    active: true,
    id: 1,
  },
  adList: [
    {
      text: "交通部数据源，数据合规，数源稳定，全量数据，多场景设计应用。依托网络货运信用大数据联合实验室，已接入交通部运政类数据，主机厂数据。覆盖全国轻中重卡，新能源车辆数近900万辆。",
      icon: new URL(`/@/assets/special/adicon1.png`, import.meta.url).href,
      url: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
      active: true,
      title: "Data advantages",
      subTitle: "专业模型体系",
      id: 1,
    },
    {
      text: "交通部数据源，数据合规，数源稳定，全量数据，多场景设计应用。依托网络货运信用大数据联合实验室，已接入交通部运政类数据，主机厂数据。覆盖全国轻中重卡，新能源车辆数近900万辆。",
      icon: new URL(`/@/assets/special/adicon1.png`, import.meta.url).href,
      url: new URL(`/@/assets/special/banner1.png`, import.meta.url).href,
      active: false,
      title: "Data advantages",
      subTitle: "服务对象广泛",
      id: 2,
    },
  ],
  prodMenuList: [],
  prodList: {
    pageSize: 6,
    pageNum: 1,
    totalPage: 1,
    type: null,
    prod: [],
  },
});
const {
  classId,
  title,
  bannerList,
  titleContext,
  caseList,
  selectedCase,
  adList,
  prodMenuList,
  prodList,
} = toRefs(state);
const tabKey = ref("0");
const dataTabList = ref([]);
const home_2_list = ref([
  {
    TITLE: "惠民就医",
    INTRO:
      "联合福建省各地医保、各家医院重塑医疗付费流程，通过医保数据的开发利用，构建信用就医风控模型，为福建省医保参保市民提供“先就医、后付费”的就医体验。",
    imageUrl: new URL(`/@/assets/public/pc8.png`, import.meta.url).href,
  },
]);
const leftPartList = ref([]);
const rightPartList = ref([]);
const columns = [
  {
    title: "数据目录",
    key: "DATADIRECTORY",
  },
  {
    title: "简介",
    key: "INTRO",
    ellipsis: {
      tooltip: {
        scorllable: true,
        contentStyle: {
          maxWidth: "200rem",
        },
      },
    },
  },
  {
    title: "子主题数",
    key: "NUM",
  },
];
const tableData = ref([]);
const itemSubtitle = ref(null);
const tableRef = ref(null);
const interval = ref(null);
const scrollTopNum = ref(0);
const coverImg = ref("");
const home_new_list = ref([
  {
    TITLE: "榕易贷",
    INTRO:
      "“榕易贷”场景以社保、公积金、不动产等政务数据为切入点，通过政务数据和行内数据对客户进行准入和授信，同时结合线下收集的客户信息，更有针对性地分层分类服务客户，最终，客户可通过手机银行等线上渠道进行贷款申请、合同签订、线上支取及还款。",
    imageUrl: new URL(`/@/assets/public/pc3.png`, import.meta.url).href,
  },
  {
    TITLE: "园区访客核验",
    INTRO:
      "为解决涉密园区的访客管理难题，利用社保、公积金、司法等公共数据，打造访客核验系统，对申请到访园区的人员进行先行核验，以满足涉密园区的保密需求。",
    imageUrl: new URL(`/@/assets/public/pc11.png`, import.meta.url).href,
  },
  {
    TITLE: "惠民就医",
    INTRO:
      "联合福建省各地医保、各家医院重塑医疗付费流程，通过医保数据的开发利用，构建信用就医风控模型，为福建省医保参保市民提供“先就医、后付费”的就医体验。",
    imageUrl: new URL(`/@/assets/public/pc8.png`, import.meta.url).href,
  },
  {
    TITLE: "招聘背调",
    INTRO:
      "以岗位精准匹配、降低招聘风险为目的， 通过挖掘福建省公共数据资源，对求职者进行职业背景信息核实及就业风险评估，帮助企业定期开展员工尽调、预警等。",
    imageUrl: new URL(`/@/assets/public/pc1.png`, import.meta.url).href,
  },
  {
    TITLE: "企业污染防治大数据应用",
    INTRO:
      "充分发掘企业排污数据、应急管控计划信息等公共数据和电力数据的碰撞融合价值，通过构建企业信用评价、排污治污异常预警等深度分析模型，实现企业污染防治。",
    imageUrl: new URL(`/@/assets/public/pc4.png`, import.meta.url).href,
  },
  {
    TITLE: "社保快贷",
    INTRO:
      "以社保信息为切入点，推出面向社保客户的小额信用贷款产品“社保快贷”，在贷款申请环节充分利用社保、公积金、失信被执行人等政务数据和对客户进行准入和授信，切实让县域客户、农村客户享受“方便、快捷、高效”的普惠金融服务。",
    imageUrl: new URL(`/@/assets/public/pc9.png`, import.meta.url).href,
  },
  {
    TITLE: "茶企入驻核验",
    INTRO:
      "利用统一社会信用号码、企业名称等公共数据，核验茶企（企业、个体工商户）、茶农基本信息的真实性，为商户提供便捷的入驻方式，提升用户体验。",
    imageUrl: new URL(`/@/assets/public/pc2.png`, import.meta.url).href,
  },
  {
    TITLE: "电子分离式投标保函项目",
    INTRO:
      "在建筑工程招标领域，利用失信人信息、企业资质信息、行政处罚信息等公共数据构建智能审批模型，实现开函流程线上审批，以出具电子保函的形式向受益人承诺。",
    imageUrl: new URL(`/@/assets/public/pc5.png`, import.meta.url).href,
  },
  {
    TITLE: "公积金信用贷",
    INTRO:
      "通过公积金数据，打造政务大数据运用和金融机构服务融合的范本，为公积金缴存人提供优质、快捷、便利的融资服务，满足缴存人的综合消费需求，充分发挥消费对经济循环的牵引带动作用，提振内需，为活跃消费市场添势赋能。",
    imageUrl: new URL(`/@/assets/public/pc12.png`, import.meta.url).href,
  },
  {
    TITLE: "智慧快贷",
    INTRO:
      "通过医社保、企业主不动产等公共数据，结合银行掌握的行内外信息，运用大数据技术进行客户分析评价，为小微企业或个体工商户打造全流程的线上自助信用贷款业务。",
    imageUrl: new URL(`/@/assets/public/pc7.png`, import.meta.url).href,
  },
  {
    TITLE: "兴安贷",
    INTRO:
      "通过核实客户缴交连续性，解决风控规则冗长、效率较低的问题，提高客户申贷效率、提升用户体验。兴安贷+是厦门国际银行向符合贷款条件的自然人以便捷、快速方式发放的标准化信用或保证类贷款，贷款用途包括个人综合消费及经营性用途。",
    imageUrl: new URL(`/@/assets/public/pc10.png`, import.meta.url).href,
  },
  {
    TITLE: "投资模型大数据应用",
    INTRO:
      "通过深度挖掘利用福建省公共数据资源，建设企业基本信息核验、企业奖励信息核验、企业处罚信息查询、企业处罚信息核验、企业年报信息核验、企业股东出资信息查询和企业股东出资信息核验等模型。从而获取更全面、准确的一手企业信息，提高投资业务的风险排查能力。",
    imageUrl: new URL(`/@/assets/public/pc6.png`, import.meta.url).href,
  },
]);
onMounted(() => {
  getBannerList();
  getCustomTitle();
  getCaseList();
  getDataAdvantageList();
  getDataTab();
  getPublicCom();
  getPublicPro();
  getTableData();
  let dom = document.getElementsByClassName("tableList")[0];
  nextTick(() => {
    // console.log("here", maxHeight, maxHeight > scrollTopNum.value - 40);
    // if (maxHeight > scrollTopNum.value - 200) {
    //   interval.value = setInterval(() => {
    //     console.log("here1", maxHeight,scrollTopNum.value  ,scrollTopNum.value - 200);
    //     autoScrollTable("down", 1); // 向下滚动1px
    //   }, 50);
    // } else if(maxHeight < scrollTopNum.value - 200 || maxHeight == scrollTopNum.value - 200) {
    //   console.log("here");
    // clearInterval(interval.value);
    // interval.value = setInterval(() => {
    //   autoScrollTable("up", 1); // 向下滚动1px
    // }, 50);
    // }
  });
});
onBeforeUnmount(() => {
  clearInterval(interval.value);
});
function autoScrollTable(direction, distance) {
  let maxHeight = 0;
  if (tableRef.value) {
    maxHeight = parseInt(tableRef.value.maxHeight.split("rem")[0]);
  }
  let scrollHeight = scrollTopNum.value - 320;
  if (maxHeight > scrollHeight) {
    scrollTopNum.value += distance;
  } else {
    scrollTopNum.value = 0;
  }
  tableRef.value.scrollTo({ top: scrollTopNum.value });
}
function getCustomTitle() {
  getModuleConfig({ classId: [11171, 11172, 11173, 11161, 11159] }).then(
    (res: any) => {
      if (res?.length > 0) {
        state.title = res;
      }
    }
  );
}
function getBannerList() {
  getZoneCarousel({ classId: classId.value.topBanner }).then((res: any) => {
    state.titleContext.title = res.title;
    state.titleContext.subTitle = res.subTitle;
    state.titleContext.context = res.detail;
    coverImg.value = res.cover;
    let tempArray = [...res.videoUrl, ...res.imgUrls];
    state.bannerList.list = tempArray.map((fileName: string) => {
      return {
        url: apiUrl.value + fileName,
        type: getFileType(fileName),
      };
    });

    if (res.videoUrl && res.videoUrl.length > 0) {
      state.bannerList.videoUrl = [];
      res.videoUrl.forEach((element: string) => {
        state.bannerList.videoUrl.push({
          url: apiUrl.value + element,
        });
      });
    }
    if (res.imgUrls && res.imgUrls.length > 0) {
      state.bannerList.img = [];
      res.imgUrls.forEach((element: string) => {
        state.bannerList.img.push({
          url: apiUrl.value + element,
        });
      });
    }
  });
}
function getCaseList() {
  getDataApplication({ classId: 11158 }).then((res: any) => {
    if (res && res.length > 0) {
      state.caseList = res;
      state.caseList.forEach((element: string) => {
        element.image = apiUrl.value + element.image;
        element.logo = apiUrl.value + element.logo;
        element.active = false;
      });
      state.caseList[0].active = true;
      state.selectedCase = state.caseList[0];
    }
  });
}

function handleCase(data) {
  state.selectedCase = data;
  state.caseList.forEach((value) => {
    value.active = false;
  });
  state.caseList.find((obj) => obj.contentId === data.contentId).active = true;
}
function getDataAdvantageList() {
  getDataApplication({ classId: 11160 }).then((res: any) => {
    if (res && res.length > 0) {
      state.adList = res.map((i) => {
        return {
          text: i.detail,
          icon: apiUrl.value + i.logo,
          url: apiUrl.value + i.image,
          active: false,
          title: i.title,
          id: i.contentId,
        };
      });
      state.adList[0].active = true;
    }
  });
}

function changeAdSwiper(num) {
  useSwiper.value.slideTo(num);
}

function goPage(path: string, outer: boolean = false) {
  const regex = /(https?)/;
  if (outer && path) {
    window.open(unescapeHTML(path), "_blank");
    return;
  }
  router.push(unescapeHTML(path));
}
function unescapeHTML(a) {
  a = "" + a;
  return a
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'");
}
function getDataTab() {
  let params = {
    classId: 11170,
  };
  getAdvantage(params).then((res) => {
    dataTabList.value = res;
  });
}
function getPublicCom() {
  let params = {
    classId: 11167,
  };
  getAdvantage(params).then((res) => {
    home_2_list.value = res;
  });
}
function getPublicPro() {
  let params = {
    classId: 11168,
  };
  getAdvantage(params).then((res) => {
    leftPartList.value = res;
  });
}
function getTableData() {
  let params = {
    classId: 11169,
  };
  getDataDirectory(params).then((res) => {
    tableData.value = res;
  });
}
let countUp: ICountUp | undefined;
const onInit = (ctx: ICountUp) => {
  console.log("init", ctx);
  countUp = ctx;
};
const onFinished = () => {
  console.log("finished");
};
</script>
<style scoped lang="less">
::-webkit-scrollbar {
  display: none;
}
@media screen and (min-width: 769px) {
  #home {
    min-width: 1024px;
    /* :deep(.n-scrollbar-content){
    height: 465rem;
    overflow-y: auto;
  } */
    @keyframes countup {
      from {
        content: "0";
      }
      to {
        content: attr(data-count);
      }
    }
    .home_more {
      /* width: 130rem; */
      height: 28rem;
      font-size: 16rem;
      font-weight: 500;
      line-height: 28rem;
      color: rgb(76, 78, 107);
    }
    :deep(.n-data-table-th) {
      background: #2342a2;
      color: #ffffff;
      font-size: 24rem;
      font-weight: 500;
      padding: 28rem;
    }
    :deep(.swiper-button-prev1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      left: -80rem;
      transform: rotate(180deg);
      background: rgba(237, 237, 237, 0.6);
    }
    :deep(.swiper-button-next1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      right: -80rem;
      left: auto;
      transform: rotate(180deg);
      background: #f6f6f6;
    }
    .swiper-button-prev:after,
    .swiper-button-next:after {
      font-family: "";
    }
    .swiper-button-next:after {
      content: "";
    }
    .swiper-button-prev:after {
      content: "";
    }
    .home-wrapper {
      position: relative;
      padding-top: 60rem;
      width: 100%;
      // min-width: 1600px;
      height: 100vh;
    }
    .home-wrapper-free {
      position: relative;
      padding-top: 60rem;
      width: 100%;
      // min-width: 1600px;
      background: rgba(249, 250, 251, 1);
    }
    .bg-gray {
      background-color: rgba(249, 250, 251, 1);
    }
    .carousel-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: All 0.3s ease-in-out;
    }
    .carousel-img:hover {
      transform: scale(1.04);
    }
    .custom-dots {
      position: absolute;
      display: flex;
      flex-wrap: nowrap;
      position: absolute;
      bottom: 44rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .custom-dots li {
      display: inline-block;
      margin: 0 4rem;
      width: 101rem;
      height: 6rem;
      opacity: 0.5;
      background: rgba(229, 229, 229, 1);
      transition: width 0.3s, background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
    }
    .custom-dots li.is-active {
      opacity: 1;
      background: rgba(255, 255, 255, 1);
    }
    .glass {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      background: linear-gradient(
        to right,
        rgba(13, 46, 153, 0.8) 0%,
        rgba(70, 128, 255, 0) 100%
      );
    }
    .banner-content {
      position: absolute;
      top: calc(308 / 1080 * 100vh);
      left: 160rem;
      width: 75%;
      font-size: 20rem;
      letter-spacing: 1rem;
      color: rgba(255, 255, 255, 1);
      & > .title {
        font-size: 50rem;
        font-weight: 700;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .subTitle {
        font-size: 36rem;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .context {
        margin-top: 26px;
        line-height: 28rem;
        font-weight: 300;
      }
    }
    @keyframes blink {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    .toDown {
      position: absolute;
      bottom: 7%;
      left: 50%;
      transform: translateX(-50%);
      animation: blink 2s infinite;
    }
    .case-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      // width: 84%;
      // min-width: 1600px;
      height: calc(100% - calc(99 / 1080 * 100vh));
      .dotMatrix:nth-of-type(1) {
        position: absolute;
        top: 48rem;
        right: 0;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      .dotMatrix:nth-of-type(2) {
        position: absolute;
        bottom: -20rem;
        left: -72rem;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 96rem;
      }
      & > .subTitle {
        margin-bottom: 26rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .main {
        display: flex;
        justify-content: space-between;
        position: relative;
        width: 100%;
        height: calc(100% - 169rem);
        .side-menu {
          position: relative;
          z-index: 10;
          margin-right: 30rem;
          // width: 419px;
          // width: calc(419 / 1920 * 100%);
          width: 419rem;
          max-height: 100%;
          overflow-y: auto;
          .side-menu-item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100rem;
            border: 1rem solid rgba(240, 240, 240, 1);
            font-size: 24rem;
            font-weight: 500;
            color: rgba(128, 128, 128, 1);
            cursor: pointer;
            img {
              margin: 0 27rem 0 39rem;
              width: 45rem;
              height: 41rem;
            }
            span {
              width: calc(100% - 111rem);
            }
          }
          .side-menu-item.active {
            color: rgba(255, 255, 255, 1);
            background: rgba(13, 46, 153, 1);
          }
        }
        .board {
          position: relative;
          width: 1151rem;
          height: 100%;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            transition: All 0.3s ease-in-out;
          }
          img:hover {
            transform: scale(1.08);
          }
          .case-glass {
            position: absolute;
            top: 0;
            left: 0;
            width: 66%;
            height: 100%;
            pointer-events: none;
            background: linear-gradient(
              90deg,
              rgba(242, 76, 39, 1) 0.93%,
              rgba(242, 76, 39, 0) 100%
            );
            .case-glass-content {
              box-sizing: border-box;
              // display: flex;
              // flex-direction: column;
              // justify-content: space-between;
              position: absolute;
              left: 82rem;
              // margin-top: calc(86 / 1080 * 100vh);
              padding: calc(180 / 1080 * 100vh) 0 calc(20 / 1080 * 100vh);
              width: 90%;
              height: 100%;
              font-size: 20rem;
              letter-spacing: 1rem;
              color: rgba(255, 255, 255, 1);
              & > .title {
                /* height: 127rem; */
                font-size: 48rem;
                font-weight: 700;
                letter-spacing: 0rem;
                /* line-height: 63rem; */
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                margin-top: 24rem;
              }
              .sub-context {
                margin-top: calc(80 / 1080 * 100vh);
                .sub-context-title {
                  font-size: 48rem;
                  font-weight: 700;
                  letter-spacing: 0rem;
                  line-height: 63.65rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                }
                .sub-context-middle {
                  margin-top: 13rem;
                  width: 100%;
                  height: 23rem;
                  font-size: 18rem;
                  font-weight: 500;
                  letter-spacing: 1rem;
                  line-height: 23.87rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                .sub-context-context {
                  /* margin-top: 18rem; */
                  font-size: 20rem;
                  font-weight: 400;
                  letter-spacing: 0rem;
                  /* line-height: 26rem; */
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  /* -webkit-line-clamp: 4;
                -webkit-box-orient: vertical; */
                }
              }
            }
          }
        }
      }
    }
    .advantage-box {
      display: flex;
      flex-direction: column;
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      // width: 84%;
      // min-width: 1600px;
      /* height: calc(100% - calc(89 / 1080 * 100vh)); */
      .dotMatrix {
        position: absolute;
        top: 48rem;
        right: 0;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 96rem;
      }
      & > .subTitle {
        margin-bottom: 26rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .main {
        display: flex;
        justify-content: space-between;
        position: relative;
        width: calc(100% + 5rem);
        height: 622rem;
        // height: 686rem;
        .advantage-swiper {
          width: calc(100% - 240rem);
          // height: 686px;
          height: 100%;
          .mySwiper {
            width: 100%;
            height: 100%;
          }
          .ad-swiper-img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 60rem 0rem 60rem 0rem;
            transition: All 0.3s ease-in-out;
          }

          .ad-glass {
            position: absolute;
            top: 0;
            left: 0;
            padding: calc(108 / 1080 * 100vh) 96rem 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            border-radius: 60rem 0rem 60rem 0rem;
            background: linear-gradient(
              90deg,
              rgba(13, 46, 153, 1) 0%,
              rgba(13, 46, 153, 0) 100%
            );
            .ad-glass-icon {
              width: 128.57rem;
              height: 128.57rem;
              border-radius: 50%;
            }
            .title {
              margin-top: calc(45 / 1080 * 100vh);
              font-size: 50rem;
              font-weight: 500;
              letter-spacing: 1rem;
              line-height: 95.47rem;
              color: rgba(255, 255, 255, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-title {
              font-size: 48rem;
              font-weight: 700;
              letter-spacing: 0rem;
              line-height: 63.65rem;
              color: rgba(255, 255, 255, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-context {
              margin-top: calc(19 / 1080 * 100vh);
              width: 90%;
              font-size: 20rem;
              font-weight: 400;
              letter-spacing: 1rem;
              /* line-height: 28rem; */
              color: rgba(255, 255, 255, 1);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 4;
              -webkit-box-orient: vertical;
            }
          }
        }

        .advantage-select {
          width: 183rem;
          max-height: 100%;
          overflow-y: auto;
          .advantage-select-item {
            position: relative;
            margin: 0rem auto 0;
            width: 100%;
            height: 135rem;
            & > img {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto auto;
              width: 164rem;
              height: 112rem;
            }
            .ad-select-glass {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto auto;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 164rem;
              height: 112rem;
              pointer-events: none;
              background: rgba(13, 46, 153, 0.6);
              .ad-select-icon {
                margin: calc(20 / 1080 * 100vh) auto 0;
                width: 100%;
                height: 51rem;
                text-align: center;
                & > img {
                  width: 51rem;
                  height: 51rem;
                }
              }
              .ad-select-text {
                margin-top: calc(4 / 1080 * 100vh);
                width: 100%;
                font-size: 18rem;
                font-weight: 500;
                letter-spacing: 0rem;
                line-height: 23.87rem;
                color: rgba(255, 255, 255, 1);
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
              }
            }
          }
          .advantage-select-item.active {
            border-radius: 6rem;
            border: 4rem solid rgba(204, 207, 219, 1);
          }
        }
      }
    }
    .prod-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      // width: 84%;
      // min-width: 1600px;
      // height: calc(100% - calc(98 / 1080 * 100vh));
      & > .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        & > .prod-title-context {
          font-size: 72rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 96rem;
        }
        :deep(.n-tabs-nav-scroll-content) {
          border: 0;
        }
        :deep(.n-tabs-tab) {
          font-size: 22rem;
          font-weight: 400;
          letter-spacing: 0rem;
          line-height: 29.17rem;
          color: rgba(115, 115, 115, 1);
        }
        :deep(.n-tabs-tab.n-tabs-tab--active) {
          color: rgba(13, 46, 153, 1) !important;
        }
        :deep(.n-tabs-bar) {
          height: 4rem;
          border-radius: 3rem;
          --n-bar-color: rgba(13, 46, 153, 1);
        }
      }
      & > .subTitle {
        margin-bottom: 26rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }

      .prod-main {
        padding-bottom: 32rem;
        width: 100%;
        // min-height: 1000px;

        .empty {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 400rem;
        }
        .prod-item-box {
          display: grid;
          grid-template-columns: repeat(2, minmax(0rem, 1fr));
          gap: 20rem;
          justify-content: center;
          width: 100%;
          height: 100%;
          .prod-item:hover {
            transform: translate(0, -10rem);
            box-shadow: 0 0.25rem 1.25rem #d4d4d4;
          }
          .prod-item {
            display: flex;
            justify-content: space-between;
            height: 344rem;
            background: rgba(255, 255, 255, 1);
            cursor: pointer;
            transition: All 0.3s ease-in-out;
            .prod-info {
              padding: 52rem 0 0 53rem;
              width: calc(347 / 790 * 100%);
              height: 100%;
              overflow: hidden;
              .title {
                width: 100%;
                font-size: 36rem;
                font-weight: 700;
                letter-spacing: 0.2rem;
                line-height: 40rem;
                color: rgba(37, 43, 66, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .context {
                margin-top: 19rem;
                width: 100%;
                height: 80rem;
                font-size: 14rem;
                font-weight: 400;
                letter-spacing: 0.2rem;
                line-height: 20rem;
                color: rgba(85, 85, 85, 1);
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
              }
              .prod-view {
                display: flex;
                align-items: center;
                margin-top: 40rem;
                width: 100%;
                .prod-view-eye {
                  margin-right: 17rem;
                  width: 32.02rem;
                  height: 28.08rem;
                }
                .prod-view-text {
                  font-size: 14rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
            .prod-img {
              position: relative;
              width: 337rem;
              height: 100%;
              .prod-arrow-box {
                box-sizing: border-box;
                position: absolute;
                bottom: 24rem;
                // padding: 0 24;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 115.8rem;
                height: 46.13rem;
                opacity: 1;
                border-radius: 60rem;
                background: rgba(13, 46, 153, 1);
                transform: translateX(-50%);
                text-align: center;
                img {
                  width: 67.07rem;
                  height: 8.02rem;
                  object-fit: cover;
                }
              }
              & > img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }
        }
      }
      .prod-pag {
        display: flex;
        justify-content: center;
        margin-top: 36px;
        padding-bottom: 32px;
        text-align: center;
        :deep(.n-pagination-item) {
          color: rgba(128, 128, 128, 1);
          --n-item-border-active: 1px solid rgba(128, 128, 128, 1);
        }
      }
    }
    .activity-box {
      position: relative;
      margin: calc(153 / 1080 * 100vh) auto 0;
      width: 100%;
      // width: 84%;
      // min-width: 1600px;
      height: calc(100% - calc(153 / 1080 * 100vh));
      & > img:nth-of-type(1) {
        position: absolute;
        left: -200rem;
        width: 750rem;
        height: 581rem;
        border-radius: 20rem;
        transform: rotate(10.54deg);
        z-index: 10;
      }
      & > img:nth-of-type(2) {
        position: absolute;
        // right: -230px;
        // width: 750px;
        // height: 580px;
        right: -200rem;
        width: 750rem;
        height: 581rem;
        border-radius: 20rem;
        transform: rotate(10.54deg);
        z-index: 10;
      }
      .act-contain {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 20;
        transform: translate(-50%, -50%);
        // width: 605px;
        // height: 600px;
        width: calc(605 / 1920 * 100vw);
        height: calc(600 / 1080 * 100vh);
        background-color: rgba(255, 255, 255, 0.3);
        .act-title {
          width: 100%;
          text-align: center;
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .act-sub-title {
          width: 100%;
          font-size: 20rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 26.52rem;
          color: rgba(128, 128, 128, 1);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .act-context {
          margin-top: 40rem;
          width: 100%;
          font-size: 18rem;
          font-weight: 400;
          letter-spacing: 0rem;
          line-height: 30rem;
          color: rgba(85, 85, 85, 0.8);
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 11;
          -webkit-box-orient: vertical;
        }
        .act-button {
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          // margin: 117px auto 0;
          width: 262rem;
          height: calc(66 / 1080 * 100vh);
          border-radius: 100rem;
          background: rgba(13, 46, 153, 1);
          font-size: 24rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: calc(66 / 1080 * 100vh);
          color: rgba(255, 255, 255, 1);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
    }
    .advertisement {
      position: relative;
      width: 100%;
      height: 705rem;
      background-color: rgba(249, 250, 251, 1);
      // background-repeat: no-repeat;
      // background-size: cover;
      & > img {
        position: absolute;
        right: 0;
        top: 0;
        width: 968rem;
        height: 100%;
      }
      .advertisement-contain {
        position: relative;
        z-index: 10;
        margin: 0 0 0 259rem;
        padding-top: 129rem;
        width: 620rem;

        .title {
          width: 100%;
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .subTitle {
          margin-bottom: 18rem;
          width: 470rem;
          font-size: 36rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 47.74rem;
          color: rgba(128, 128, 128, 1);
        }
        .line {
          display: flex;
          align-items: center;
          margin-top: 26rem;
          width: 100%;
          .icon-box {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 25rem;
            width: 53rem;
            height: 53rem;
            background: rgba(232, 232, 232, 1);
            border-radius: 50%;
            img {
              width: 28rem;
              height: 28rem;
            }
          }
          .tel-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: rgba(128, 128, 128, 1);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .address-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: rgba(128, 128, 128, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
    .placeholder {
      width: 100%;
      height: 101rem;
    }
    .dataTab {
      display: flex;
      flex-direction: row;
      /* width: 1600rem;
      margin: 100rem auto; */
      background: rgba(250, 250, 250, 1);
      .dataItem {
        position: relative;
        display: flex;
        align-items: center;
        flex: 1;
        margin: 0 28rem;
        .itemImg {
          width: 150rem;
          height: 150rem;
          overflow: hidden;
          border-radius: 50%;
          box-shadow: 0px 7px 12px rgba(14, 31, 53, 0.08);
          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
        .itemText {
          margin-left: 40rem;
          .itemTitle {
            font-size: 30rem;
            line-height: 1;
            color: #383838;
            padding-bottom: 40rem;
            transition: all 0.2s linear;
          }
          .itemSubtitle {
            font-size: 70rem;
            font-weight: bold;
            line-height: 1;
            color: #383838;
            transition: all 0.2s linear;
            animation: countup linear forwards;
            :deep(.countup-wrap) {
              span {
                font-weight: bold !important;
              }
            }
          }
        }

        /* &::after {
        position: absolute;
        display: block;
        content: "";
        top: 50%;
        right: -2.85rem;
        width: 1px;
        height: 80%;
        transform: translateY(-50%);
        background-color: #e7e7e7;
      } */
      }
    }
    .publicCom {
      position: relative;
      .home_box_2_top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 1600rem;
        margin: 60rem auto 0;
        .home_title {
          line-height: 96rem;
          color: rgba(56, 56, 56, 1);
          font-size: 50rem !important;
          font-weight: 900;
          letter-spacing: 0rem;
          margin-bottom: 4rem;
        }
        .home_detail {
          font-size: 24rem;
          font-weight: 300;
          letter-spacing: 1rem;
          /* line-height: 24rem; */
          color: rgba(128, 128, 128, 1);
          width: 1133rem;
          margin-top: 22rem;
          overflow: hidden;
          text-overflow: ellipsis;
          text-wrap: wrap;
        }
      }
      .content {
        z-index: 1;
        display: flex;
        width: 1600rem;
        margin: 30rem auto 40rem;
        position: relative;
        .home_box_2_card {
          //  width: calc(291 / 1600 * 100%) !important;
          /* height: 100%; */
          /* width: 284rem !important; */
          height: 412rem;
          background: linear-gradient(0deg, #f4f3f9 0%, #fefefe 100%);
          border-radius: 4px;
          border: 2px solid #eceef0;
          /* margin-right: 40rem; */
          .home_box_2_item {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
            transition: transform 1s;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            .home_box_img {
              width: 92rem;
              height: 92rem;
              background: #2342a2;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
              overflow: hidden;
              img {
                width: 60%;
                height: 60%;
                transition: transform 1s;
                z-index: 999;
              }
            }
            .title {
              margin: 28rem 30rem 0;
              line-height: 40rem;
              height: 80rem;
              color: #383838;
              font-size: 30rem;
              font-weight: 600;
              text-align: center;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow: hidden;
            }
            .con {
              /* width: 100%; */
              height: 100rem;
              font-size: 18rem;
              font-weight: 400;
              line-height: 26rem;
              color: #999999;
              text-align: left;
              vertical-align: top;
              margin: 28rem 30rem 0;
              -webkit-line-clamp: 4;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow: hidden;
            }
            .Hbottom {
              width: 104rem;
              height: 54rem;
              border-radius: 60rem;
              border: 1rem solid #ffffff;
              margin: 20rem auto 0;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            img {
              height: 100%;
              width: 100%;
            }
          }
          /* &:nth-child(n + 1) {
          margin-right: 34rem;
        } */
        }

        /* .home_box_2_card:hover {
        cursor: pointer;
        img {
          transform: scale(1.5);
        }
      } */

        .ownSwiper {
          display: flex;
          flex-direction: row;
          justify-content: center;
          width: 100%;
          margin-top: -3rem;
          .swiperBtn {
            border: 0;
            box-shadow: 0 0.25rem 0.75rem rgba(30, 34, 40, 0.02);
            width: 2.2rem;
            height: 2.2rem;
            line-height: inherit;
            border-radius: 100%;
            text-shadow: none;
            transition: all 0.2s ease-in-out;
            background: rgba(var(--bs-primary-rgb), 0.9) !important;
            margin: 0 0.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .btn-disabled {
            background: rgba(var(--bs-primary-rgb), 0.7) !important;
            opacity: 0.35;
            cursor: auto;
            pointer-events: none;
          }
          .button-prev:after {
            content: "\e949";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
          .button-next:after {
            content: "\e94c";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
        }
      }
    }
    .twoPart {
      /* width: 1600rem; */
      /* margin: 100rem auto 0; */
      padding: 60rem 160rem 40rem;
      display: flex;
      justify-content: space-between;
      background: rgba(250, 250, 250, 1);
      .leftPart {
        //width: 48%;
        width: 100%;
        .title {
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .leftBody {
          // display: flex;
          // flex-wrap: wrap;
          // justify-content: space-between;
          display: grid;
          grid-template-columns: repeat(4, minmax(0rem, 1fr));
          gap: 20rem;
          height: 580rem;
          overflow: auto;
          margin-top: 28rem;
          .leftItem {
            border-radius: 12px;
            // width: 48%;
            // width:24%;
            height: 122rem;
            // margin-bottom: 28rem;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding: 0 16rem;
            img {
              width: 100%;
              height: 100%;
            }
            .itemName {
              font-weight: 500;
              font-size: 28rem;
              color: #ffffff;
              line-height: 32rem;
              z-index: 99;
              text-align: center;
            }
          }
        }
      }
      .rightPart {
        width: 48%;
        .title {
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .rightBody {
        .seamless {
          width: 100%;
          height: 575rem;
          overflow: hidden;
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #home {
    /* :deep(.n-scrollbar-content){
    height: 465rem;
    overflow-y: auto;
  } */
    @keyframes countup {
      from {
        content: "0";
      }
      to {
        content: attr(data-count);
      }
    }
    .home_more {
      /* width: 130rem; */
      height: 28rem;
      font-size: 16rem;
      font-weight: 500;
      line-height: 28rem;
      color: rgb(76, 78, 107);
    }
    :deep(.n-data-table-th) {
      background: #2342a2;
      color: #ffffff;
      font-size: 24rem;
      font-weight: 500;
      padding: 28rem;
    }
    :deep(.swiper-button-prev1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      left: -80rem;
      transform: rotate(180deg);
      background: rgba(237, 237, 237, 0.6);
    }
    :deep(.swiper-button-next1) {
      width: 68rem;
      height: 68rem;
      border-radius: 34rem;
      right: -80rem;
      left: auto;
      transform: rotate(180deg);
      background: #f6f6f6;
    }
    .swiper-button-prev:after,
    .swiper-button-next:after {
      font-family: "";
    }
    .swiper-button-next:after {
      content: "";
    }
    .swiper-button-prev:after {
      content: "";
    }
    .home-wrapper {
      position: relative;
      padding-top: 60rem;
      width: 100%;
      // min-width: 1600px;
      height: 50vh;
    }
    .home-wrapper-free {
      position: relative;
      padding-top: 60rem;
      width: 100%;
      // min-width: 1600px;
      background: rgba(249, 250, 251, 1);
    }
    .bg-gray {
      background-color: rgba(249, 250, 251, 1);
      height: auto;
      margin-bottom: 40rem;
    }
    .carousel-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: All 0.3s ease-in-out;
    }
    .carousel-img:hover {
      transform: scale(1.04);
    }
    .custom-dots {
      position: absolute;
      display: flex;
      flex-wrap: nowrap;
      position: absolute;
      bottom: 44rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .custom-dots li {
      display: inline-block;
      margin: 0 4rem;
      width: 101rem;
      height: 6rem;
      opacity: 0.5;
      background: rgba(229, 229, 229, 1);
      transition: width 0.3s, background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
    }
    .custom-dots li.is-active {
      opacity: 1;
      background: rgba(255, 255, 255, 1);
    }
    .glass {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      background: linear-gradient(
        to right,
        rgba(13, 46, 153, 0.8) 0%,
        rgba(70, 128, 255, 0) 100%
      );
    }
    .banner-content {
      /* position: absolute;
      top: calc(208 / 1080 * 100vh); */
      /* left: calc(160 / 1080 * 50vh); */
      /* width: calc(708 / 1080 * 100vh); */
      font-size: 20rem;
      letter-spacing: 1rem;
      color: rgba(255, 255, 255, 1);
      width: 720rem;
      margin: 170rem auto 0;
      & > .title {
        font-size: 32rem;
        font-weight: 700;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .subTitle {
        font-size: 36rem;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & > .context {
        margin-top: 12rem;
        line-height: 28rem;
        font-weight: 300;
        :deep(p) {
          font-size: 22rem !important;
          span {
            font-size: 22rem !important;
          }
        }
      }
    }
    @keyframes blink {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    .toDown {
      position: absolute;
      bottom: 7%;
      left: 50%;
      transform: translateX(-50%);
      animation: blink 2s infinite;
    }
    .case-box {
      position: relative;
      margin: 0 auto;
      width: 720rem;
      height: calc(100% - calc(99 / 1080 * 100vh));
      .dotMatrix:nth-of-type(1) {
        position: absolute;
        top: 48rem;
        right: 0;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      .dotMatrix:nth-of-type(2) {
        position: absolute;
        bottom: -20rem;
        left: -72rem;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 96rem;
      }
      & > .subTitle {
        margin-bottom: 26rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .main {
        display: flex;
        justify-content: space-between;
        position: relative;
        width: 100%;
        height: calc(100% - 169rem);
        display: flex;
        flex-direction: column;
        .side-menu {
          position: relative;
          z-index: 10;
          margin-right: 30rem;
          width: 100%;
          height: 400rem;
          max-height: 40%;
          overflow-y: auto;
          .side-menu-item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100rem;
            border: 1rem solid rgba(240, 240, 240, 1);
            font-size: 24rem;
            font-weight: 500;
            color: rgba(128, 128, 128, 1);
            cursor: pointer;
            img {
              margin: 0 27rem 0 39rem;
              width: 45rem;
              height: 41rem;
            }
            span {
              width: calc(100% - 111rem);
            }
          }
          .side-menu-item.active {
            color: rgba(255, 255, 255, 1);
            background: rgba(13, 46, 153, 1);
          }
        }
        .board {
          position: relative;
          width: 100%;
          height: 100%;
          overflow: hidden;
          margin-top: 24rem;
          img {
            width: 100%;
            height: 100%;
            transition: All 0.3s ease-in-out;
          }
          img:hover {
            transform: scale(1.08);
          }
          .case-glass {
            position: absolute;
            top: 0;
            left: 0;
            width: 66%;
            height: 100%;
            pointer-events: none;
            /* background: linear-gradient(
              90deg,
              rgba(242, 76, 39, 1) 0.93%,
              rgba(242, 76, 39, 0) 100%
            ); */
            .case-glass-content {
              box-sizing: border-box;
              // display: flex;
              // flex-direction: column;
              // justify-content: space-between;
              position: absolute;
              left: 82rem;
              // margin-top: calc(86 / 1080 * 100vh);
              padding: 20rem 0;
              width: 100%;
              height: 100%;
              font-size: 20rem;
              letter-spacing: 1rem;
              color: rgba(255, 255, 255, 1);
              & > .title {
                /* height: 127rem; */
                font-size: 48rem;
                font-weight: 700;
                letter-spacing: 0rem;
                /* line-height: 63rem; */
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                margin-top: 24rem;
              }
              .sub-context {
                margin-top: 24rem;
                .sub-context-title {
                  font-size: 48rem;
                  font-weight: 700;
                  letter-spacing: 0rem;
                  line-height: 63.65rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                }
                .sub-context-middle {
                  margin-top: 13rem;
                  width: 100%;
                  height: 23rem;
                  font-size: 18rem;
                  font-weight: 500;
                  letter-spacing: 1rem;
                  line-height: 23.87rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                .sub-context-context {
                  /* margin-top: 18rem; */
                  font-size: 20rem;
                  font-weight: 400;
                  letter-spacing: 0rem;

                  /* line-height: 26rem; */
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 7;
                  -webkit-box-orient: vertical;
                  /* -webkit-line-clamp: 4;
                -webkit-box-orient: vertical; */
                  :deep(p) {
                    font-size: 18rem !important;
                    span {
                      font-size: 18rem !important;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .advantage-box {
      display: flex;
      flex-direction: column;
      position: relative;
      margin: 0 auto;
      width: 720rem;
      /* height: calc(100% - calc(89 / 1080 * 100vh)); */
      .dotMatrix {
        position: absolute;
        top: 48rem;
        right: 0;
        width: 144rem;
        height: 104rem;
        background: url("../../assets/special/dotMatrix.png") center / 144rem
          104rem;
      }
      & > .title {
        font-size: 50rem;
        font-weight: 900;
        letter-spacing: 0rem;
        line-height: 96rem;
      }
      & > .subTitle {
        margin-bottom: 26rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }
      & > .main {
        display: flex;
        /* justify-content: space-between; */
        flex-direction: column;
        position: relative;
        width: 720rem;
        /* height: 622rem; */
        // height: 686rem;
        .advantage-swiper {
          width: 720rem;
          // height: 686px;
          height: 392rem;
          .mySwiper {
            width: 100%;
            height: 100%;
          }
          .ad-swiper-img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 60rem 0rem 60rem 0rem;
            transition: All 0.3s ease-in-out;
          }

          .ad-glass {
            position: absolute;
            top: 0;
            left: 0;
            padding: 68rem 60rem 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            border-radius: 60rem 0rem 60rem 0rem;
            /* background: linear-gradient(
              90deg,
              rgba(13, 46, 153, 1) 0%,
              rgba(13, 46, 153, 0) 100%
            ); */
            .ad-glass-icon {
              width: 128.57rem;
              height: 128.57rem;
              border-radius: 50%;
            }
            .title {
              /* margin-top: calc(45 / 1080 * 100vh); */
              font-size: 50rem;
              font-weight: 500;
              letter-spacing: 1rem;
              line-height: 95.47rem;
              color: rgba(255, 255, 255, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-title {
              font-size: 48rem;
              font-weight: 700;
              letter-spacing: 0rem;
              line-height: 63.65rem;
              color: rgba(255, 255, 255, 1);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .sub-context {
              margin-top: calc(19 / 1080 * 100vh);
              width: 90%;
              font-size: 20rem;
              font-weight: 400;
              letter-spacing: 1rem;
              /* line-height: 28rem; */
              color: rgba(255, 255, 255, 1);
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 4;
              -webkit-box-orient: vertical;
              :deep(p) {
                font-size: 18rem !important;
                span {
                  font-size: 18rem !important;
                }
              }
            }
          }
        }

        .advantage-select {
          width: 100%;
          max-height: 100%;
          overflow-x: auto;
          display: flex;
          justify-content: space-between;
          .advantage-select-item {
            position: relative;
            margin: 0rem auto 0;
            width: 100%;
            height: 135rem;
            & > img {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto auto;
              width: 164rem;
              height: 112rem;
            }
            .ad-select-glass {
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto auto;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 164rem;
              height: 112rem;
              pointer-events: none;
              background: rgba(13, 46, 153, 0.6);
              .ad-select-icon {
                margin: calc(20 / 1080 * 100vh) auto 0;
                width: 100%;
                height: 51rem;
                text-align: center;
                & > img {
                  width: 51rem;
                  height: 51rem;
                }
              }
              .ad-select-text {
                margin-top: calc(4 / 1080 * 100vh);
                width: 100%;
                font-size: 18rem;
                font-weight: 500;
                letter-spacing: 0rem;
                line-height: 23.87rem;
                color: rgba(255, 255, 255, 1);
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
              }
            }
          }
          .advantage-select-item.active {
            border-radius: 6rem;
            border: 4rem solid rgba(204, 207, 219, 1);
          }
        }
      }
    }
    .prod-box {
      position: relative;
      // margin: calc(99 / 1080 * 100vh) auto 0;
      margin: 0 auto;
      width: calc(100% - 320rem);
      // width: 84%;
      // min-width: 1600px;
      // height: calc(100% - calc(98 / 1080 * 100vh));
      & > .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        & > .prod-title-context {
          font-size: 72rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 96rem;
        }
        :deep(.n-tabs-nav-scroll-content) {
          border: 0;
        }
        :deep(.n-tabs-tab) {
          font-size: 22rem;
          font-weight: 400;
          letter-spacing: 0rem;
          line-height: 29.17rem;
          color: rgba(115, 115, 115, 1);
        }
        :deep(.n-tabs-tab.n-tabs-tab--active) {
          color: rgba(13, 46, 153, 1) !important;
        }
        :deep(.n-tabs-bar) {
          height: 4rem;
          border-radius: 3rem;
          --n-bar-color: rgba(13, 46, 153, 1);
        }
      }
      & > .subTitle {
        margin-bottom: 26rem;
        font-size: 36rem;
        font-weight: 500;
        letter-spacing: 0rem;
        line-height: 47rem;
        color: rgba(128, 128, 128, 1);
      }

      .prod-main {
        padding-bottom: 32rem;
        width: 100%;
        // min-height: 1000px;

        .empty {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 400rem;
        }
        .prod-item-box {
          display: grid;
          grid-template-columns: repeat(2, minmax(0rem, 1fr));
          gap: 20rem;
          justify-content: center;
          width: 100%;
          height: 100%;
          .prod-item:hover {
            transform: translate(0, -10rem);
            box-shadow: 0 0.25rem 1.25rem #d4d4d4;
          }
          .prod-item {
            display: flex;
            justify-content: space-between;
            height: 344rem;
            background: rgba(255, 255, 255, 1);
            cursor: pointer;
            transition: All 0.3s ease-in-out;
            .prod-info {
              padding: 52rem 0 0 53rem;
              width: calc(347 / 790 * 100%);
              height: 100%;
              overflow: hidden;
              .title {
                width: 100%;
                font-size: 36rem;
                font-weight: 700;
                letter-spacing: 0.2rem;
                line-height: 40rem;
                color: rgba(37, 43, 66, 1);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .context {
                margin-top: 19rem;
                width: 100%;
                height: 80rem;
                font-size: 14rem;
                font-weight: 400;
                letter-spacing: 0.2rem;
                line-height: 20rem;
                color: rgba(85, 85, 85, 1);
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 4;
                -webkit-box-orient: vertical;
              }
              .prod-view {
                display: flex;
                align-items: center;
                margin-top: 40rem;
                width: 100%;
                .prod-view-eye {
                  margin-right: 17rem;
                  width: 32.02rem;
                  height: 28.08rem;
                }
                .prod-view-text {
                  font-size: 14rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
            .prod-img {
              position: relative;
              width: 337rem;
              height: 100%;
              .prod-arrow-box {
                box-sizing: border-box;
                position: absolute;
                bottom: 24rem;
                // padding: 0 24;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 115.8rem;
                height: 46.13rem;
                opacity: 1;
                border-radius: 60rem;
                background: rgba(13, 46, 153, 1);
                transform: translateX(-50%);
                text-align: center;
                img {
                  width: 67.07rem;
                  height: 8.02rem;
                  object-fit: cover;
                }
              }
              & > img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }
        }
      }
      .prod-pag {
        display: flex;
        justify-content: center;
        margin-top: 36px;
        padding-bottom: 32px;
        text-align: center;
        :deep(.n-pagination-item) {
          color: rgba(128, 128, 128, 1);
          --n-item-border-active: 1px solid rgba(128, 128, 128, 1);
        }
      }
    }
    .activity-box {
      position: relative;
      margin: calc(153 / 1080 * 100vh) auto 0;
      width: 100%;
      // width: 84%;
      // min-width: 1600px;
      height: calc(100% - calc(153 / 1080 * 100vh));
      & > img:nth-of-type(1) {
        position: absolute;
        left: -200rem;
        width: 750rem;
        height: 581rem;
        border-radius: 20rem;
        transform: rotate(10.54deg);
        z-index: 10;
      }
      & > img:nth-of-type(2) {
        position: absolute;
        // right: -230px;
        // width: 750px;
        // height: 580px;
        right: -200rem;
        width: 750rem;
        height: 581rem;
        border-radius: 20rem;
        transform: rotate(10.54deg);
        z-index: 10;
      }
      .act-contain {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 20;
        transform: translate(-50%, -50%);
        // width: 605px;
        // height: 600px;
        width: calc(605 / 1920 * 100vw);
        height: calc(600 / 1080 * 100vh);
        background-color: rgba(255, 255, 255, 0.3);
        .act-title {
          width: 100%;
          text-align: center;
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .act-sub-title {
          width: 100%;
          font-size: 20rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 26.52rem;
          color: rgba(128, 128, 128, 1);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .act-context {
          margin-top: 40rem;
          width: 100%;
          font-size: 18rem;
          font-weight: 400;
          letter-spacing: 0rem;
          line-height: 30rem;
          color: rgba(85, 85, 85, 0.8);
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 11;
          -webkit-box-orient: vertical;
        }
        .act-button {
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          // margin: 117px auto 0;
          width: 262rem;
          height: calc(66 / 1080 * 100vh);
          border-radius: 100rem;
          background: rgba(13, 46, 153, 1);
          font-size: 24rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: calc(66 / 1080 * 100vh);
          color: rgba(255, 255, 255, 1);
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
    }
    .advertisement {
      position: relative;
      width: 100%;
      height: 705rem;
      background-color: rgba(249, 250, 251, 1);
      // background-repeat: no-repeat;
      // background-size: cover;
      & > img {
        position: absolute;
        right: 0;
        top: 0;
        width: 968rem;
        height: 100%;
      }
      .advertisement-contain {
        position: relative;
        z-index: 10;
        margin: 0 0 0 259rem;
        padding-top: 129rem;
        width: 620rem;

        .title {
          width: 100%;
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .subTitle {
          margin-bottom: 18rem;
          width: 470rem;
          font-size: 36rem;
          font-weight: 500;
          letter-spacing: 0rem;
          line-height: 47.74rem;
          color: rgba(128, 128, 128, 1);
        }
        .line {
          display: flex;
          align-items: center;
          margin-top: 26rem;
          width: 100%;
          .icon-box {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 25rem;
            width: 53rem;
            height: 53rem;
            background: rgba(232, 232, 232, 1);
            border-radius: 50%;
            img {
              width: 28rem;
              height: 28rem;
            }
          }
          .tel-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: rgba(128, 128, 128, 1);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .address-text {
            width: calc(100% - 80rem);
            font-size: 24rem;
            font-weight: 500;
            letter-spacing: 1rem;
            line-height: 31.82rem;
            color: rgba(128, 128, 128, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
    .placeholder {
      width: 100%;
      height: 101rem;
    }
    .dataTab {
      display: flex;
      flex-direction: row;
      /* width: 1600rem;
    margin: 100rem auto; */
      background: rgba(250, 250, 250, 1);
      width: 100%;
      overflow-x: auto;
      .dataItem {
        position: relative;
        display: flex;
        align-items: center;
        width: 465rem;
        margin: 0 28rem;
        .itemImg {
          width: 150rem;
          height: 150rem;
          overflow: hidden;
          border-radius: 50%;
          box-shadow: 0px 7px 12px rgba(14, 31, 53, 0.08);
          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
        .itemText {
          margin-left: 40rem;
          .itemTitle {
            font-size: 30rem;
            line-height: 1;
            color: #383838;
            padding-bottom: 40rem;
            transition: all 0.2s linear;
          }
          .itemSubtitle {
            font-size: 70rem;
            font-weight: bold;
            line-height: 1;
            color: #383838;
            transition: all 0.2s linear;
            animation: countup linear forwards;
            :deep(.countup-wrap) {
              span {
                font-weight: bold !important;
              }
            }
          }
        }

        /* &::after {
        position: absolute;
        display: block;
        content: "";
        top: 50%;
        right: -2.85rem;
        width: 1px;
        height: 80%;
        transform: translateY(-50%);
        background-color: #e7e7e7;
      } */
      }
    }
    .publicCom {
      position: relative;
      .home_box_2_top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 720rem;
        margin: 60rem auto 0;
        .home_title {
          line-height: 96rem;
          color: rgba(56, 56, 56, 1);
          font-size: 50rem !important;
          font-weight: 900;
          letter-spacing: 0rem;
          margin-bottom: 4rem;
        }
      }
      .content {
        z-index: 1;
        display: flex;
        width: 720rem;
        margin: 30rem auto 40rem;
        position: relative;
        .home_box_2_card {
          /* width: calc(370 / 1600 * 100%) !important;
        height: 100%; */
          /* width: 284rem !important; */
          height: 412rem;
          background: linear-gradient(0deg, #f4f3f9 0%, #fefefe 100%);
          border-radius: 4px;
          border: 2px solid #eceef0;
          /* margin-right: 40rem; */
          .home_box_2_item {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
            transition: transform 1s;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            .home_box_img {
              width: 92rem;
              height: 92rem;
              background: #2342a2;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
              overflow: hidden;
              img {
                width: 60%;
                height: 60%;
                transition: transform 1s;
                z-index: 999;
              }
            }
            .title {
              margin: 28rem 30rem 0;
              line-height: 40rem;
              height: 80rem;
              color: #383838;
              font-size: 30rem;
              font-weight: 600;
              text-align: center;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow: hidden;
            }
            .con {
              /* width: 100%; */
              height: 100rem;
              font-size: 18rem;
              font-weight: 400;
              line-height: 26rem;
              color: #999999;
              text-align: left;
              vertical-align: top;
              margin: 28rem 30rem 0;
              -webkit-line-clamp: 4;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow: hidden;
            }
            .Hbottom {
              width: 104rem;
              height: 54rem;
              border-radius: 60rem;
              border: 1rem solid #ffffff;
              margin: 20rem auto 0;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            img {
              height: 100%;
              width: 100%;
            }
          }
          /* &:nth-child(n + 1) {
          margin-right: 34rem;
        } */
        }

        /* .home_box_2_card:hover {
        cursor: pointer;
        img {
          transform: scale(1.5);
        }
      } */

        .ownSwiper {
          display: flex;
          flex-direction: row;
          justify-content: center;
          width: 100%;
          margin-top: -3rem;
          .swiperBtn {
            border: 0;
            box-shadow: 0 0.25rem 0.75rem rgba(30, 34, 40, 0.02);
            width: 2.2rem;
            height: 2.2rem;
            line-height: inherit;
            border-radius: 100%;
            text-shadow: none;
            transition: all 0.2s ease-in-out;
            background: rgba(var(--bs-primary-rgb), 0.9) !important;
            margin: 0 0.2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .btn-disabled {
            background: rgba(var(--bs-primary-rgb), 0.7) !important;
            opacity: 0.35;
            cursor: auto;
            pointer-events: none;
          }
          .button-prev:after {
            content: "\e949";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
          .button-next:after {
            content: "\e94c";
            font-family: Unicons;
            font-size: 1.2rem;
            color: var(--bs-white) !important;
          }
        }
      }
    }
    .twoPart {
      /* width: 1600rem; */
      /* margin: 100rem auto 0; */
      display: flex;
      flex-direction: column;
      background: rgba(250, 250, 250, 1);
      padding: 60rem 0 40rem;
      .leftPart {
        width: 720rem;
        margin: 0 auto 40rem;
        .title {
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .leftBody {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          height: 580rem;
          overflow: auto;
          margin-top: 28rem;
          .leftItem {
            border-radius: 12px;
            width: 48%;
            height: 122rem;
            margin-bottom: 28rem;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            img {
              width: 100%;
              height: 100%;
            }
            .itemName {
              font-weight: 500;
              font-size: 32rem;
              color: #ffffff;
              line-height: 32rem;
              z-index: 99;
            }
          }
        }
      }
      .rightPart {
        width: 720rem;
        margin: 0 auto;
        .title {
          font-size: 50rem;
          font-weight: 900;
          letter-spacing: 0rem;
          line-height: 104.26rem;
          color: rgba(56, 56, 56, 1);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .rightBody {
        .seamless {
          width: 100%;
          height: 575rem;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
