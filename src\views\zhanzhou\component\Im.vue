<template>
  <div class="im-container">
    <div class="header">
      <!-- <a-avatar :size="30" :src="apiUrl + otherUser.headIcon" /> -->
      <span class="name">客服</span>
      <!-- <i class="icon-ym icon-ym-nav-close" @click="visible = false"></i> -->
      <n-icon size="30" :component="CloseOutline" @click="closeModal()">
      </n-icon>
    </div>
    <div class="main">
      <div class="chatBox">
        <div class="chatList" ref="chatListRef">
          <div
            class="chatList-item"
            v-for="(item, index) in list"
            :key="index"
            :class="{ 'chatList-item--mine': item.userId == '111' }"
          >
            <div class="chatList-user" v-if="item.userId == '111'">
              <n-avatar round :size="40" />
              <cite>
                <i>{{ item.dateTime }}</i>
                我
              </cite>
            </div>
            <div class="chatList-user" v-else>
              <n-avatar round :size="40" />
              <cite>
                客服<i>{{ item.dateTime }}</i>
              </cite>
            </div>
            <div class="chatList-text">
              <div class="chatList-arrow"></div>
              <p
                v-if="item.messageType == 'text'"
                v-html="item.message"
                class="chatList__msg--text"
              ></p>
              <img
                :src="apiUrl + item.path"
                class="chatList__msg--img"
                v-if="item.messageType == 'image' && item.path"
              />
              <audio
                class="chatList__msg--audio"
                controls
                :src="apiUrl + item.message.path"
                v-if="item.messageType == 'voice' && item.path"
              ></audio>
              <video
                :src="item.message"
                controls
                class="chatList__msg--video"
                v-if="item.messageType == 'video'"
              ></video>
            </div>
          </div>
        </div>
        <div class="toolBox">
          <div class="toolBox-left">
            <!-- <a-upload
              :showUploadList="false"
              :action="uploadUrl + '/IM'"
              class="uploadImg-btn"
              :headers="getHeaders"
              accept="image/*"
              :before-upload="beforeUpload"
              @change="handleChange"
            >
              <i
                class="link-text ym-custom ym-custom-image"
                title="发送图片"
              ></i>
            </a-upload> -->
          </div>
        </div>
        <div class="writeBox">
          <n-input
            type="textarea"
            placeholder="点击这里，直接输入消息咨询"
            v-model:value.trim="messageContent"
            @keyup.enter.native="sendMessage()"
          ></n-input>
          <div class="btns dynamicMore">
            <n-button @click="sendMessage()">发送</n-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineComponent } from "vue";
import { useGlobSetting } from "/@/hooks/setting";
import { CloseOutline } from "@vicons/ionicons5";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const uploadUrl = ref(globSetting.uploadUrl);
const messageContent = ref("");
const emit = defineEmits(["close"]);
defineComponent({
  CloseOutline,
});
const list = ref([]);
function sendMessage() {
  console.log(messageContent);
  list.value.push({
    userId: "111",
    dateTime: "2024-07-02 14:00:00",
    messageType: "text",
    message: messageContent.value,
    path: "",
  });
  messageContent.value = "";
  setTimeout(() => {
    list.value.push({
      userId: "222",
      dateTime: "2024-07-02 14:00:00",
      messageType: "text",
      message: "请稍等...",
      path: "",
    });
  }, 1000);
}
function closeModal() {
  emit("close");
}
</script>
<style lang="less" scoped>
.im-container {
  position: fixed;
  right: 290px;
  bottom: 10px;
  z-index: 3000;
  background: #e6e9f0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 0 6px 1px rgba(0, 0, 0, 0.3);
  * {
    box-sizing: border-box;
  }
  .header {
    background: #fff;
    height: 50px;
    border-bottom: 1px solid #eee;
    padding: 0 10px;
    font-size: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .ant-avatar {
      margin-top: 10px;
      vertical-align: top;
      margin-right: 10px;
      &.offLine {
        filter: grayscale(1);
      }
    }
    span {
      line-height: 49px;
      font-size: 16px;
    }
    .icon-ym {
      float: right;
      font-size: 16px;
      line-height: 50px;
      cursor: pointer;
      &:hover {
        color: #f15d6d;
      }
    }
  }
  .main {
    display: flex;
    .chatBox {
      width: 600px;
      height: 480px;
      .chatList {
        height: 335px;
        overflow: auto;
        overflow-x: hidden;
        padding: 5px 15px 5px;
        background: #fff;
      }
      .toolBox {
        height: 35px;
        display: flex;
        justify-content: space-between;
        padding: 0 10px;
        align-items: center;
        font-size: 0;
        i {
          line-height: 35px;
          font-size: 20px;
          margin-right: 10px;
          color: #6b7a99;
          cursor: pointer;
        }
        .toolBox-left {
          .uploadImg-btn {
            display: inline-block;
          }
        }
        .toolBox-right {
          color: #6b7a99;
          line-height: 35px;
          font-size: 14px;
          display: flex;
          align-items: center;
          cursor: pointer;
          i {
            font-size: 18px;
            margin-right: 5px;
          }
        }
      }
      .writeBox {
        background: #fff;
        height: 110px;
        .ant-input {
          border: none !important;
          box-shadow: none !important;
          resize: none;
        }
        .btns {
          padding-right: 10px;
          text-align: right;
        }
      }
    }
    .historyBox {
      width: 350px;
      height: 480px;
      margin-left: 10px;
      background: #ebeef5;
      .historyList-box {
        height: 448px;
        overflow: auto;
        overflow-x: hidden;
        padding: 5px;
        border-bottom-right-radius: 4px;
        .chatList-item .chatList-text {
          max-width: 94%;
        }
        .chatList__msg--audio {
          width: 230px;
        }
      }
    }
  }
  .chatList-item {
    position: relative;
    font-size: 0;
    margin-bottom: 10px;
    padding-left: 60px;
    min-height: 68px;
    text-align: left;
    .chatList-user,
    .chatList-text {
      display: inline-block;
      vertical-align: top;
      font-size: 14px;
    }
    .chatList-user {
      position: absolute;
      cursor: pointer;
      left: 3px;
    }
    .chatList-user img {
      &.offLine {
        filter: grayscale(1);
      }
    }
    .chatList-user cite {
      position: absolute;
      left: 60px;
      top: -2px;
      line-height: 24px;
      font-size: 12px;
      white-space: nowrap;
      color: #8b949e;
      text-align: left;
      font-style: normal;
    }
    .chatList-user cite i {
      padding-left: 15px;
      font-style: normal;
    }
    .chatList-text {
      max-width: 75%;
      position: relative;
      line-height: 22px;
      margin-top: 25px;
      padding: 8px 15px;
      background-color: #ebeef5;
      border-radius: 3px;
      border: 1px solid #ebeef5;
      word-break: break-all;
    }
    .chatList__msg--text {
      line-height: 24px;
      img {
        vertical-align: top;
        width: 24px;
        height: 24px;
        display: inline-block;
      }
    }
    .chatList-arrow {
      top: 6px;
      left: -8px;
      position: absolute;
      display: block;
      width: 0;
      height: 0;
      border-color: transparent;
      border-style: solid;
      border-width: 8px;
      border-left-width: 0;
      border-right-color: #ebeef5;
    }
    .chatList-arrow::after {
      content: " ";
      top: -7px;
      left: 1px;
      position: absolute;
      display: block;
      width: 0;
      height: 0;
      border-color: transparent;
      border-style: solid;
      border-width: 7px;
      border-left-width: 0;
      border-right-color: #ebeef5;
    }
    &.chatList-item--mine .chatList-text .chatList-arrow {
      left: auto;
      right: -5px;
      border-color: transparent;
      border-style: solid;
      border-width: 8px;
      border-right-width: 0;
      border-left-color: #1a8cff;
    }
    &.chatList-item--mine .chatList-text .chatList-arrow::after {
      left: auto;
      right: -2px;
      border-color: transparent;
      border-style: solid;
      border-width: 7px;
      border-right-width: 0;
      border-left-color: #1a8cff;
    }
    &.chatList-item--mine {
      text-align: right;
      padding-left: 0;
      padding-right: 60px;
    }
    &.chatList-item--mine .chatList-user {
      left: auto;
      right: 3px;
    }
    &.chatList-item--mine .chatList-user cite {
      left: auto;
      right: 60px;
      text-align: right;
    }
    &.chatList-item--mine .chatList-user cite i {
      padding-left: 0;
      padding-right: 15px;
    }
    &.chatList-item--mine .chatList-text {
      margin-left: 0;
      text-align: left;
      background-color: #1a8cff;
      color: #fff;
    }
    .chatList__msg--video,
    .chatList__msg--file {
      position: relative;
      max-width: 100%;
      min-width: 200px;
      width: 100%;
      margin: 10px 0;
      overflow: hidden;
      border-radius: 5px;
      cursor: pointer;
      display: block;
    }
    .chatList__msg--img {
      position: relative;
      max-width: 200px;
      width: 100%;
      margin: 10px 0;
      overflow: hidden;
      border-radius: 5px;
      cursor: pointer;
      display: block;
    }
  }
}
.emojiBox {
  height: 150px;
  width: 300px;
  overflow: auto;
  text-align: left;
}
.emojiBox .emoji {
  padding: 0;
}
.emojiBox li {
  display: inline-block;
  width: 28px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
}
</style>
<style scoped lang="less">
.emoji-popover {
  z-index: 30000 !important;
  .ant-popover-inner-content {
    padding: 10px;
  }
}
:deep(.n-button__border) {
  border: none !important;
}
</style>
