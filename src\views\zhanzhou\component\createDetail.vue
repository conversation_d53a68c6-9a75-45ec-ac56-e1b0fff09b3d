<template>
  <div id="legal">
    <div class="legal legal_box_1">
      <n-carousel
        ref="carousel"
        effect="slide"
        draggable
        keyboard
        autoplay
        class="legal_box_1_carousel"
        :onUpdateCurrentIndex="onUpdate"
        :show-dots="false"
      >
        <n-carousel-item
          class="n-carousel-item"
          v-if="legal_1_videoUrl.length != 0"
          v-for="(item, index) in legal_1_videoUrl"
          style="width: 100%; height: 100%"
        >
          <div class="legal_box_1_item">
            <video
              :src="apiUrl + item"
              autoplay
              loop
              muted
              style="width: 100%; height: 100%"
            ></video>
          </div>
        </n-carousel-item>
        <n-carousel-item
          class="n-carousel-item"
          v-else
          v-for="(item, index) in detail.carouselImage"
          style="width: 100%; height: 100%"
        >
          <div class="legal_box_1_item">
            <img :src="apiUrl + item" alt="" />
          </div>
        </n-carousel-item>
      </n-carousel>
      <div class="legal_box_1_header">
        <div class="legal_box_1_glass">
          <div class="legal_box_1_header_title">{{ detail.title }}</div>
          <!-- <div class="legal_box_1_header_context" v-html="detail.intro"></div> -->
        </div>
      </div>
    </div>
    <div class="legal legal_box_2">
      <div style="margin-bottom: 30rem">
        <n-breadcrumb separator=">">
          <n-breadcrumb-item @click="goPage('/')"> 首页</n-breadcrumb-item>
          <n-breadcrumb-item> 场景应用详情</n-breadcrumb-item>
        </n-breadcrumb>
      </div>
      <div class="text" v-html="detail.detail"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useGlobSetting } from "/@/hooks/setting";
import { getDetailBg } from "/@/api/home/<USER>";
import { useRoute, useRouter } from "vue-router";
import { collectUserVisitInfoAndSendToServer } from "/@/utils/visit.js";
const route = useRoute();
const router = useRouter();
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
const legal_1_videoUrl = ref([]);
const detail = ref({});
function getBg() {
  let params = {
    contentId: route.query.contentId,
  };
  getDetailBg(params).then((res) => {
    detail.value = res;
    const regex = /src="\/local\//;
    if (regex.test(detail.value.detail)) {
      detail.value.detail = detail.value.detail.replace(
        /src="\/local\//g,
        `src="${apiUrl.value}/local/`
      );
    }
  });
}
function goPage(path) {
  router.push(path);
}
onMounted(() => {
  getBg();
  let params = {
    contentId: route.query.contentId,
  };
  collectUserVisitInfoAndSendToServer(params);
});
</script>
<style lang="less" scoped>
@media screen and (min-width: 769px) {
  #legal {
    min-width: 1600rem;
    .legal_box_1 {
      height: 100vh;
      width: 100%;
      display: flex;
      .legal_box_1_carousel {
        height: 100%;
        .legal_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
      }
      .legal_box_1_header {
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(13, 46, 153, 0.8) 0%,
          rgba(70, 128, 255, 0) 100%
        );
        position: absolute;
        display: flex;
        justify-content: center;
        flex-direction: column;
        .legal_box_1_glass {
          /* position: absolute;
          left: 160rem;
          width: 75%;
          top: 270rem; */
          display: flex;
          justify-content: center;
          align-items: center;
          .legal_box_1_header_title {
            /* font-size: 50rem; */
            font-size: 86rem;
            font-weight: 900;
            color: rgba(255, 255, 255, 1);
          }
          .legal_box_1_header_context {
            margin-top: 26px;
            line-height: 28rem;
            font-size: 20rem;
            font-weight: 300;
            color: #ffffff;
          }
        }
      }
    }
    .legal_box_2 {
      /* height: 100vh; */
      width: 1600rem;
      margin: 48rem auto 0;
      .text {
        font-size: 18rem;
        font-weight: 400;
        line-height: 30rem;
        width: 100%;
        /* overflow: auto; */
        white-space: wrap;
        overflow-wrap: break-word;
        height: 93%;
        color: rgba(85, 85, 85, 0.8);
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #legal {
    min-width: 100%;
    .legal_box_1 {
      height: 50vh;
      width: 100%;
      display: flex;
      .legal_box_1_carousel {
        height: 100%;
        .legal_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
      }
      .legal_box_1_header {
        width: 100%;
        height: 50vh;
        background: linear-gradient(
          90deg,
          rgba(13, 46, 153, 0.8) 0%,
          rgba(70, 128, 255, 0) 100%
        );
        position: absolute;
        display: flex;
        /* justify-content: center; */
        flex-direction: column;
        .legal_box_1_glass {
          width: 720rem;
          margin: 170rem auto 0;
          .legal_box_1_header_title {
            /* height: 125rem; */
            font-size: 32rem;
            font-weight: 900;
            /* line-height: 124.53rem; */
            color: rgba(255, 255, 255, 1);
            /* display: flex;
            justify-content: center;
            align-items: center; */
          }
          .legal_box_1_header_context {
            margin-top: 26rem;
            /* line-height: 28rem; */
            font-size: 22rem;
            font-weight: 300;
            color: #ffffff;
            :deep(p){
              font-size: 22rem !important;
              span{
                font-size: 22rem !important;
              }
            }
          }
        }
      }
    }
    .legal_box_2 {
      /* height: 100vh; */
      width: 720rem;
      margin: 48rem auto 0;
      .text {
        font-size: 18rem;
        font-weight: 400;
        line-height: 30rem;
        width: 100%;
        /* overflow: auto; */
        white-space: wrap;
        overflow-wrap: break-word;
        height: 93%;
        color: rgba(85, 85, 85, 0.8);
        :deep(img) {
          width: 100% !important;
        }
      }
    }
  }
}
</style>
