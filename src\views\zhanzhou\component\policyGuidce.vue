<template>
  <div id="policy">
    <div class="policy_box_1">
      <n-carousel
        ref="carousel"
        draggable
        autoplay
        dot-type="line"
        class="policy_box_1_carousel"
      >
        <n-carousel-item
          class="n-carousel-item"
          v-if="policy_1_videoUrl.length != 0"
          v-for="(item, index) in policy_1_videoUrl"
          style="width: 100%; height: 100%"
        >
          <div class="policy_box_1_item">
            <video
              :src="apiUrl + item"
              autoplay
              loop
              muted
              style="width: 100%; height: 100%; object-fit: fill"
              :poster="apiUrl + coverImg"
            >
              <!-- <source
              :src="apiUrl+`/local/video/2024-04-13/test/output.m3u8`"
              type="application/x-mpegURL"
          /> -->
            </video>
          </div>
        </n-carousel-item>
        <n-carousel-item
          class="n-carousel-item"
          v-else
          v-for="(item, index) in policy_1_imgUrls"
          style="width: 100%; height: 100%"
        >
          <div class="policy_box_1_item">
            <img :src="apiUrl + item" alt="" />
          </div>
        </n-carousel-item>
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              @click="to(index - 1)"
            />
          </ul>
        </template>
      </n-carousel>
      <div class="glass">
        <div class="banner-content">
          <div class="title">{{ titleContext.title }}</div>
          <div class="subTitle" v-if="false">{{ titleContext.subTitle }}</div>
          <div class="context" v-html="titleContext.detail"></div>
        </div>
        <!-- <div class="toDown">
          <img src="../../assets/special/down.png" alt="" />
        </div> -->
      </div>
    </div>
    <div class="policy_box_2">
      <div style="margin-bottom: 30rem">
        <n-breadcrumb separator=">">
          <n-breadcrumb-item @click="goPage('/')"> 首页</n-breadcrumb-item>
          <n-breadcrumb-item> 政策指引</n-breadcrumb-item>
          <n-breadcrumb-item> {{ detail.TITLE }}</n-breadcrumb-item>
        </n-breadcrumb>
      </div>
      <div style="display: flex; height: 90vh" v-if="!isMobile">
        <div class="treeList">
          <n-tree
            ref="treeRef"
            block-line
            :data="treeList"
            selectable
            check-on-click
            :render-label="renderLabel"
            :default-selected-keys="checkedKeys"
            key-field="classId"
            label-field="className"
            default-expand-all
            @update:selected-keys="getDetail"
          />
        </div>
        <div class="content">
          <div class="content_title">{{ detail.TITLE }}</div>
          <div class="content_text" v-html="detail.CONTENT"></div>
        </div>
      </div>
      <div class="container" v-else>
        <div class="treeList">
          <n-tree
            ref="treeRef"
            block-line
            :data="treeList"
            selectable
            check-on-click
            :render-label="renderLabel"
            :default-selected-keys="checkedKeys"
            key-field="classId"
            label-field="className"
            default-expand-all
            @update:selected-keys="getDetail"
          />
        </div>
        <div class="content">
          <div class="content_title">{{ detail.TITLE }}</div>
          <div class="content_text" v-html="detail.CONTENT"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import toRight from "/@/assets/home/<USER>";
import { h, ref, onMounted, computed } from "vue";
import { NImage, TreeOption } from "naive-ui";
import { getBg } from "/@/api/home/<USER>";
import { getPolicyTree, getPolicyGuidanceDetail } from "/@/api/home/<USER>";
import { useRoute, useRouter } from "vue-router";
import { useGlobSetting } from "/@/hooks/setting";
import { collectUserVisitInfoAndSendToServer } from "/@/utils/visit.js";
const globSetting = useGlobSetting();
const apiUrl = ref(globSetting.apiUrl);
import { useUserStoreWithOut } from "/@/stores/modules/sysInfo";
const userStore = useUserStoreWithOut();

const isMobile = computed(() => {
  return userStore.deviceWidth <= 768;
});
const route = useRoute();
const router = useRouter();
const checkedKeys = ref([]);
const treeList = ref([]);
const detail = ref({});
const treeRef = ref(null);
const policy_1_videoUrl = ref([]);
const policy_1_imgUrls = ref([]);
const titleContext = ref({});
const coverImg =ref('');
function getTree() {
  getPolicyTree().then((res) => {
    treeList.value = res;
    treeList.value.forEach((e) => {
      e.suffix = () =>
        h(
          NImage,
          { src: toRight, previewDisabled: true },
          { default: () => "Suffix" }
        );
      if (e.children.length != 0) {
        e.children.forEach((item) => {
          item.suffix = () =>
            h(
              NImage,
              { src: toRight, previewDisabled: true },
              { default: () => "Suffix" }
            );
          if (item.children.length == 0) {
            delete item.children;
          }
        });
      } else {
        delete e.children;
      }
    });
  });
}
function getDetail(keys) {
  let contentId = 0;
  if (keys) {
    contentId = parseInt(keys);
  } else {
    contentId = treeList.value[0].children[0].classId;
  }
  checkedKeys.value.push(contentId);
  let params = {
    contentId: contentId,
  };
  getPolicyGuidanceDetail(params).then((res) => {
    detail.value = res;
    const regex = /src="/;
    if (regex.test(detail.value.CONTENT)) {
      detail.value.CONTENT = detail.value.CONTENT.replace(
        "/local",
        `${apiUrl.value}/local`
      );
    }
  });
}
function goPage(path) {
  router.push(path);
}
function renderLabel({ option }: { option: TreeOption }) {
  let label;
  if (option.children) {
    label = option.className + `(${option.num})`;
    return h("span", { class: "custom-label" }, label);
  } else {
    label = option.className;
    return label;
  }
}
function getbox1Bg() {
  let params = {
    classId: 11185,
  };
  getBg(params).then((res) => {
    policy_1_imgUrls.value = res.imgUrls;
    policy_1_videoUrl.value = res.videoUrl;
    titleContext.value = {
      title: res.title,
      subTitle: res.subTitle,
      detail: res.detail,
      buttonName: res.buttonName,
      buttonUrl: res.buttonUrl,
    };
    coverImg.value = res.cover
  });
}
onMounted(() => {
  getTree();
  getbox1Bg();
  let params = {
    contentId: route.query.contentId,
  };
  collectUserVisitInfoAndSendToServer(params);
  setTimeout(() => {
    if (route.query) {
      getDetail(route.query.contentId);
    } else {
      getDetail();
    }
  }, 500);
});
</script>
<style lang="less" scoped>
@media screen and (min-width: 769px) {
  #policy {
    min-width: 1600rem;
    :deep(.n-tree) {
      --n-line-height: 2 !important;
      .n-tree-node-wrapper {
        padding: 6px 0;
      }
      .n-tree-node--selected {
        background: rgba(13, 46, 153, 1) !important;
        border: 1rem solid rgba(240, 241, 247, 1);
        padding: 22rem 0;
        .n-tree-node-content__text {
          color: rgba(255, 255, 255, 1);
          font-weight: 500;
          .custom-label {
            color: rgba(255, 255, 255, 1);
            font-weight: 500;
          }
        }
      }
    }
    :deep(.n-tree-node) {
      align-items: center;
    }
    :deep(.n-tree-node-switcher__icon) {
      font-size: 18rem;
      height: 18rem;
      width: 18rem;
    }
    :deep(.n-tree-node-content__text) {
      line-height: 24rem;
      font-size: 18rem;
      font-weight: 500;
      color: rgba(166, 166, 166, 1);
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow: hidden;
      .custom-label {
        font-size: 24rem;
        line-height: 32rem;
        font-weight: 700;
        color: rgba(56, 56, 56, 1);
      }
    }
    .policy_box_1 {
      /* background: url(../../../assets/home/<USER>/
      height: 100vh;
      width: 100%;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      justify-content: center;
      align-items: center;
      .glass {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        /* background: linear-gradient(
        to right,
        rgba(13, 46, 153, 0.8) 0%,
        rgba(70, 128, 255, 0) 100%
      ); */
      }
      .banner-content {
        position: absolute;
        top: calc(308 / 1080 * 100vh);
        left: 160rem;
        font-size: 20rem;
        letter-spacing: 1rem;
        width: 75%;
        color: rgba(255, 255, 255, 1);
        & > .title {
          font-size: 50rem;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .subTitle {
          font-size: 36rem;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .context {
          margin-top: 26rem;
          line-height: 28rem;
          font-size: 18rem;
          font-weight: 300;
        }
      }
      .policy_box_1_carousel {
        height: 100%;
        position: relative;
        .policy_box_1_item {
          height: 100%;
          width: 100%;
          img {
            height: 100%;
            width: 100%;
          }
        }
        .custom-dots {
          display: flex;
          flex-wrap: nowrap;
          position: absolute;
          bottom: 44rem;
          left: 50%;
          transform: translateX(-50%);
        }
        .custom-dots li {
          display: inline-block;
          margin: 0 4rem;
          width: 101rem;
          height: 6rem;
          opacity: 0.5;
          background: rgba(229, 229, 229, 1);
          transition: width 0.3s,
            background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
        }
        .custom-dots li.is-active {
          opacity: 1;
          background: rgba(255, 255, 255, 1);
        }
      }
      /* .policy_box_1_title {
        font-size: 60rem;
        font-weight: 700;
        line-height: 80rem;
        color: #ffffff;
      } */
    }
    .policy_box_2 {
      height: 110vh;
      /* width: 100%;
    padding: 30rem 260rem 0; */
      width: 1600rem;
      margin: 30rem auto 0;
      .treeList {
        /* width: 420rem; */
        width: 26%;
        border: 1rem solid rgba(240, 241, 247, 1);
        overflow: auto;
      }
      .content {
        margin-left: 86rem;
        width: 68%;
        overflow: auto;
        .content_title {
          font-size: 24rem;
          font-weight: 700;
          color: rgba(56, 56, 56, 0.87);
          margin-top: 48rem;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 52rem;
        }
        .content_text {
          margin-top: 28rem;
          width: 100%;
          height: 100%;
          font-size: 14rem;
          font-weight: 400;
          line-height: 18.56rem;
          color: rgba(85, 85, 85, 0.8);
        }
      }
    }
  }
}
@media screen and (max-width: 768px) {
  #policy {
    min-width: 100%;
    :deep(.n-tree) {
      --n-line-height: 2 !important;
      .n-tree-node-wrapper {
        padding: 6px 0;
      }
      .n-tree-node--selected {
        background: rgba(13, 46, 153, 1) !important;
        border: 1rem solid rgba(240, 241, 247, 1);
        padding: 22rem 0;
        .n-tree-node-content__text {
          color: rgba(255, 255, 255, 1);
          font-weight: 500;
          .custom-label {
            color: rgba(255, 255, 255, 1);
            font-weight: 500;
          }
        }
      }
    }
    :deep(.n-tree-node) {
      align-items: center;
    }
    :deep(.n-tree-node-switcher__icon) {
      font-size: 18rem;
      height: 18rem;
      width: 18rem;
    }
    :deep(.n-tree-node-content__text) {
      line-height: 24rem;
      font-size: 18rem;
      font-weight: 500;
      color: rgba(166, 166, 166, 1);
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow: hidden;
      .custom-label {
        font-size: 24rem;
        line-height: 32rem;
        font-weight: 700;
        color: rgba(56, 56, 56, 1);
      }
    }
    .policy_box_1 {
      background: url(../../../assets/home/<USER>
      height: 50vh;
      width: 100%;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .glass {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        background: linear-gradient(
          to right,
          rgba(13, 46, 153, 0.8) 0%,
          rgba(70, 128, 255, 0) 100%
        );
      }
      .banner-content {
        /* position: absolute;
          top: calc(108 / 1080 * 100vh); */
        /* width: calc(708 / 1080 * 100vh); */
        font-size: 20rem;
        letter-spacing: 1rem;
        color: rgba(255, 255, 255, 1);
        width: 720rem;
        margin: 150rem auto 0;
        & > .title {
          font-size: 36rem;
          font-weight: 700;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .subTitle {
          font-size: 36rem;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        & > .context {
          margin-top: 6rem;
          line-height: 28rem;
          font-size: 18rem;
          font-weight: 300;
          :deep(p) {
            font-size: 22rem !important;
            span {
              font-size: 22rem !important;
            }
          }
        }
      }
      .policy_box_1_title {
        /* position: absolute;
        left: 780rem;
        top: 338rem; */
        height: 80rem;
        font-size: 60rem;
        font-weight: 700;
        line-height: 80rem;
        color: #ffffff;
      }
    }
    .policy_box_2 {
      /* height: 110vh; */
      /* width: 100%;
    padding: 30rem 260rem 0; */
      width: 720rem;
      margin: 30rem auto 0;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .container {
        display: flex;
        flex-direction: column;
        height: 200vh;
        .treeList {
          /* width: 420rem; */
          width: 100%;
          height: 70%;
          border: 1rem solid rgba(240, 241, 247, 1);
          overflow: auto;
        }
        .content {
          /* margin-left: 86rem; */
          width: 100%;
          overflow: auto;
          .content_title {
            font-size: 24rem;
            font-weight: 700;
            color: rgba(56, 56, 56, 0.87);
            margin-top: 48rem;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 52rem;
          }
          .content_text {
            margin-top: 28rem;
            width: 100%;
            height: 100%;
            font-size: 14rem;
            font-weight: 400;
            line-height: 18.56rem;
            color: rgba(85, 85, 85, 0.8);
          }
        }
      }
    }
  }
}
</style>
