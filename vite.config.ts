import { fileURLToPath, URL } from "node:url";

import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import { createProxy } from "./build/vite/proxy";
import { wrapperEnv } from "./build/utils";
import { createVitePlugins } from "./build/vite/plugin";
import { resolve } from "path";

function pathResolve(dir: string) {
  return resolve(process.cwd(), ".", dir);
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode, ssrBuild }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);

  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY, VITE_DROP_CONSOLE } =
    viteEnv;
  const isBuild = command === "build";
  return {
    plugins: createVitePlugins(viteEnv, isBuild),
    resolve: {
      alias: [
        {
          find: "vue-i18n",
          replacement: "vue-i18n/dist/vue-i18n.cjs.js",
        },
        // //@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve("src") + "/",
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve("types") + "/",
        },
      ],
    },
    server: {
      https: false,
      // Listening on all local IPs
      host: true,
      port: VITE_PORT,
      // Load proxy configuration from .env
        proxy: {
          "/dev": {
            target: "https://www.fjbdex.com",
            changeOrigin: true,
            ws: true,
            rewrite: (path) => path.replace(new RegExp(`^/dev`), ""),
          },
        },
      //proxy: createProxy(VITE_PROXY),
      open: true, //vite项目启动时自动打开浏览器
    },
    esbuild: {
      drop: VITE_DROP_CONSOLE ? ["console", "debugger"] : ["debugger"],
    },
    base: VITE_PUBLIC_PATH,
    build: {
      // ...其他构建配置...
      terserOptions: {
        compress: {
          drop_console: VITE_DROP_CONSOLE, // 设置为false以保留console.log
        },
      },
    },
  };
});
