// vite.config.ts
import { fileURLToPath, URL } from "node:url";
import { defineConfig } from "file:///H:/%E5%88%9B%E4%BA%9A%E7%A7%91%E6%8A%80Git/%E5%A4%A7%E6%95%B0%E6%8D%AE%E5%AE%98%E7%BD%91/%E5%A4%A7%E6%95%B0%E6%8D%AE%E5%AE%98%E7%BD%91/node_modules/vite/dist/node/index.js";
import vue from "file:///H:/%E5%88%9B%E4%BA%9A%E7%A7%91%E6%8A%80Git/%E5%A4%A7%E6%95%B0%E6%8D%AE%E5%AE%98%E7%BD%91/%E5%A4%A7%E6%95%B0%E6%8D%AE%E5%AE%98%E7%BD%91/node_modules/@vitejs/plugin-vue/dist/index.mjs";
var __vite_injected_original_import_meta_url = "file:///H:/%E5%88%9B%E4%BA%9A%E7%A7%91%E6%8A%80Git/%E5%A4%A7%E6%95%B0%E6%8D%AE%E5%AE%98%E7%BD%91/%E5%A4%A7%E6%95%B0%E6%8D%AE%E5%AE%98%E7%BD%91/vite.config.ts";
var vite_config_default = defineConfig({
  plugins: [
    vue()
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJIOlxcXFxcdTUyMUJcdTRFOUFcdTc5RDFcdTYyODBHaXRcXFxcXHU1OTI3XHU2NTcwXHU2MzZFXHU1Qjk4XHU3RjUxXFxcXFx1NTkyN1x1NjU3MFx1NjM2RVx1NUI5OFx1N0Y1MVwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiSDpcXFxcXHU1MjFCXHU0RTlBXHU3OUQxXHU2MjgwR2l0XFxcXFx1NTkyN1x1NjU3MFx1NjM2RVx1NUI5OFx1N0Y1MVxcXFxcdTU5MjdcdTY1NzBcdTYzNkVcdTVCOThcdTdGNTFcXFxcdml0ZS5jb25maWcudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0g6LyVFNSU4OCU5QiVFNCVCQSU5QSVFNyVBNyU5MSVFNiU4QSU4MEdpdC8lRTUlQTQlQTclRTYlOTUlQjAlRTYlOEQlQUUlRTUlQUUlOTglRTclQkQlOTEvJUU1JUE0JUE3JUU2JTk1JUIwJUU2JThEJUFFJUU1JUFFJTk4JUU3JUJEJTkxL3ZpdGUuY29uZmlnLnRzXCI7aW1wb3J0IHsgZmlsZVVSTFRvUGF0aCwgVVJMIH0gZnJvbSAnbm9kZTp1cmwnXG5cbmltcG9ydCB7IGRlZmluZUNvbmZpZyB9IGZyb20gJ3ZpdGUnXG5pbXBvcnQgdnVlIGZyb20gJ0B2aXRlanMvcGx1Z2luLXZ1ZSdcblxuLy8gaHR0cHM6Ly92aXRlanMuZGV2L2NvbmZpZy9cbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZyh7XG4gIHBsdWdpbnM6IFtcbiAgICB2dWUoKSxcbiAgXSxcbiAgcmVzb2x2ZToge1xuICAgIGFsaWFzOiB7XG4gICAgICAnQCc6IGZpbGVVUkxUb1BhdGgobmV3IFVSTCgnLi9zcmMnLCBpbXBvcnQubWV0YS51cmwpKVxuICAgIH1cbiAgfVxufSlcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFBNFcsU0FBUyxlQUFlLFdBQVc7QUFFL1ksU0FBUyxvQkFBb0I7QUFDN0IsT0FBTyxTQUFTO0FBSDJJLElBQU0sMkNBQTJDO0FBTTVNLElBQU8sc0JBQVEsYUFBYTtBQUFBLEVBQzFCLFNBQVM7QUFBQSxJQUNQLElBQUk7QUFBQSxFQUNOO0FBQUEsRUFDQSxTQUFTO0FBQUEsSUFDUCxPQUFPO0FBQUEsTUFDTCxLQUFLLGNBQWMsSUFBSSxJQUFJLFNBQVMsd0NBQWUsQ0FBQztBQUFBLElBQ3REO0FBQUEsRUFDRjtBQUNGLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
