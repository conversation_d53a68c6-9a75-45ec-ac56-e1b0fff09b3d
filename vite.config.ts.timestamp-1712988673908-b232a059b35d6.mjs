// vite.config.ts
import { defineConfig, loadEnv } from "file:///E:/CYKJ-PROJECT/official-website/node_modules/vite/dist/node/index.js";

// build/vite/proxy.ts
var httpsRE = /^https:\/\//;
function createProxy(list = []) {
  const ret = {};
  for (const [prefix, target] of list) {
    const isHttps = httpsRE.test(target);
    ret[prefix] = {
      target,
      changeOrigin: true,
      ws: true,
      rewrite: (path2) => path2.replace(new RegExp(`^${prefix}`), ""),
      // https is require secure=false
      ...isHttps ? { secure: false } : {}
    };
  }
  return ret;
}

// build/utils.ts
import dotenv from "file:///E:/CYKJ-PROJECT/official-website/node_modules/dotenv/lib/main.js";
function isReportMode() {
  return process.env.REPORT === "true";
}
function wrapperEnv(envConf) {
  const ret = {};
  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, "\n");
    realName = realName === "true" ? true : realName === "false" ? false : realName;
    if (envName === "VITE_PORT") {
      realName = Number(realName);
    }
    if (envName === "VITE_PROXY" && realName) {
      try {
        realName = JSON.parse(realName.replace(/'/g, '"'));
      } catch (error) {
        realName = "";
      }
    }
    ret[envName] = realName;
    if (typeof realName === "string") {
      process.env[envName] = realName;
    } else if (typeof realName === "object") {
      process.env[envName] = JSON.stringify(realName);
    }
  }
  return ret;
}

// build/vite/plugin/index.ts
import vue from "file:///E:/CYKJ-PROJECT/official-website/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///E:/CYKJ-PROJECT/official-website/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import legacy from "file:///E:/CYKJ-PROJECT/official-website/node_modules/@vitejs/plugin-legacy/dist/index.mjs";
import purgeIcons from "file:///E:/CYKJ-PROJECT/official-website/node_modules/vite-plugin-purge-icons/dist/index.mjs";
import windiCSS from "file:///E:/CYKJ-PROJECT/official-website/node_modules/vite-plugin-windicss/dist/index.mjs";

// build/vite/plugin/html.ts
import { createHtmlPlugin } from "file:///E:/CYKJ-PROJECT/official-website/node_modules/vite-plugin-html/dist/index.mjs";

// package.json
var package_default = {
  name: "dsjgw",
  version: "0.0.0",
  private: true,
  type: "module",
  scripts: {
    dev: "vite",
    build: "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts",
    preview: "vite preview",
    "build-only": "vite build",
    "type-check": "vue-tsc --build --force"
  },
  dependencies: {
    axios: "^1.6.8",
    dayjs: "^1.11.10",
    dotenv: "^16.4.5",
    less: "^4.2.0",
    "particles.vue3": "1.43.1",
    pinia: "^2.1.7",
    qs: "^6.12.0",
    swiper: "^11.1.0",
    vue: "^3.4.15",
    "vue-axios": "^3.5.2",
    "vue-router": "^4.2.5",
    "vue-tsc": "^2.0.6"
  },
  devDependencies: {
    "@tsconfig/node20": "^20.1.2",
    "@types/fs-extra": "^11.0.1",
    "@types/node": "^20.11.10",
    "@types/qs": "^6.9.14",
    "@vitejs/plugin-legacy": "^5.3.2",
    "@vitejs/plugin-vue": "^5.0.4",
    "@vitejs/plugin-vue-jsx": "^3.1.0",
    "@vue/tsconfig": "^0.5.1",
    "cross-env": "^7.0.3",
    esno: "^0.16.3",
    "fs-extra": "11.1.1",
    "less-loader": "^12.2.0",
    "naive-ui": "^2.38.1",
    "npm-run-all2": "^6.1.1",
    "rollup-plugin-visualizer": "5.12.0",
    typescript: "~5.3.0",
    vfonts: "^0.0.3",
    vite: "^5.0.11",
    "vite-plugin-compression": "^0.5.1",
    "vite-plugin-html": "^3.2.0",
    "vite-plugin-purge-icons": "^0.10.0",
    "vite-plugin-pwa": "^0.19.0",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-windicss": "^1.9.3"
  }
};

// build/constant.ts
var GLOB_CONFIG_FILE_NAME = "_app.config.js";

// build/vite/plugin/html.ts
function configHtmlPlugin(env, isBuild) {
  const { VITE_GLOB_APP_TITLE, VITE_PUBLIC_PATH } = env;
  const path2 = VITE_PUBLIC_PATH.endsWith("/") ? VITE_PUBLIC_PATH : `${VITE_PUBLIC_PATH}/`;
  const getAppConfigSrc = () => {
    return `${path2 || "/"}${GLOB_CONFIG_FILE_NAME}?v=${package_default.version}-${(/* @__PURE__ */ new Date()).getTime()}`;
  };
  const htmlPlugin = createHtmlPlugin({
    minify: isBuild,
    inject: {
      // Inject data into ejs template
      data: {
        title: VITE_GLOB_APP_TITLE
      },
      // Embed the generated app.config.js file
      tags: isBuild ? [
        {
          tag: "script",
          attrs: {
            src: getAppConfigSrc()
          }
        }
      ] : []
    }
  });
  return htmlPlugin;
}

// build/vite/plugin/pwa.ts
import { VitePWA } from "file:///E:/CYKJ-PROJECT/official-website/node_modules/vite-plugin-pwa/dist/index.js";
function configPwaConfig(env) {
  const { VITE_USE_PWA, VITE_GLOB_APP_TITLE, VITE_GLOB_APP_SHORT_NAME } = env;
  if (VITE_USE_PWA) {
    const pwaPlugin = VitePWA({
      manifest: {
        name: VITE_GLOB_APP_TITLE,
        short_name: VITE_GLOB_APP_SHORT_NAME,
        icons: [
          {
            src: "./resource/img/pwa-192x192.png",
            sizes: "192x192",
            type: "image/png"
          },
          {
            src: "./resource/img/pwa-512x512.png",
            sizes: "512x512",
            type: "image/png"
          }
        ]
      }
    });
    return pwaPlugin;
  }
  return [];
}

// build/vite/plugin/compress.ts
import compressPlugin from "file:///E:/CYKJ-PROJECT/official-website/node_modules/vite-plugin-compression/dist/index.mjs";
function configCompressPlugin(compress, deleteOriginFile = false) {
  const compressList = compress.split(",");
  const plugins = [];
  if (compressList.includes("gzip")) {
    plugins.push(
      compressPlugin({
        ext: ".gz",
        deleteOriginFile
      })
    );
  }
  if (compressList.includes("brotli")) {
    plugins.push(
      compressPlugin({
        ext: ".br",
        algorithm: "brotliCompress",
        deleteOriginFile
      })
    );
  }
  return plugins;
}

// build/vite/plugin/visualizer.ts
import visualizer from "file:///E:/CYKJ-PROJECT/official-website/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
function configVisualizerConfig() {
  if (isReportMode()) {
    return visualizer({
      filename: "./node_modules/.cache/visualizer/stats.html",
      open: true,
      gzipSize: true,
      brotliSize: true
    });
  }
  return [];
}

// build/vite/plugin/svgSprite.ts
import { createSvgIconsPlugin } from "file:///E:/CYKJ-PROJECT/official-website/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import path from "path";
function configSvgIconsPlugin(isBuild) {
  const svgIconsPlugin = createSvgIconsPlugin({
    iconDirs: [path.resolve(process.cwd(), "src/assets/icons")],
    svgoOptions: isBuild,
    // default
    symbolId: "icon-[dir]-[name]"
  });
  return svgIconsPlugin;
}

// build/vite/plugin/index.ts
function createVitePlugins(viteEnv, isBuild) {
  const {
    VITE_USE_IMAGEMIN,
    VITE_LEGACY,
    VITE_BUILD_COMPRESS,
    VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE
  } = viteEnv;
  const vitePlugins = [
    // have to
    vue(),
    // have to
    vueJsx()
    // support name
    // vueSetupExtend(),
    // VitePluginCertificate({
    //   source: 'coding',
    // }),
  ];
  vitePlugins.push(windiCSS());
  VITE_LEGACY && isBuild && vitePlugins.push(legacy());
  vitePlugins.push(configHtmlPlugin(viteEnv, isBuild));
  vitePlugins.push(configSvgIconsPlugin(isBuild));
  vitePlugins.push(purgeIcons());
  vitePlugins.push(configVisualizerConfig());
  if (isBuild) {
    vitePlugins.push(
      configCompressPlugin(VITE_BUILD_COMPRESS, VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE)
    );
    vitePlugins.push(configPwaConfig(viteEnv));
  }
  return vitePlugins;
}

// vite.config.ts
import { resolve } from "path";
function pathResolve(dir) {
  return resolve(process.cwd(), ".", dir);
}
var vite_config_default = defineConfig(({ command, mode, ssrBuild }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY, VITE_DROP_CONSOLE } = viteEnv;
  const isBuild = command === "build";
  return {
    plugins: createVitePlugins(viteEnv, isBuild),
    resolve: {
      alias: [
        {
          find: "vue-i18n",
          replacement: "vue-i18n/dist/vue-i18n.cjs.js"
        },
        // //@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve("src") + "/"
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve("types") + "/"
        }
      ]
    },
    server: {
      https: false,
      // Listening on all local IPs
      host: true,
      port: VITE_PORT,
      // Load proxy configuration from .env
      //   proxy: {
      //     "/dev": {
      //       target: "http://************:8088",
      //       changeOrigin: true,
      //       ws: true,
      //       rewrite: (path) => path.replace(new RegExp(`^/dev`), ""),
      //     },
      //   },
      proxy: createProxy(VITE_PROXY),
      open: true
      //vite项目启动时自动打开浏览器
    },
    esbuild: {
      drop: VITE_DROP_CONSOLE ? ["console", "debugger"] : ["debugger"]
    },
    base: VITE_PUBLIC_PATH,
    build: {
      // ...其他构建配置...
      terserOptions: {
        compress: {
          drop_console: VITE_DROP_CONSOLE
          // 设置为false以保留console.log
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiLCAiYnVpbGQvdml0ZS9wcm94eS50cyIsICJidWlsZC91dGlscy50cyIsICJidWlsZC92aXRlL3BsdWdpbi9pbmRleC50cyIsICJidWlsZC92aXRlL3BsdWdpbi9odG1sLnRzIiwgInBhY2thZ2UuanNvbiIsICJidWlsZC9jb25zdGFudC50cyIsICJidWlsZC92aXRlL3BsdWdpbi9wd2EudHMiLCAiYnVpbGQvdml0ZS9wbHVnaW4vY29tcHJlc3MudHMiLCAiYnVpbGQvdml0ZS9wbHVnaW4vdmlzdWFsaXplci50cyIsICJidWlsZC92aXRlL3BsdWdpbi9zdmdTcHJpdGUudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcQ1lLSi1QUk9KRUNUXFxcXG9mZmljaWFsLXdlYnNpdGVcXFxcdml0ZS5jb25maWcudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6L0NZS0otUFJPSkVDVC9vZmZpY2lhbC13ZWJzaXRlL3ZpdGUuY29uZmlnLnRzXCI7aW1wb3J0IHsgZmlsZVVSTFRvUGF0aCwgVVJMIH0gZnJvbSBcIm5vZGU6dXJsXCI7XHJcblxyXG5pbXBvcnQgeyBkZWZpbmVDb25maWcsIGxvYWRFbnYgfSBmcm9tIFwidml0ZVwiO1xyXG5pbXBvcnQgdnVlIGZyb20gXCJAdml0ZWpzL3BsdWdpbi12dWVcIjtcclxuaW1wb3J0IHsgY3JlYXRlUHJveHkgfSBmcm9tIFwiLi9idWlsZC92aXRlL3Byb3h5XCI7XHJcbmltcG9ydCB7IHdyYXBwZXJFbnYgfSBmcm9tIFwiLi9idWlsZC91dGlsc1wiO1xyXG5pbXBvcnQgeyBjcmVhdGVWaXRlUGx1Z2lucyB9IGZyb20gXCIuL2J1aWxkL3ZpdGUvcGx1Z2luXCI7XHJcbmltcG9ydCB7IHJlc29sdmUgfSBmcm9tIFwicGF0aFwiO1xyXG5cclxuZnVuY3Rpb24gcGF0aFJlc29sdmUoZGlyOiBzdHJpbmcpIHtcclxuICByZXR1cm4gcmVzb2x2ZShwcm9jZXNzLmN3ZCgpLCBcIi5cIiwgZGlyKTtcclxufVxyXG5cclxuLy8gaHR0cHM6Ly92aXRlanMuZGV2L2NvbmZpZy9cclxuZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29uZmlnKCh7IGNvbW1hbmQsIG1vZGUsIHNzckJ1aWxkIH0pID0+IHtcclxuICBjb25zdCByb290ID0gcHJvY2Vzcy5jd2QoKTtcclxuICBjb25zdCBlbnYgPSBsb2FkRW52KG1vZGUsIHJvb3QpO1xyXG4gIGNvbnN0IHZpdGVFbnYgPSB3cmFwcGVyRW52KGVudik7XHJcblxyXG4gIGNvbnN0IHsgVklURV9QT1JULCBWSVRFX1BVQkxJQ19QQVRILCBWSVRFX1BST1hZLCBWSVRFX0RST1BfQ09OU09MRSB9ID1cclxuICAgIHZpdGVFbnY7XHJcbiAgY29uc3QgaXNCdWlsZCA9IGNvbW1hbmQgPT09IFwiYnVpbGRcIjtcclxuICByZXR1cm4ge1xyXG4gICAgcGx1Z2luczogY3JlYXRlVml0ZVBsdWdpbnModml0ZUVudiwgaXNCdWlsZCksXHJcbiAgICByZXNvbHZlOiB7XHJcbiAgICAgIGFsaWFzOiBbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgZmluZDogXCJ2dWUtaTE4blwiLFxyXG4gICAgICAgICAgcmVwbGFjZW1lbnQ6IFwidnVlLWkxOG4vZGlzdC92dWUtaTE4bi5janMuanNcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIC8vIC8vQC94eHh4ID0+IHNyYy94eHh4XHJcbiAgICAgICAge1xyXG4gICAgICAgICAgZmluZDogL1xcL0BcXC8vLFxyXG4gICAgICAgICAgcmVwbGFjZW1lbnQ6IHBhdGhSZXNvbHZlKFwic3JjXCIpICsgXCIvXCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgICAvLyAvIy94eHh4ID0+IHR5cGVzL3h4eHhcclxuICAgICAgICB7XHJcbiAgICAgICAgICBmaW5kOiAvXFwvI1xcLy8sXHJcbiAgICAgICAgICByZXBsYWNlbWVudDogcGF0aFJlc29sdmUoXCJ0eXBlc1wiKSArIFwiL1wiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIF0sXHJcbiAgICB9LFxyXG4gICAgc2VydmVyOiB7XHJcbiAgICAgIGh0dHBzOiBmYWxzZSxcclxuICAgICAgLy8gTGlzdGVuaW5nIG9uIGFsbCBsb2NhbCBJUHNcclxuICAgICAgaG9zdDogdHJ1ZSxcclxuICAgICAgcG9ydDogVklURV9QT1JULFxyXG4gICAgICAvLyBMb2FkIHByb3h5IGNvbmZpZ3VyYXRpb24gZnJvbSAuZW52XHJcbiAgICAgIC8vICAgcHJveHk6IHtcclxuICAgICAgLy8gICAgIFwiL2RldlwiOiB7XHJcbiAgICAgIC8vICAgICAgIHRhcmdldDogXCJodHRwOi8vMTkyLjE2OC4yLjE4OjgwODhcIixcclxuICAgICAgLy8gICAgICAgY2hhbmdlT3JpZ2luOiB0cnVlLFxyXG4gICAgICAvLyAgICAgICB3czogdHJ1ZSxcclxuICAgICAgLy8gICAgICAgcmV3cml0ZTogKHBhdGgpID0+IHBhdGgucmVwbGFjZShuZXcgUmVnRXhwKGBeL2RldmApLCBcIlwiKSxcclxuICAgICAgLy8gICAgIH0sXHJcbiAgICAgIC8vICAgfSxcclxuICAgICAgcHJveHk6IGNyZWF0ZVByb3h5KFZJVEVfUFJPWFkpLFxyXG4gICAgICBvcGVuOiB0cnVlLCAvL3ZpdGVcdTk4NzlcdTc2RUVcdTU0MkZcdTUyQThcdTY1RjZcdTgxRUFcdTUyQThcdTYyNTNcdTVGMDBcdTZENEZcdTg5QzhcdTU2NjhcclxuICAgIH0sXHJcbiAgICBlc2J1aWxkOiB7XHJcbiAgICAgIGRyb3A6IFZJVEVfRFJPUF9DT05TT0xFID8gW1wiY29uc29sZVwiLCBcImRlYnVnZ2VyXCJdIDogW1wiZGVidWdnZXJcIl0sXHJcbiAgICB9LFxyXG4gICAgYmFzZTogVklURV9QVUJMSUNfUEFUSCxcclxuICAgIGJ1aWxkOiB7XHJcbiAgICAgIC8vIC4uLlx1NTE3Nlx1NEVENlx1Njc4NFx1NUVGQVx1OTE0RFx1N0Y2RS4uLlxyXG4gICAgICB0ZXJzZXJPcHRpb25zOiB7XHJcbiAgICAgICAgY29tcHJlc3M6IHtcclxuICAgICAgICAgIGRyb3BfY29uc29sZTogVklURV9EUk9QX0NPTlNPTEUsIC8vIFx1OEJCRVx1N0Y2RVx1NEUzQWZhbHNlXHU0RUU1XHU0RkREXHU3NTU5Y29uc29sZS5sb2dcclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICB9O1xyXG59KTtcclxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVxcXFxidWlsZFxcXFx2aXRlXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVxcXFxidWlsZFxcXFx2aXRlXFxcXHByb3h5LnRzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9FOi9DWUtKLVBST0pFQ1Qvb2ZmaWNpYWwtd2Vic2l0ZS9idWlsZC92aXRlL3Byb3h5LnRzXCI7LyoqXHJcbiAqIFVzZWQgdG8gcGFyc2UgdGhlIC5lbnYuZGV2ZWxvcG1lbnQgcHJveHkgY29uZmlndXJhdGlvblxyXG4gKi9cclxuaW1wb3J0IHR5cGUgeyBQcm94eU9wdGlvbnMgfSBmcm9tICd2aXRlJztcclxuXHJcbnR5cGUgUHJveHlJdGVtID0gW3N0cmluZywgc3RyaW5nXTtcclxuXHJcbnR5cGUgUHJveHlMaXN0ID0gUHJveHlJdGVtW107XHJcblxyXG50eXBlIFByb3h5VGFyZ2V0TGlzdCA9IFJlY29yZDxzdHJpbmcsIFByb3h5T3B0aW9ucz47XHJcblxyXG5jb25zdCBodHRwc1JFID0gL15odHRwczpcXC9cXC8vO1xyXG5cclxuLyoqXHJcbiAqIEdlbmVyYXRlIHByb3h5XHJcbiAqIEBwYXJhbSBsaXN0XHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlUHJveHkobGlzdDogUHJveHlMaXN0ID0gW10pIHtcclxuICBjb25zdCByZXQ6IFByb3h5VGFyZ2V0TGlzdCA9IHt9O1xyXG4gIGZvciAoY29uc3QgW3ByZWZpeCwgdGFyZ2V0XSBvZiBsaXN0KSB7XHJcbiAgICBjb25zdCBpc0h0dHBzID0gaHR0cHNSRS50ZXN0KHRhcmdldCk7XHJcblxyXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2h0dHAtcGFydHkvbm9kZS1odHRwLXByb3h5I29wdGlvbnNcclxuICAgIHJldFtwcmVmaXhdID0ge1xyXG4gICAgICB0YXJnZXQ6IHRhcmdldCxcclxuICAgICAgY2hhbmdlT3JpZ2luOiB0cnVlLFxyXG4gICAgICB3czogdHJ1ZSxcclxuICAgICAgcmV3cml0ZTogKHBhdGgpID0+IHBhdGgucmVwbGFjZShuZXcgUmVnRXhwKGBeJHtwcmVmaXh9YCksICcnKSxcclxuICAgICAgLy8gaHR0cHMgaXMgcmVxdWlyZSBzZWN1cmU9ZmFsc2VcclxuICAgICAgLi4uKGlzSHR0cHMgPyB7IHNlY3VyZTogZmFsc2UgfSA6IHt9KSxcclxuICAgIH07XHJcbiAgfVxyXG4gIHJldHVybiByZXQ7XHJcbn1cclxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVxcXFxidWlsZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcQ1lLSi1QUk9KRUNUXFxcXG9mZmljaWFsLXdlYnNpdGVcXFxcYnVpbGRcXFxcdXRpbHMudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6L0NZS0otUFJPSkVDVC9vZmZpY2lhbC13ZWJzaXRlL2J1aWxkL3V0aWxzLnRzXCI7aW1wb3J0IGZzIGZyb20gJ2ZzJztcclxuaW1wb3J0IHBhdGggZnJvbSAncGF0aCc7XHJcbmltcG9ydCBkb3RlbnYgZnJvbSAnZG90ZW52JztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBpc0RldkZuKG1vZGU6IHN0cmluZyk6IGJvb2xlYW4ge1xyXG4gIHJldHVybiBtb2RlID09PSAnZGV2ZWxvcG1lbnQnO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gaXNQcm9kRm4obW9kZTogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgcmV0dXJuIG1vZGUgPT09ICdwcm9kdWN0aW9uJztcclxufVxyXG5cclxuLyoqXHJcbiAqIFdoZXRoZXIgdG8gZ2VuZXJhdGUgcGFja2FnZSBwcmV2aWV3XHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gaXNSZXBvcnRNb2RlKCk6IGJvb2xlYW4ge1xyXG4gIHJldHVybiBwcm9jZXNzLmVudi5SRVBPUlQgPT09ICd0cnVlJztcclxufVxyXG5cclxuLy8gUmVhZCBhbGwgZW52aXJvbm1lbnQgdmFyaWFibGUgY29uZmlndXJhdGlvbiBmaWxlcyB0byBwcm9jZXNzLmVudlxyXG5leHBvcnQgZnVuY3Rpb24gd3JhcHBlckVudihlbnZDb25mOiBSZWNvcmRhYmxlKTogVml0ZUVudiB7XHJcbiAgY29uc3QgcmV0OiBhbnkgPSB7fTtcclxuXHJcbiAgZm9yIChjb25zdCBlbnZOYW1lIG9mIE9iamVjdC5rZXlzKGVudkNvbmYpKSB7XHJcbiAgICBsZXQgcmVhbE5hbWUgPSBlbnZDb25mW2Vudk5hbWVdLnJlcGxhY2UoL1xcXFxuL2csICdcXG4nKTtcclxuICAgIHJlYWxOYW1lID0gcmVhbE5hbWUgPT09ICd0cnVlJyA/IHRydWUgOiByZWFsTmFtZSA9PT0gJ2ZhbHNlJyA/IGZhbHNlIDogcmVhbE5hbWU7XHJcblxyXG4gICAgaWYgKGVudk5hbWUgPT09ICdWSVRFX1BPUlQnKSB7XHJcbiAgICAgIHJlYWxOYW1lID0gTnVtYmVyKHJlYWxOYW1lKTtcclxuICAgIH1cclxuICAgIGlmIChlbnZOYW1lID09PSAnVklURV9QUk9YWScgJiYgcmVhbE5hbWUpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICByZWFsTmFtZSA9IEpTT04ucGFyc2UocmVhbE5hbWUucmVwbGFjZSgvJy9nLCAnXCInKSk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgcmVhbE5hbWUgPSAnJztcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgcmV0W2Vudk5hbWVdID0gcmVhbE5hbWU7XHJcbiAgICBpZiAodHlwZW9mIHJlYWxOYW1lID09PSAnc3RyaW5nJykge1xyXG4gICAgICBwcm9jZXNzLmVudltlbnZOYW1lXSA9IHJlYWxOYW1lO1xyXG4gICAgfSBlbHNlIGlmICh0eXBlb2YgcmVhbE5hbWUgPT09ICdvYmplY3QnKSB7XHJcbiAgICAgIHByb2Nlc3MuZW52W2Vudk5hbWVdID0gSlNPTi5zdHJpbmdpZnkocmVhbE5hbWUpO1xyXG4gICAgfVxyXG4gIH1cclxuICByZXR1cm4gcmV0O1xyXG59XHJcblxyXG4vKipcclxuICogXHU4M0I3XHU1M0Q2XHU1RjUzXHU1MjREXHU3M0FGXHU1ODgzXHU0RTBCXHU3NTFGXHU2NTQ4XHU3Njg0XHU5MTREXHU3RjZFXHU2NTg3XHU0RUY2XHU1NDBEXHJcbiAqL1xyXG5mdW5jdGlvbiBnZXRDb25mRmlsZXMoKSB7XHJcbiAgY29uc3Qgc2NyaXB0ID0gcHJvY2Vzcy5lbnYubnBtX2xpZmVjeWNsZV9zY3JpcHQ7XHJcbiAgY29uc3QgcmVnID0gbmV3IFJlZ0V4cCgnLS1tb2RlIChbYS16X1xcXFxkXSspJyk7XHJcbiAgY29uc3QgcmVzdWx0ID0gcmVnLmV4ZWMoc2NyaXB0IGFzIHN0cmluZykgYXMgYW55O1xyXG4gIGlmIChyZXN1bHQpIHtcclxuICAgIGNvbnN0IG1vZGUgPSByZXN1bHRbMV0gYXMgc3RyaW5nO1xyXG4gICAgcmV0dXJuIFsnLmVudicsIGAuZW52LiR7bW9kZX1gXTtcclxuICB9XHJcbiAgcmV0dXJuIFsnLmVudicsICcuZW52LnByb2R1Y3Rpb24nXTtcclxufVxyXG5cclxuLyoqXHJcbiAqIEdldCB0aGUgZW52aXJvbm1lbnQgdmFyaWFibGVzIHN0YXJ0aW5nIHdpdGggdGhlIHNwZWNpZmllZCBwcmVmaXhcclxuICogQHBhcmFtIG1hdGNoIHByZWZpeFxyXG4gKiBAcGFyYW0gY29uZkZpbGVzIGV4dFxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGdldEVudkNvbmZpZyhtYXRjaCA9ICdWSVRFX0dMT0JfJywgY29uZkZpbGVzID0gZ2V0Q29uZkZpbGVzKCkpIHtcclxuICBsZXQgZW52Q29uZmlnID0ge307XHJcbiAgY29uZkZpbGVzLmZvckVhY2goKGl0ZW0pID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGVudiA9IGRvdGVudi5wYXJzZShmcy5yZWFkRmlsZVN5bmMocGF0aC5yZXNvbHZlKHByb2Nlc3MuY3dkKCksIGl0ZW0pKSk7XHJcbiAgICAgIGVudkNvbmZpZyA9IHsgLi4uZW52Q29uZmlnLCAuLi5lbnYgfTtcclxuICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgaW4gcGFyc2luZyAke2l0ZW19YCwgZSk7XHJcbiAgICB9XHJcbiAgfSk7XHJcbiAgY29uc3QgcmVnID0gbmV3IFJlZ0V4cChgXigke21hdGNofSlgKTtcclxuICBPYmplY3Qua2V5cyhlbnZDb25maWcpLmZvckVhY2goKGtleSkgPT4ge1xyXG4gICAgaWYgKCFyZWcudGVzdChrZXkpKSB7XHJcbiAgICAgIFJlZmxlY3QuZGVsZXRlUHJvcGVydHkoZW52Q29uZmlnLCBrZXkpO1xyXG4gICAgfVxyXG4gIH0pO1xyXG4gIHJldHVybiBlbnZDb25maWc7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBHZXQgdXNlciByb290IGRpcmVjdG9yeVxyXG4gKiBAcGFyYW0gZGlyIGZpbGUgcGF0aFxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGdldFJvb3RQYXRoKC4uLmRpcjogc3RyaW5nW10pIHtcclxuICByZXR1cm4gcGF0aC5yZXNvbHZlKHByb2Nlc3MuY3dkKCksIC4uLmRpcik7XHJcbn1cclxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVxcXFxidWlsZFxcXFx2aXRlXFxcXHBsdWdpblwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcQ1lLSi1QUk9KRUNUXFxcXG9mZmljaWFsLXdlYnNpdGVcXFxcYnVpbGRcXFxcdml0ZVxcXFxwbHVnaW5cXFxcaW5kZXgudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6L0NZS0otUFJPSkVDVC9vZmZpY2lhbC13ZWJzaXRlL2J1aWxkL3ZpdGUvcGx1Z2luL2luZGV4LnRzXCI7aW1wb3J0IHsgUGx1Z2luT3B0aW9uIH0gZnJvbSAndml0ZSc7XHJcbmltcG9ydCB2dWUgZnJvbSAnQHZpdGVqcy9wbHVnaW4tdnVlJztcclxuaW1wb3J0IHZ1ZUpzeCBmcm9tICdAdml0ZWpzL3BsdWdpbi12dWUtanN4JztcclxuaW1wb3J0IGxlZ2FjeSBmcm9tICdAdml0ZWpzL3BsdWdpbi1sZWdhY3knO1xyXG5pbXBvcnQgcHVyZ2VJY29ucyBmcm9tICd2aXRlLXBsdWdpbi1wdXJnZS1pY29ucyc7XHJcbmltcG9ydCB3aW5kaUNTUyBmcm9tICd2aXRlLXBsdWdpbi13aW5kaWNzcyc7XHJcbi8vIGltcG9ydCBWaXRlUGx1Z2luQ2VydGlmaWNhdGUgZnJvbSAndml0ZS1wbHVnaW4tbWtjZXJ0JztcclxuaW1wb3J0IHsgY29uZmlnSHRtbFBsdWdpbiB9IGZyb20gJy4vaHRtbCc7XHJcbmltcG9ydCB7IGNvbmZpZ1B3YUNvbmZpZyB9IGZyb20gJy4vcHdhJztcclxuXHJcbmltcG9ydCB7IGNvbmZpZ0NvbXByZXNzUGx1Z2luIH0gZnJvbSAnLi9jb21wcmVzcyc7XHJcbi8vIGltcG9ydCB7IGNvbmZpZ1N0eWxlSW1wb3J0UGx1Z2luIH0gZnJvbSAnLi9zdHlsZUltcG9ydCc7XHJcbmltcG9ydCB7IGNvbmZpZ1Zpc3VhbGl6ZXJDb25maWcgfSBmcm9tICcuL3Zpc3VhbGl6ZXInO1xyXG4vLyBpbXBvcnQgeyBjb25maWdUaGVtZVBsdWdpbiB9IGZyb20gJy4vdGhlbWUnO1xyXG4vLyBpbXBvcnQgeyBjb25maWdJbWFnZW1pblBsdWdpbiB9IGZyb20gJy4vaW1hZ2VtaW4nO1xyXG5pbXBvcnQgeyBjb25maWdTdmdJY29uc1BsdWdpbiB9IGZyb20gJy4vc3ZnU3ByaXRlJztcclxuXHJcbi8vIHN1cHBvcnQgTXVsdGktbGFuZ3VhZ2VcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVWaXRlUGx1Z2lucyh2aXRlRW52OiBWaXRlRW52LCBpc0J1aWxkOiBib29sZWFuKSB7XHJcbiAgY29uc3Qge1xyXG4gICAgVklURV9VU0VfSU1BR0VNSU4sXHJcbiAgICBWSVRFX0xFR0FDWSxcclxuICAgIFZJVEVfQlVJTERfQ09NUFJFU1MsXHJcbiAgICBWSVRFX0JVSUxEX0NPTVBSRVNTX0RFTEVURV9PUklHSU5fRklMRSxcclxuICB9ID0gdml0ZUVudjtcclxuICBjb25zdCB2aXRlUGx1Z2luczogKFBsdWdpbk9wdGlvbiB8IFBsdWdpbk9wdGlvbltdKVtdID0gW1xyXG4gICAgLy8gaGF2ZSB0b1xyXG4gICAgdnVlKCksXHJcbiAgICAvLyBoYXZlIHRvXHJcbiAgICB2dWVKc3goKSxcclxuICAgIC8vIHN1cHBvcnQgbmFtZVxyXG4gICAgLy8gdnVlU2V0dXBFeHRlbmQoKSxcclxuICAgIC8vIFZpdGVQbHVnaW5DZXJ0aWZpY2F0ZSh7XHJcbiAgICAvLyAgIHNvdXJjZTogJ2NvZGluZycsXHJcbiAgICAvLyB9KSxcclxuICBdO1xyXG5cclxuICAvLyB2aXRlLXBsdWdpbi13aW5kaWNzc1xyXG4gIHZpdGVQbHVnaW5zLnB1c2god2luZGlDU1MoKSk7XHJcblxyXG4gIC8vIEB2aXRlanMvcGx1Z2luLWxlZ2FjeVxyXG4gIFZJVEVfTEVHQUNZICYmIGlzQnVpbGQgJiYgdml0ZVBsdWdpbnMucHVzaChsZWdhY3koKSk7XHJcblxyXG4gIC8vIHZpdGUtcGx1Z2luLWh0bWxcclxuICB2aXRlUGx1Z2lucy5wdXNoKGNvbmZpZ0h0bWxQbHVnaW4odml0ZUVudiwgaXNCdWlsZCkpO1xyXG5cclxuICAvLyB2aXRlLXBsdWdpbi1zdmctaWNvbnNcclxuICB2aXRlUGx1Z2lucy5wdXNoKGNvbmZpZ1N2Z0ljb25zUGx1Z2luKGlzQnVpbGQpKTtcclxuXHJcbiAgLy8gdml0ZS1wbHVnaW4tcHVyZ2UtaWNvbnNcclxuICB2aXRlUGx1Z2lucy5wdXNoKHB1cmdlSWNvbnMoKSk7XHJcblxyXG4gIC8vIHZpdGUtcGx1Z2luLXN0eWxlLWltcG9ydFxyXG4gIC8vIHZpdGVQbHVnaW5zLnB1c2goY29uZmlnU3R5bGVJbXBvcnRQbHVnaW4oaXNCdWlsZCkpO1xyXG5cclxuICAvLyByb2xsdXAtcGx1Z2luLXZpc3VhbGl6ZXJcclxuICB2aXRlUGx1Z2lucy5wdXNoKGNvbmZpZ1Zpc3VhbGl6ZXJDb25maWcoKSk7XHJcblxyXG4gIC8vIEBraXJrbGluL3ZpdGUtcGx1Z2luLXZiZW4tdGhlbWVcclxuICAvLyB2aXRlUGx1Z2lucy5wdXNoKGNvbmZpZ1RoZW1lUGx1Z2luKGlzQnVpbGQpKTtcclxuXHJcbiAgLy8gVGhlIGZvbGxvd2luZyBwbHVnaW5zIG9ubHkgd29yayBpbiB0aGUgcHJvZHVjdGlvbiBlbnZpcm9ubWVudFxyXG4gIGlmIChpc0J1aWxkKSB7XHJcbiAgICAvLyB2aXRlLXBsdWdpbi1pbWFnZW1pblxyXG4gICAgLy8gVklURV9VU0VfSU1BR0VNSU4gJiYgdml0ZVBsdWdpbnMucHVzaChjb25maWdJbWFnZW1pblBsdWdpbigpKTtcclxuXHJcbiAgICAvLyByb2xsdXAtcGx1Z2luLWd6aXBcclxuICAgIHZpdGVQbHVnaW5zLnB1c2goXHJcbiAgICAgIGNvbmZpZ0NvbXByZXNzUGx1Z2luKFZJVEVfQlVJTERfQ09NUFJFU1MsIFZJVEVfQlVJTERfQ09NUFJFU1NfREVMRVRFX09SSUdJTl9GSUxFKSxcclxuICAgICk7XHJcblxyXG4gICAgLy8gdml0ZS1wbHVnaW4tcHdhXHJcbiAgICB2aXRlUGx1Z2lucy5wdXNoKGNvbmZpZ1B3YUNvbmZpZyh2aXRlRW52KSk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gdml0ZVBsdWdpbnM7XHJcbn1cclxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVxcXFxidWlsZFxcXFx2aXRlXFxcXHBsdWdpblwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcQ1lLSi1QUk9KRUNUXFxcXG9mZmljaWFsLXdlYnNpdGVcXFxcYnVpbGRcXFxcdml0ZVxcXFxwbHVnaW5cXFxcaHRtbC50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRTovQ1lLSi1QUk9KRUNUL29mZmljaWFsLXdlYnNpdGUvYnVpbGQvdml0ZS9wbHVnaW4vaHRtbC50c1wiOy8qKlxyXG4gKiBQbHVnaW4gdG8gbWluaW1pemUgYW5kIHVzZSBlanMgdGVtcGxhdGUgc3ludGF4IGluIGluZGV4Lmh0bWwuXHJcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9hbm5jd2Ivdml0ZS1wbHVnaW4taHRtbFxyXG4gKi9cclxuaW1wb3J0IHR5cGUgeyBQbHVnaW5PcHRpb24gfSBmcm9tICd2aXRlJztcclxuaW1wb3J0IHsgY3JlYXRlSHRtbFBsdWdpbiB9IGZyb20gJ3ZpdGUtcGx1Z2luLWh0bWwnO1xyXG5pbXBvcnQgcGtnIGZyb20gJy4uLy4uLy4uL3BhY2thZ2UuanNvbic7XHJcbmltcG9ydCB7IEdMT0JfQ09ORklHX0ZJTEVfTkFNRSB9IGZyb20gJy4uLy4uL2NvbnN0YW50JztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjb25maWdIdG1sUGx1Z2luKGVudjogVml0ZUVudiwgaXNCdWlsZDogYm9vbGVhbikge1xyXG4gIGNvbnN0IHsgVklURV9HTE9CX0FQUF9USVRMRSwgVklURV9QVUJMSUNfUEFUSCB9ID0gZW52O1xyXG5cclxuICBjb25zdCBwYXRoID0gVklURV9QVUJMSUNfUEFUSC5lbmRzV2l0aCgnLycpID8gVklURV9QVUJMSUNfUEFUSCA6IGAke1ZJVEVfUFVCTElDX1BBVEh9L2A7XHJcblxyXG4gIGNvbnN0IGdldEFwcENvbmZpZ1NyYyA9ICgpID0+IHtcclxuICAgIHJldHVybiBgJHtwYXRoIHx8ICcvJ30ke0dMT0JfQ09ORklHX0ZJTEVfTkFNRX0/dj0ke3BrZy52ZXJzaW9ufS0ke25ldyBEYXRlKCkuZ2V0VGltZSgpfWA7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaHRtbFBsdWdpbjogUGx1Z2luT3B0aW9uW10gPSBjcmVhdGVIdG1sUGx1Z2luKHtcclxuICAgIG1pbmlmeTogaXNCdWlsZCxcclxuICAgIGluamVjdDoge1xyXG4gICAgICAvLyBJbmplY3QgZGF0YSBpbnRvIGVqcyB0ZW1wbGF0ZVxyXG4gICAgICBkYXRhOiB7XHJcbiAgICAgICAgdGl0bGU6IFZJVEVfR0xPQl9BUFBfVElUTEUsXHJcbiAgICAgIH0sXHJcbiAgICAgIC8vIEVtYmVkIHRoZSBnZW5lcmF0ZWQgYXBwLmNvbmZpZy5qcyBmaWxlXHJcbiAgICAgIHRhZ3M6IGlzQnVpbGRcclxuICAgICAgICA/IFtcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIHRhZzogJ3NjcmlwdCcsXHJcbiAgICAgICAgICAgICAgYXR0cnM6IHtcclxuICAgICAgICAgICAgICAgIHNyYzogZ2V0QXBwQ29uZmlnU3JjKCksXHJcbiAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIF1cclxuICAgICAgICA6IFtdLFxyXG4gICAgfSxcclxuICB9KTtcclxuICByZXR1cm4gaHRtbFBsdWdpbjtcclxufVxyXG4iLCAie1xyXG4gIFwibmFtZVwiOiBcImRzamd3XCIsXHJcbiAgXCJ2ZXJzaW9uXCI6IFwiMC4wLjBcIixcclxuICBcInByaXZhdGVcIjogdHJ1ZSxcclxuICBcInR5cGVcIjogXCJtb2R1bGVcIixcclxuICBcInNjcmlwdHNcIjoge1xyXG4gICAgXCJkZXZcIjogXCJ2aXRlXCIsXHJcbiAgICBcImJ1aWxkXCI6IFwiY3Jvc3MtZW52IE5PREVfRU5WPXByb2R1Y3Rpb24gTk9ERV9PUFRJT05TPS0tbWF4LW9sZC1zcGFjZS1zaXplPTgxOTIgdml0ZSBidWlsZCAmJiBlc25vIC4vYnVpbGQvc2NyaXB0L3Bvc3RCdWlsZC50c1wiLFxyXG4gICAgXCJwcmV2aWV3XCI6IFwidml0ZSBwcmV2aWV3XCIsXHJcbiAgICBcImJ1aWxkLW9ubHlcIjogXCJ2aXRlIGJ1aWxkXCIsXHJcbiAgICBcInR5cGUtY2hlY2tcIjogXCJ2dWUtdHNjIC0tYnVpbGQgLS1mb3JjZVwiXHJcbiAgfSxcclxuICBcImRlcGVuZGVuY2llc1wiOiB7XHJcbiAgICBcImF4aW9zXCI6IFwiXjEuNi44XCIsXHJcbiAgICBcImRheWpzXCI6IFwiXjEuMTEuMTBcIixcclxuICAgIFwiZG90ZW52XCI6IFwiXjE2LjQuNVwiLFxyXG4gICAgXCJsZXNzXCI6IFwiXjQuMi4wXCIsXHJcbiAgICBcInBhcnRpY2xlcy52dWUzXCI6IFwiMS40My4xXCIsXHJcbiAgICBcInBpbmlhXCI6IFwiXjIuMS43XCIsXHJcbiAgICBcInFzXCI6IFwiXjYuMTIuMFwiLFxyXG4gICAgXCJzd2lwZXJcIjogXCJeMTEuMS4wXCIsXHJcbiAgICBcInZ1ZVwiOiBcIl4zLjQuMTVcIixcclxuICAgIFwidnVlLWF4aW9zXCI6IFwiXjMuNS4yXCIsXHJcbiAgICBcInZ1ZS1yb3V0ZXJcIjogXCJeNC4yLjVcIixcclxuICAgIFwidnVlLXRzY1wiOiBcIl4yLjAuNlwiXHJcbiAgfSxcclxuICBcImRldkRlcGVuZGVuY2llc1wiOiB7XHJcbiAgICBcIkB0c2NvbmZpZy9ub2RlMjBcIjogXCJeMjAuMS4yXCIsXHJcbiAgICBcIkB0eXBlcy9mcy1leHRyYVwiOiBcIl4xMS4wLjFcIixcclxuICAgIFwiQHR5cGVzL25vZGVcIjogXCJeMjAuMTEuMTBcIixcclxuICAgIFwiQHR5cGVzL3FzXCI6IFwiXjYuOS4xNFwiLFxyXG4gICAgXCJAdml0ZWpzL3BsdWdpbi1sZWdhY3lcIjogXCJeNS4zLjJcIixcclxuICAgIFwiQHZpdGVqcy9wbHVnaW4tdnVlXCI6IFwiXjUuMC40XCIsXHJcbiAgICBcIkB2aXRlanMvcGx1Z2luLXZ1ZS1qc3hcIjogXCJeMy4xLjBcIixcclxuICAgIFwiQHZ1ZS90c2NvbmZpZ1wiOiBcIl4wLjUuMVwiLFxyXG4gICAgXCJjcm9zcy1lbnZcIjogXCJeNy4wLjNcIixcclxuICAgIFwiZXNub1wiOiBcIl4wLjE2LjNcIixcclxuICAgIFwiZnMtZXh0cmFcIjogXCIxMS4xLjFcIixcclxuICAgIFwibGVzcy1sb2FkZXJcIjogXCJeMTIuMi4wXCIsXHJcbiAgICBcIm5haXZlLXVpXCI6IFwiXjIuMzguMVwiLFxyXG4gICAgXCJucG0tcnVuLWFsbDJcIjogXCJeNi4xLjFcIixcclxuICAgIFwicm9sbHVwLXBsdWdpbi12aXN1YWxpemVyXCI6IFwiNS4xMi4wXCIsXHJcbiAgICBcInR5cGVzY3JpcHRcIjogXCJ+NS4zLjBcIixcclxuICAgIFwidmZvbnRzXCI6IFwiXjAuMC4zXCIsXHJcbiAgICBcInZpdGVcIjogXCJeNS4wLjExXCIsXHJcbiAgICBcInZpdGUtcGx1Z2luLWNvbXByZXNzaW9uXCI6IFwiXjAuNS4xXCIsXHJcbiAgICBcInZpdGUtcGx1Z2luLWh0bWxcIjogXCJeMy4yLjBcIixcclxuICAgIFwidml0ZS1wbHVnaW4tcHVyZ2UtaWNvbnNcIjogXCJeMC4xMC4wXCIsXHJcbiAgICBcInZpdGUtcGx1Z2luLXB3YVwiOiBcIl4wLjE5LjBcIixcclxuICAgIFwidml0ZS1wbHVnaW4tc3ZnLWljb25zXCI6IFwiXjIuMC4xXCIsXHJcbiAgICBcInZpdGUtcGx1Z2luLXdpbmRpY3NzXCI6IFwiXjEuOS4zXCJcclxuICB9XHJcbn1cclxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVxcXFxidWlsZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcQ1lLSi1QUk9KRUNUXFxcXG9mZmljaWFsLXdlYnNpdGVcXFxcYnVpbGRcXFxcY29uc3RhbnQudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6L0NZS0otUFJPSkVDVC9vZmZpY2lhbC13ZWJzaXRlL2J1aWxkL2NvbnN0YW50LnRzXCI7LyoqXHJcbiAqIFRoZSBuYW1lIG9mIHRoZSBjb25maWd1cmF0aW9uIGZpbGUgZW50ZXJlZCBpbiB0aGUgcHJvZHVjdGlvbiBlbnZpcm9ubWVudFxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IEdMT0JfQ09ORklHX0ZJTEVfTkFNRSA9ICdfYXBwLmNvbmZpZy5qcyc7XHJcblxyXG5leHBvcnQgY29uc3QgT1VUUFVUX0RJUiA9ICdkaXN0JztcclxuIiwgImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVxcXFxidWlsZFxcXFx2aXRlXFxcXHBsdWdpblwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcQ1lLSi1QUk9KRUNUXFxcXG9mZmljaWFsLXdlYnNpdGVcXFxcYnVpbGRcXFxcdml0ZVxcXFxwbHVnaW5cXFxccHdhLnRzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9FOi9DWUtKLVBST0pFQ1Qvb2ZmaWNpYWwtd2Vic2l0ZS9idWlsZC92aXRlL3BsdWdpbi9wd2EudHNcIjsvKipcclxuICogWmVyby1jb25maWcgUFdBIGZvciBWaXRlXHJcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9hbnRmdS92aXRlLXBsdWdpbi1wd2FcclxuICovXHJcbmltcG9ydCB7IFZpdGVQV0EgfSBmcm9tICd2aXRlLXBsdWdpbi1wd2EnO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGNvbmZpZ1B3YUNvbmZpZyhlbnY6IFZpdGVFbnYpIHtcclxuICBjb25zdCB7IFZJVEVfVVNFX1BXQSwgVklURV9HTE9CX0FQUF9USVRMRSwgVklURV9HTE9CX0FQUF9TSE9SVF9OQU1FIH0gPSBlbnY7XHJcblxyXG4gIGlmIChWSVRFX1VTRV9QV0EpIHtcclxuICAgIC8vIHZpdGUtcGx1Z2luLXB3YVxyXG4gICAgY29uc3QgcHdhUGx1Z2luID0gVml0ZVBXQSh7XHJcbiAgICAgIG1hbmlmZXN0OiB7XHJcbiAgICAgICAgbmFtZTogVklURV9HTE9CX0FQUF9USVRMRSxcclxuICAgICAgICBzaG9ydF9uYW1lOiBWSVRFX0dMT0JfQVBQX1NIT1JUX05BTUUsXHJcbiAgICAgICAgaWNvbnM6IFtcclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgc3JjOiAnLi9yZXNvdXJjZS9pbWcvcHdhLTE5MngxOTIucG5nJyxcclxuICAgICAgICAgICAgc2l6ZXM6ICcxOTJ4MTkyJyxcclxuICAgICAgICAgICAgdHlwZTogJ2ltYWdlL3BuZycsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICBzcmM6ICcuL3Jlc291cmNlL2ltZy9wd2EtNTEyeDUxMi5wbmcnLFxyXG4gICAgICAgICAgICBzaXplczogJzUxMng1MTInLFxyXG4gICAgICAgICAgICB0eXBlOiAnaW1hZ2UvcG5nJyxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgXSxcclxuICAgICAgfSxcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHB3YVBsdWdpbjtcclxuICB9XHJcbiAgcmV0dXJuIFtdO1xyXG59XHJcbiIsICJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRTpcXFxcQ1lLSi1QUk9KRUNUXFxcXG9mZmljaWFsLXdlYnNpdGVcXFxcYnVpbGRcXFxcdml0ZVxcXFxwbHVnaW5cIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkU6XFxcXENZS0otUFJPSkVDVFxcXFxvZmZpY2lhbC13ZWJzaXRlXFxcXGJ1aWxkXFxcXHZpdGVcXFxccGx1Z2luXFxcXGNvbXByZXNzLnRzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9FOi9DWUtKLVBST0pFQ1Qvb2ZmaWNpYWwtd2Vic2l0ZS9idWlsZC92aXRlL3BsdWdpbi9jb21wcmVzcy50c1wiOy8qKlxyXG4gKiBVc2VkIHRvIHBhY2thZ2UgYW5kIG91dHB1dCBnemlwLiBOb3RlIHRoYXQgdGhpcyBkb2VzIG5vdCB3b3JrIHByb3Blcmx5IGluIFZpdGUsIHRoZSBzcGVjaWZpYyByZWFzb24gaXMgc3RpbGwgYmVpbmcgaW52ZXN0aWdhdGVkXHJcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9hbm5jd2Ivdml0ZS1wbHVnaW4tY29tcHJlc3Npb25cclxuICovXHJcbmltcG9ydCB0eXBlIHsgUGx1Z2luT3B0aW9uIH0gZnJvbSAndml0ZSc7XHJcbmltcG9ydCBjb21wcmVzc1BsdWdpbiBmcm9tICd2aXRlLXBsdWdpbi1jb21wcmVzc2lvbic7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gY29uZmlnQ29tcHJlc3NQbHVnaW4oXHJcbiAgY29tcHJlc3M6ICdnemlwJyB8ICdicm90bGknIHwgJ25vbmUnLFxyXG4gIGRlbGV0ZU9yaWdpbkZpbGUgPSBmYWxzZSxcclxuKTogUGx1Z2luT3B0aW9uIHwgUGx1Z2luT3B0aW9uW10ge1xyXG4gIGNvbnN0IGNvbXByZXNzTGlzdCA9IGNvbXByZXNzLnNwbGl0KCcsJyk7XHJcblxyXG4gIGNvbnN0IHBsdWdpbnM6IFBsdWdpbk9wdGlvbltdID0gW107XHJcblxyXG4gIGlmIChjb21wcmVzc0xpc3QuaW5jbHVkZXMoJ2d6aXAnKSkge1xyXG4gICAgcGx1Z2lucy5wdXNoKFxyXG4gICAgICBjb21wcmVzc1BsdWdpbih7XHJcbiAgICAgICAgZXh0OiAnLmd6JyxcclxuICAgICAgICBkZWxldGVPcmlnaW5GaWxlLFxyXG4gICAgICB9KSxcclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBpZiAoY29tcHJlc3NMaXN0LmluY2x1ZGVzKCdicm90bGknKSkge1xyXG4gICAgcGx1Z2lucy5wdXNoKFxyXG4gICAgICBjb21wcmVzc1BsdWdpbih7XHJcbiAgICAgICAgZXh0OiAnLmJyJyxcclxuICAgICAgICBhbGdvcml0aG06ICdicm90bGlDb21wcmVzcycsXHJcbiAgICAgICAgZGVsZXRlT3JpZ2luRmlsZSxcclxuICAgICAgfSksXHJcbiAgICApO1xyXG4gIH1cclxuICByZXR1cm4gcGx1Z2lucztcclxufVxyXG4iLCAiY29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2Rpcm5hbWUgPSBcIkU6XFxcXENZS0otUFJPSkVDVFxcXFxvZmZpY2lhbC13ZWJzaXRlXFxcXGJ1aWxkXFxcXHZpdGVcXFxccGx1Z2luXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJFOlxcXFxDWUtKLVBST0pFQ1RcXFxcb2ZmaWNpYWwtd2Vic2l0ZVxcXFxidWlsZFxcXFx2aXRlXFxcXHBsdWdpblxcXFx2aXN1YWxpemVyLnRzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9FOi9DWUtKLVBST0pFQ1Qvb2ZmaWNpYWwtd2Vic2l0ZS9idWlsZC92aXRlL3BsdWdpbi92aXN1YWxpemVyLnRzXCI7LyoqXHJcbiAqIFBhY2thZ2UgZmlsZSB2b2x1bWUgYW5hbHlzaXNcclxuICovXHJcbmltcG9ydCB2aXN1YWxpemVyIGZyb20gJ3JvbGx1cC1wbHVnaW4tdmlzdWFsaXplcic7XHJcbmltcG9ydCB7IGlzUmVwb3J0TW9kZSB9IGZyb20gJy4uLy4uL3V0aWxzJztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjb25maWdWaXN1YWxpemVyQ29uZmlnKCkge1xyXG4gIGlmIChpc1JlcG9ydE1vZGUoKSkge1xyXG4gICAgcmV0dXJuIHZpc3VhbGl6ZXIoe1xyXG4gICAgICBmaWxlbmFtZTogJy4vbm9kZV9tb2R1bGVzLy5jYWNoZS92aXN1YWxpemVyL3N0YXRzLmh0bWwnLFxyXG4gICAgICBvcGVuOiB0cnVlLFxyXG4gICAgICBnemlwU2l6ZTogdHJ1ZSxcclxuICAgICAgYnJvdGxpU2l6ZTogdHJ1ZSxcclxuICAgIH0pIGFzIFBsdWdpbjtcclxuICB9XHJcbiAgcmV0dXJuIFtdO1xyXG59XHJcbiIsICJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRTpcXFxcQ1lLSi1QUk9KRUNUXFxcXG9mZmljaWFsLXdlYnNpdGVcXFxcYnVpbGRcXFxcdml0ZVxcXFxwbHVnaW5cIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkU6XFxcXENZS0otUFJPSkVDVFxcXFxvZmZpY2lhbC13ZWJzaXRlXFxcXGJ1aWxkXFxcXHZpdGVcXFxccGx1Z2luXFxcXHN2Z1Nwcml0ZS50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRTovQ1lLSi1QUk9KRUNUL29mZmljaWFsLXdlYnNpdGUvYnVpbGQvdml0ZS9wbHVnaW4vc3ZnU3ByaXRlLnRzXCI7LyoqXHJcbiAqICBWaXRlIFBsdWdpbiBmb3IgZmFzdCBjcmVhdGluZyBTVkcgc3ByaXRlcy5cclxuICogaHR0cHM6Ly9naXRodWIuY29tL2FubmN3Yi92aXRlLXBsdWdpbi1zdmctaWNvbnNcclxuICovXHJcblxyXG5pbXBvcnQgeyBjcmVhdGVTdmdJY29uc1BsdWdpbiB9IGZyb20gJ3ZpdGUtcGx1Z2luLXN2Zy1pY29ucyc7XHJcbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGNvbmZpZ1N2Z0ljb25zUGx1Z2luKGlzQnVpbGQ6IGJvb2xlYW4pIHtcclxuICBjb25zdCBzdmdJY29uc1BsdWdpbiA9IGNyZWF0ZVN2Z0ljb25zUGx1Z2luKHtcclxuICAgIGljb25EaXJzOiBbcGF0aC5yZXNvbHZlKHByb2Nlc3MuY3dkKCksICdzcmMvYXNzZXRzL2ljb25zJyldLFxyXG4gICAgc3Znb09wdGlvbnM6IGlzQnVpbGQsXHJcbiAgICAvLyBkZWZhdWx0XHJcbiAgICBzeW1ib2xJZDogJ2ljb24tW2Rpcl0tW25hbWVdJyxcclxuICB9KTtcclxuICByZXR1cm4gc3ZnSWNvbnNQbHVnaW47XHJcbn1cclxuIl0sCiAgIm1hcHBpbmdzIjogIjtBQUVBLFNBQVMsY0FBYyxlQUFlOzs7QUNTdEMsSUFBTSxVQUFVO0FBTVQsU0FBUyxZQUFZLE9BQWtCLENBQUMsR0FBRztBQUNoRCxRQUFNLE1BQXVCLENBQUM7QUFDOUIsYUFBVyxDQUFDLFFBQVEsTUFBTSxLQUFLLE1BQU07QUFDbkMsVUFBTSxVQUFVLFFBQVEsS0FBSyxNQUFNO0FBR25DLFFBQUksTUFBTSxJQUFJO0FBQUEsTUFDWjtBQUFBLE1BQ0EsY0FBYztBQUFBLE1BQ2QsSUFBSTtBQUFBLE1BQ0osU0FBUyxDQUFDQSxVQUFTQSxNQUFLLFFBQVEsSUFBSSxPQUFPLElBQUksTUFBTSxFQUFFLEdBQUcsRUFBRTtBQUFBO0FBQUEsTUFFNUQsR0FBSSxVQUFVLEVBQUUsUUFBUSxNQUFNLElBQUksQ0FBQztBQUFBLElBQ3JDO0FBQUEsRUFDRjtBQUNBLFNBQU87QUFDVDs7O0FDL0JBLE9BQU8sWUFBWTtBQWFaLFNBQVMsZUFBd0I7QUFDdEMsU0FBTyxRQUFRLElBQUksV0FBVztBQUNoQztBQUdPLFNBQVMsV0FBVyxTQUE4QjtBQUN2RCxRQUFNLE1BQVcsQ0FBQztBQUVsQixhQUFXLFdBQVcsT0FBTyxLQUFLLE9BQU8sR0FBRztBQUMxQyxRQUFJLFdBQVcsUUFBUSxPQUFPLEVBQUUsUUFBUSxRQUFRLElBQUk7QUFDcEQsZUFBVyxhQUFhLFNBQVMsT0FBTyxhQUFhLFVBQVUsUUFBUTtBQUV2RSxRQUFJLFlBQVksYUFBYTtBQUMzQixpQkFBVyxPQUFPLFFBQVE7QUFBQSxJQUM1QjtBQUNBLFFBQUksWUFBWSxnQkFBZ0IsVUFBVTtBQUN4QyxVQUFJO0FBQ0YsbUJBQVcsS0FBSyxNQUFNLFNBQVMsUUFBUSxNQUFNLEdBQUcsQ0FBQztBQUFBLE1BQ25ELFNBQVMsT0FBTztBQUNkLG1CQUFXO0FBQUEsTUFDYjtBQUFBLElBQ0Y7QUFDQSxRQUFJLE9BQU8sSUFBSTtBQUNmLFFBQUksT0FBTyxhQUFhLFVBQVU7QUFDaEMsY0FBUSxJQUFJLE9BQU8sSUFBSTtBQUFBLElBQ3pCLFdBQVcsT0FBTyxhQUFhLFVBQVU7QUFDdkMsY0FBUSxJQUFJLE9BQU8sSUFBSSxLQUFLLFVBQVUsUUFBUTtBQUFBLElBQ2hEO0FBQUEsRUFDRjtBQUNBLFNBQU87QUFDVDs7O0FDNUNBLE9BQU8sU0FBUztBQUNoQixPQUFPLFlBQVk7QUFDbkIsT0FBTyxZQUFZO0FBQ25CLE9BQU8sZ0JBQWdCO0FBQ3ZCLE9BQU8sY0FBYzs7O0FDQXJCLFNBQVMsd0JBQXdCOzs7QUNMakM7QUFBQSxFQUNFLE1BQVE7QUFBQSxFQUNSLFNBQVc7QUFBQSxFQUNYLFNBQVc7QUFBQSxFQUNYLE1BQVE7QUFBQSxFQUNSLFNBQVc7QUFBQSxJQUNULEtBQU87QUFBQSxJQUNQLE9BQVM7QUFBQSxJQUNULFNBQVc7QUFBQSxJQUNYLGNBQWM7QUFBQSxJQUNkLGNBQWM7QUFBQSxFQUNoQjtBQUFBLEVBQ0EsY0FBZ0I7QUFBQSxJQUNkLE9BQVM7QUFBQSxJQUNULE9BQVM7QUFBQSxJQUNULFFBQVU7QUFBQSxJQUNWLE1BQVE7QUFBQSxJQUNSLGtCQUFrQjtBQUFBLElBQ2xCLE9BQVM7QUFBQSxJQUNULElBQU07QUFBQSxJQUNOLFFBQVU7QUFBQSxJQUNWLEtBQU87QUFBQSxJQUNQLGFBQWE7QUFBQSxJQUNiLGNBQWM7QUFBQSxJQUNkLFdBQVc7QUFBQSxFQUNiO0FBQUEsRUFDQSxpQkFBbUI7QUFBQSxJQUNqQixvQkFBb0I7QUFBQSxJQUNwQixtQkFBbUI7QUFBQSxJQUNuQixlQUFlO0FBQUEsSUFDZixhQUFhO0FBQUEsSUFDYix5QkFBeUI7QUFBQSxJQUN6QixzQkFBc0I7QUFBQSxJQUN0QiwwQkFBMEI7QUFBQSxJQUMxQixpQkFBaUI7QUFBQSxJQUNqQixhQUFhO0FBQUEsSUFDYixNQUFRO0FBQUEsSUFDUixZQUFZO0FBQUEsSUFDWixlQUFlO0FBQUEsSUFDZixZQUFZO0FBQUEsSUFDWixnQkFBZ0I7QUFBQSxJQUNoQiw0QkFBNEI7QUFBQSxJQUM1QixZQUFjO0FBQUEsSUFDZCxRQUFVO0FBQUEsSUFDVixNQUFRO0FBQUEsSUFDUiwyQkFBMkI7QUFBQSxJQUMzQixvQkFBb0I7QUFBQSxJQUNwQiwyQkFBMkI7QUFBQSxJQUMzQixtQkFBbUI7QUFBQSxJQUNuQix5QkFBeUI7QUFBQSxJQUN6Qix3QkFBd0I7QUFBQSxFQUMxQjtBQUNGOzs7QUNqRE8sSUFBTSx3QkFBd0I7OztBRk05QixTQUFTLGlCQUFpQixLQUFjLFNBQWtCO0FBQy9ELFFBQU0sRUFBRSxxQkFBcUIsaUJBQWlCLElBQUk7QUFFbEQsUUFBTUMsUUFBTyxpQkFBaUIsU0FBUyxHQUFHLElBQUksbUJBQW1CLEdBQUcsZ0JBQWdCO0FBRXBGLFFBQU0sa0JBQWtCLE1BQU07QUFDNUIsV0FBTyxHQUFHQSxTQUFRLEdBQUcsR0FBRyxxQkFBcUIsTUFBTSxnQkFBSSxPQUFPLEtBQUksb0JBQUksS0FBSyxHQUFFLFFBQVEsQ0FBQztBQUFBLEVBQ3hGO0FBRUEsUUFBTSxhQUE2QixpQkFBaUI7QUFBQSxJQUNsRCxRQUFRO0FBQUEsSUFDUixRQUFRO0FBQUE7QUFBQSxNQUVOLE1BQU07QUFBQSxRQUNKLE9BQU87QUFBQSxNQUNUO0FBQUE7QUFBQSxNQUVBLE1BQU0sVUFDRjtBQUFBLFFBQ0U7QUFBQSxVQUNFLEtBQUs7QUFBQSxVQUNMLE9BQU87QUFBQSxZQUNMLEtBQUssZ0JBQWdCO0FBQUEsVUFDdkI7QUFBQSxRQUNGO0FBQUEsTUFDRixJQUNBLENBQUM7QUFBQSxJQUNQO0FBQUEsRUFDRixDQUFDO0FBQ0QsU0FBTztBQUNUOzs7QUduQ0EsU0FBUyxlQUFlO0FBRWpCLFNBQVMsZ0JBQWdCLEtBQWM7QUFDNUMsUUFBTSxFQUFFLGNBQWMscUJBQXFCLHlCQUF5QixJQUFJO0FBRXhFLE1BQUksY0FBYztBQUVoQixVQUFNLFlBQVksUUFBUTtBQUFBLE1BQ3hCLFVBQVU7QUFBQSxRQUNSLE1BQU07QUFBQSxRQUNOLFlBQVk7QUFBQSxRQUNaLE9BQU87QUFBQSxVQUNMO0FBQUEsWUFDRSxLQUFLO0FBQUEsWUFDTCxPQUFPO0FBQUEsWUFDUCxNQUFNO0FBQUEsVUFDUjtBQUFBLFVBQ0E7QUFBQSxZQUNFLEtBQUs7QUFBQSxZQUNMLE9BQU87QUFBQSxZQUNQLE1BQU07QUFBQSxVQUNSO0FBQUEsUUFDRjtBQUFBLE1BQ0Y7QUFBQSxJQUNGLENBQUM7QUFDRCxXQUFPO0FBQUEsRUFDVDtBQUNBLFNBQU8sQ0FBQztBQUNWOzs7QUMzQkEsT0FBTyxvQkFBb0I7QUFFcEIsU0FBUyxxQkFDZCxVQUNBLG1CQUFtQixPQUNZO0FBQy9CLFFBQU0sZUFBZSxTQUFTLE1BQU0sR0FBRztBQUV2QyxRQUFNLFVBQTBCLENBQUM7QUFFakMsTUFBSSxhQUFhLFNBQVMsTUFBTSxHQUFHO0FBQ2pDLFlBQVE7QUFBQSxNQUNOLGVBQWU7QUFBQSxRQUNiLEtBQUs7QUFBQSxRQUNMO0FBQUEsTUFDRixDQUFDO0FBQUEsSUFDSDtBQUFBLEVBQ0Y7QUFFQSxNQUFJLGFBQWEsU0FBUyxRQUFRLEdBQUc7QUFDbkMsWUFBUTtBQUFBLE1BQ04sZUFBZTtBQUFBLFFBQ2IsS0FBSztBQUFBLFFBQ0wsV0FBVztBQUFBLFFBQ1g7QUFBQSxNQUNGLENBQUM7QUFBQSxJQUNIO0FBQUEsRUFDRjtBQUNBLFNBQU87QUFDVDs7O0FDL0JBLE9BQU8sZ0JBQWdCO0FBR2hCLFNBQVMseUJBQXlCO0FBQ3ZDLE1BQUksYUFBYSxHQUFHO0FBQ2xCLFdBQU8sV0FBVztBQUFBLE1BQ2hCLFVBQVU7QUFBQSxNQUNWLE1BQU07QUFBQSxNQUNOLFVBQVU7QUFBQSxNQUNWLFlBQVk7QUFBQSxJQUNkLENBQUM7QUFBQSxFQUNIO0FBQ0EsU0FBTyxDQUFDO0FBQ1Y7OztBQ1hBLFNBQVMsNEJBQTRCO0FBQ3JDLE9BQU8sVUFBVTtBQUVWLFNBQVMscUJBQXFCLFNBQWtCO0FBQ3JELFFBQU0saUJBQWlCLHFCQUFxQjtBQUFBLElBQzFDLFVBQVUsQ0FBQyxLQUFLLFFBQVEsUUFBUSxJQUFJLEdBQUcsa0JBQWtCLENBQUM7QUFBQSxJQUMxRCxhQUFhO0FBQUE7QUFBQSxJQUViLFVBQVU7QUFBQSxFQUNaLENBQUM7QUFDRCxTQUFPO0FBQ1Q7OztBUEdPLFNBQVMsa0JBQWtCLFNBQWtCLFNBQWtCO0FBQ3BFLFFBQU07QUFBQSxJQUNKO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsRUFDRixJQUFJO0FBQ0osUUFBTSxjQUFpRDtBQUFBO0FBQUEsSUFFckQsSUFBSTtBQUFBO0FBQUEsSUFFSixPQUFPO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBLEVBTVQ7QUFHQSxjQUFZLEtBQUssU0FBUyxDQUFDO0FBRzNCLGlCQUFlLFdBQVcsWUFBWSxLQUFLLE9BQU8sQ0FBQztBQUduRCxjQUFZLEtBQUssaUJBQWlCLFNBQVMsT0FBTyxDQUFDO0FBR25ELGNBQVksS0FBSyxxQkFBcUIsT0FBTyxDQUFDO0FBRzlDLGNBQVksS0FBSyxXQUFXLENBQUM7QUFNN0IsY0FBWSxLQUFLLHVCQUF1QixDQUFDO0FBTXpDLE1BQUksU0FBUztBQUtYLGdCQUFZO0FBQUEsTUFDVixxQkFBcUIscUJBQXFCLHNDQUFzQztBQUFBLElBQ2xGO0FBR0EsZ0JBQVksS0FBSyxnQkFBZ0IsT0FBTyxDQUFDO0FBQUEsRUFDM0M7QUFFQSxTQUFPO0FBQ1Q7OztBSHRFQSxTQUFTLGVBQWU7QUFFeEIsU0FBUyxZQUFZLEtBQWE7QUFDaEMsU0FBTyxRQUFRLFFBQVEsSUFBSSxHQUFHLEtBQUssR0FBRztBQUN4QztBQUdBLElBQU8sc0JBQVEsYUFBYSxDQUFDLEVBQUUsU0FBUyxNQUFNLFNBQVMsTUFBTTtBQUMzRCxRQUFNLE9BQU8sUUFBUSxJQUFJO0FBQ3pCLFFBQU0sTUFBTSxRQUFRLE1BQU0sSUFBSTtBQUM5QixRQUFNLFVBQVUsV0FBVyxHQUFHO0FBRTlCLFFBQU0sRUFBRSxXQUFXLGtCQUFrQixZQUFZLGtCQUFrQixJQUNqRTtBQUNGLFFBQU0sVUFBVSxZQUFZO0FBQzVCLFNBQU87QUFBQSxJQUNMLFNBQVMsa0JBQWtCLFNBQVMsT0FBTztBQUFBLElBQzNDLFNBQVM7QUFBQSxNQUNQLE9BQU87QUFBQSxRQUNMO0FBQUEsVUFDRSxNQUFNO0FBQUEsVUFDTixhQUFhO0FBQUEsUUFDZjtBQUFBO0FBQUEsUUFFQTtBQUFBLFVBQ0UsTUFBTTtBQUFBLFVBQ04sYUFBYSxZQUFZLEtBQUssSUFBSTtBQUFBLFFBQ3BDO0FBQUE7QUFBQSxRQUVBO0FBQUEsVUFDRSxNQUFNO0FBQUEsVUFDTixhQUFhLFlBQVksT0FBTyxJQUFJO0FBQUEsUUFDdEM7QUFBQSxNQUNGO0FBQUEsSUFDRjtBQUFBLElBQ0EsUUFBUTtBQUFBLE1BQ04sT0FBTztBQUFBO0FBQUEsTUFFUCxNQUFNO0FBQUEsTUFDTixNQUFNO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEsTUFVTixPQUFPLFlBQVksVUFBVTtBQUFBLE1BQzdCLE1BQU07QUFBQTtBQUFBLElBQ1I7QUFBQSxJQUNBLFNBQVM7QUFBQSxNQUNQLE1BQU0sb0JBQW9CLENBQUMsV0FBVyxVQUFVLElBQUksQ0FBQyxVQUFVO0FBQUEsSUFDakU7QUFBQSxJQUNBLE1BQU07QUFBQSxJQUNOLE9BQU87QUFBQTtBQUFBLE1BRUwsZUFBZTtBQUFBLFFBQ2IsVUFBVTtBQUFBLFVBQ1IsY0FBYztBQUFBO0FBQUEsUUFDaEI7QUFBQSxNQUNGO0FBQUEsSUFDRjtBQUFBLEVBQ0Y7QUFDRixDQUFDOyIsCiAgIm5hbWVzIjogWyJwYXRoIiwgInBhdGgiXQp9Cg==
